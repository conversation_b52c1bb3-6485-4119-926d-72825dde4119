#!/usr/bin/env python3
"""
EU Funds MCP Server - Phase 2 Pipeline Test
Tests the complete content processing pipeline: crawl → process → embed
"""

import asyncio
import logging
from typing import List, Dict, Any

from src.core.config import Settings
from src.core.crawler import EUFundsCrawler
from src.core.text_processor import BulgarianTextProcessor
from src.core.embeddings import EmbeddingProcessor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_phase2_pipeline():
    """Test complete Phase 2 content processing pipeline."""
    logger.info("🧪 Testing Phase 2 Pipeline...")
    
    # Initialize settings
    settings = Settings()
    logger.info(f"📋 Configuration loaded: {settings.embedding_model}")
    
    # Initialize components
    crawler = EUFundsCrawler()
    text_processor = BulgarianTextProcessor()
    embedding_processor = EmbeddingProcessor()
    
    try:
        # Test URL (EU Funds Bulgaria - should have Bulgarian content)
        test_url = "https://eufunds.bg/"
        logger.info(f"🌐 Testing with URL: {test_url}")
        
        # Step 1: Crawl content
        logger.info("📥 Step 1: Crawling content...")
        crawl_result = await crawler.crawl_url(test_url)
        
        if not crawl_result or not crawl_result.success:
            logger.error("❌ Crawling failed")
            return False
            
        logger.info(f"✅ Crawled {len(crawl_result.content)} characters")
        logger.info(f"   Language detected: {crawl_result.metadata.language}")
        logger.info(f"   Quality score: {crawl_result.metadata.quality_score:.2f}")
        
        # Step 2: Process text
        logger.info("🔤 Step 2: Processing Bulgarian text...")
        processed_result = await text_processor.process_text(
            crawl_result.content,
            source_url=test_url
        )
        
        if not processed_result or not processed_result.chunks:
            logger.error("❌ Text processing failed")
            return False
            
        logger.info(f"✅ Processed into {len(processed_result.chunks)} chunks")
        logger.info(f"   Language metrics: {processed_result.language_metrics.bulgarian_ratio:.2f} Bulgarian ratio")
        logger.info(f"   Funding terms found: {len(processed_result.language_metrics.funding_terms)}")
        
        # Step 3: Generate embeddings
        logger.info("🧠 Step 3: Generating embeddings...")
        
        # Test with first few chunks to avoid overloading
        test_chunks = processed_result.chunks[:3]
        chunk_texts = [chunk.text for chunk in test_chunks]
        
        batch_result = await embedding_processor.embed_batch(
            chunk_texts,
            is_query=False,
            use_bulgarian_prefix=True
        )
        
        if not batch_result or batch_result.success_rate < 1.0:
            logger.error("❌ Embedding generation failed")
            return False
            
        logger.info(f"✅ Generated {len(batch_result.embeddings)} embeddings")
        logger.info(f"   Success rate: {batch_result.success_rate:.2f}")
        logger.info(f"   Average processing time: {batch_result.avg_time_per_embedding:.3f}s")
        
        # Step 4: Test query embedding
        logger.info("🔍 Step 4: Testing query embedding...")
        
        query_text = "Как да кандидатствам за европейско финансиране?"
        query_result = await embedding_processor.embed_text(
            query_text,
            is_query=True,
            use_bulgarian_prefix=True
        )
        
        if not query_result:
            logger.error("❌ Query embedding failed")
            return False
            
        logger.info(f"✅ Query embedding generated: {len(query_result.vector)}D")
        logger.info(f"   Language: {query_result.metadata.language}")
        logger.info(f"   Processing time: {query_result.metadata.processing_time:.3f}s")
        
        # Step 5: Test similarity (basic cosine similarity)
        logger.info("📊 Step 5: Testing similarity calculation...")
        
        import numpy as np
        
        query_vector = np.array(query_result.vector)
        similarities = []
        
        for i, embedding in enumerate(batch_result.embeddings):
            if embedding:
                doc_vector = np.array(embedding.vector)
                similarity = np.dot(query_vector, doc_vector) / (
                    np.linalg.norm(query_vector) * np.linalg.norm(doc_vector)
                )
                similarities.append((i, similarity))
        
        # Sort by similarity
        similarities.sort(key=lambda x: x[1], reverse=True)
        
        logger.info("✅ Similarity scores calculated:")
        for i, (chunk_idx, score) in enumerate(similarities[:3]):
            chunk_preview = test_chunks[chunk_idx].text[:100] + "..."
            logger.info(f"   Rank {i+1}: Score {score:.3f} - {chunk_preview}")
        
        # Pipeline summary
        logger.info("📋 Pipeline Summary:")
        logger.info(f"   ✅ Crawling: {len(crawl_result.content)} chars, quality {crawl_result.metadata.quality_score:.2f}")
        logger.info(f"   ✅ Processing: {len(processed_result.chunks)} chunks, {processed_result.language_metrics.bulgarian_ratio:.2f} BG ratio")
        logger.info(f"   ✅ Embeddings: {len(batch_result.embeddings)} vectors, {batch_result.success_rate:.2f} success rate")
        logger.info(f"   ✅ Query: {len(query_result.vector)}D vector, {query_result.metadata.processing_time:.3f}s")
        logger.info(f"   ✅ Similarity: Best match score {similarities[0][1]:.3f}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Pipeline test failed: {e}")
        return False
        
    finally:
        # Cleanup
        await crawler.cleanup()
        await embedding_processor.cleanup()
        logger.info("🧹 Pipeline cleanup complete")

async def main():
    """Main test function."""
    success = await test_phase2_pipeline()
    
    if success:
        print("\n🎉 Phase 2 Pipeline Test PASSED!")
        print("✅ All components working correctly:")
        print("   - Web crawling with Crawl4AI")
        print("   - Bulgarian text processing")
        print("   - Multilingual embedding generation")
        print("   - Query processing and similarity")
    else:
        print("\n❌ Phase 2 Pipeline Test FAILED!")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
