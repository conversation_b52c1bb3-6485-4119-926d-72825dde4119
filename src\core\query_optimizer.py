#!/usr/bin/env python3
"""
Query Classification and Named Entity Recognition for EU Funds RAG System.

Based on 2024-2025 best practices for RAG optimization:
- Query type classification for targeted search
- EU funding program entity recognition
- Metadata filtering for improved relevance
- Program-specific optimization rules

References:
- Medium.com/@piash.tanjin "Optimizing RAG Systems: Query Classification with Metadata & Vector Search" (2025)
- ECAI 2024 "Few-shot Prompt Optimization for Named Entity Recognition"
- EU Funding Instruments taxonomy (EUSDR, 2024)
"""

import re
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class QueryType(Enum):
    """Types of queries for EU funding information."""
    PROGRAM = "program"          # Questions about specific programs
    FUNDING = "funding"          # Questions about funding amounts, types
    CONDITIONS = "conditions"    # Questions about requirements, criteria
    SME = "sme"                 # Questions about small/medium enterprises
    GENERAL = "general"         # General questions
    UNKNOWN = "unknown"         # Unclassified queries

@dataclass
class EUProgram:
    """EU funding program entity."""
    name: str
    type: str  # "eu_program", "bulgarian_program", "fund"
    category: str  # "research", "regional", "social", "cohesion"
    aliases: List[str]
    description: str

@dataclass
class QueryAnalysis:
    """Result of query analysis."""
    query_type: QueryType
    detected_programs: List[EUProgram]
    entities: Dict[str, List[str]]
    confidence: float
    metadata_filters: Dict[str, Any]
    optimization_hints: List[str]

class EUProgramsDatabase:
    """Database of EU funding programs and their aliases."""
    
    def __init__(self):
        self.programs = self._initialize_programs()
        self.program_patterns = self._compile_patterns()
    
    def _initialize_programs(self) -> List[EUProgram]:
        """Initialize database of EU funding programs."""
        return [
            # Major EU Programs
            EUProgram(
                name="Horizon Europe",
                type="eu_program",
                category="research",
                aliases=["horizon", "horizon europe", "хоризонт европа", "хоризонт"],
                description="EU's research and innovation programme"
            ),
            EUProgram(
                name="European Regional Development Fund",
                type="fund",
                category="regional",
                aliases=["ERDF", "ЕФРР", "регионален фонд", "regional development"],
                description="EU fund for regional development"
            ),
            EUProgram(
                name="European Social Fund Plus",
                type="fund",
                category="social",
                aliases=["ESF+", "ESF", "ЕСФ", "социален фонд", "social fund"],
                description="EU fund for employment and social inclusion"
            ),
            EUProgram(
                name="Cohesion Fund",
                type="fund",
                category="cohesion",
                aliases=["CF", "ФК", "кохезионен фонд", "cohesion"],
                description="EU fund for environmental and transport infrastructure"
            ),
            
            # Bulgarian Operational Programmes
            EUProgram(
                name="ОПИК",
                type="bulgarian_program",
                category="competitiveness",
                aliases=["ОПИК", "OPIC", "иновации", "конкурентоспособност"],
                description="Оперативна програма Иновации и конкурентоспособност"
            ),
            EUProgram(
                name="ОПРР",
                type="bulgarian_program", 
                category="regional",
                aliases=["ОПРР", "OPRR", "регионално развитие"],
                description="Оперативна програма Регионално развитие"
            ),
            EUProgram(
                name="ОПНОИР",
                type="bulgarian_program",
                category="research",
                aliases=["ОПНОИР", "OPNOIR", "наука", "образование", "иновации"],
                description="Оперативна програма Наука и образование за интелигентен растеж"
            ),
            EUProgram(
                name="ОПРЧР",
                type="bulgarian_program",
                category="human_resources",
                aliases=["ОПРЧР", "OPRHR", "човешки ресурси"],
                description="Оперативна програма Развитие на човешките ресурси"
            )
        ]
    
    def _compile_patterns(self) -> Dict[str, re.Pattern]:
        """Compile regex patterns for program detection."""
        patterns = {}
        for program in self.programs:
            # Create pattern from all aliases
            aliases_pattern = "|".join(re.escape(alias.lower()) for alias in program.aliases)
            patterns[program.name] = re.compile(f"({aliases_pattern})", re.IGNORECASE)
        return patterns
    
    def find_programs(self, text: str) -> List[EUProgram]:
        """Find EU programs mentioned in text."""
        found_programs = []
        text_lower = text.lower()
        
        for program in self.programs:
            pattern = self.program_patterns[program.name]
            if pattern.search(text_lower):
                found_programs.append(program)
        
        return found_programs

class QueryClassifier:
    """Classifies queries into types for optimized search."""
    
    def __init__(self):
        self.query_patterns = self._initialize_patterns()
    
    def _initialize_patterns(self) -> Dict[QueryType, List[re.Pattern]]:
        """Initialize query classification patterns."""
        return {
            QueryType.PROGRAM: [
                re.compile(r"(какви програми|какви схеми|кои програми|програма|схема)", re.IGNORECASE),
                re.compile(r"(what programs|which programs|program|scheme|initiative)", re.IGNORECASE),
                re.compile(r"(споменават|налични|достъпни|предлагат)", re.IGNORECASE)
            ],
            QueryType.FUNDING: [
                re.compile(r"(финансиране|средства|пари|сума|бюджет)", re.IGNORECASE),
                re.compile(r"(funding|money|amount|budget|grant|loan)", re.IGNORECASE),
                re.compile(r"(колко|размер|максимум|минимум)", re.IGNORECASE)
            ],
            QueryType.CONDITIONS: [
                re.compile(r"(условия|изисквания|критерии|правила)", re.IGNORECASE),
                re.compile(r"(conditions|requirements|criteria|rules|eligibility)", re.IGNORECASE),
                re.compile(r"(как да|как мога|необходимо|трябва)", re.IGNORECASE)
            ],
            QueryType.SME: [
                re.compile(r"(МСП|малки предприятия|средни предприятия)", re.IGNORECASE),
                re.compile(r"(SME|small business|medium enterprise|startup)", re.IGNORECASE),
                re.compile(r"(микро предприятия|предприемачи)", re.IGNORECASE)
            ]
        }
    
    def classify(self, query: str) -> Tuple[QueryType, float]:
        """Classify query type with confidence score."""
        scores = {query_type: 0 for query_type in QueryType}
        
        for query_type, patterns in self.query_patterns.items():
            for pattern in patterns:
                matches = len(pattern.findall(query))
                scores[query_type] += matches
        
        # Find best match
        best_type = max(scores, key=scores.get)
        max_score = scores[best_type]
        
        if max_score == 0:
            return QueryType.UNKNOWN, 0.0
        
        # Calculate confidence (normalize by query length)
        confidence = min(max_score / (len(query.split()) / 5), 1.0)
        
        return best_type, confidence

class QueryOptimizer:
    """Main query optimization class combining classification and NER."""
    
    def __init__(self):
        self.programs_db = EUProgramsDatabase()
        self.classifier = QueryClassifier()
        logger.info("Query optimizer initialized with EU programs database")
    
    def analyze_query(self, query: str) -> QueryAnalysis:
        """Analyze query and provide optimization recommendations."""
        try:
            # Classify query type
            query_type, confidence = self.classifier.classify(query)
            
            # Find EU programs
            detected_programs = self.programs_db.find_programs(query)
            
            # Extract other entities
            entities = self._extract_entities(query)
            
            # Generate metadata filters
            metadata_filters = self._generate_metadata_filters(query_type, detected_programs)
            
            # Generate optimization hints
            optimization_hints = self._generate_optimization_hints(query_type, detected_programs, entities)
            
            return QueryAnalysis(
                query_type=query_type,
                detected_programs=detected_programs,
                entities=entities,
                confidence=confidence,
                metadata_filters=metadata_filters,
                optimization_hints=optimization_hints
            )
            
        except Exception as e:
            logger.error(f"Query analysis failed: {e}")
            return QueryAnalysis(
                query_type=QueryType.UNKNOWN,
                detected_programs=[],
                entities={},
                confidence=0.0,
                metadata_filters={},
                optimization_hints=[]
            )
    
    def _extract_entities(self, query: str) -> Dict[str, List[str]]:
        """Extract named entities from query."""
        entities = {
            "amounts": [],
            "dates": [],
            "locations": [],
            "organizations": []
        }
        
        # Extract amounts (simple pattern matching)
        amount_pattern = re.compile(r"(\d+(?:\.\d+)?)\s*(лв|евро|euro|€|лева)", re.IGNORECASE)
        entities["amounts"] = [match.group() for match in amount_pattern.finditer(query)]
        
        # Extract years/dates
        date_pattern = re.compile(r"(20\d{2}|202[0-9])")
        entities["dates"] = [match.group() for match in date_pattern.finditer(query)]
        
        # Extract Bulgarian locations
        location_pattern = re.compile(r"(софия|пловдив|варна|бургас|русе|стара загора|плевен)", re.IGNORECASE)
        entities["locations"] = [match.group() for match in location_pattern.finditer(query)]
        
        return entities
    
    def _generate_metadata_filters(self, query_type: QueryType, programs: List[EUProgram]) -> Dict[str, Any]:
        """Generate metadata filters for targeted search."""
        filters = {}
        
        # Filter by content type based on query type
        if query_type == QueryType.PROGRAM:
            filters["content_type"] = ["program_description", "program_overview"]
        elif query_type == QueryType.FUNDING:
            filters["content_type"] = ["funding_info", "budget_details"]
        elif query_type == QueryType.CONDITIONS:
            filters["content_type"] = ["eligibility", "requirements", "criteria"]
        elif query_type == QueryType.SME:
            filters["content_type"] = ["sme_specific", "small_business"]
        
        # Filter by program category if specific programs detected
        if programs:
            categories = list(set(program.category for program in programs))
            filters["program_category"] = categories
        
        return filters
    
    def _generate_optimization_hints(self, query_type: QueryType, programs: List[EUProgram], entities: Dict[str, List[str]]) -> List[str]:
        """Generate optimization hints for search improvement."""
        hints = []
        
        # Query type specific hints
        if query_type == QueryType.PROGRAM:
            hints.append("Focus on program descriptions and overviews")
            hints.append("Prioritize official program documentation")
        elif query_type == QueryType.FUNDING:
            hints.append("Look for budget and funding amount information")
            hints.append("Include financial details and grant sizes")
        elif query_type == QueryType.CONDITIONS:
            hints.append("Search for eligibility criteria and requirements")
            hints.append("Include application procedures and deadlines")
        elif query_type == QueryType.SME:
            hints.append("Focus on small and medium enterprise specific content")
            hints.append("Prioritize business support measures")
        
        # Program specific hints
        if programs:
            program_names = [p.name for p in programs]
            hints.append(f"Target specific programs: {', '.join(program_names)}")
        
        # Entity specific hints
        if entities["amounts"]:
            hints.append("Include financial amount context in search")
        if entities["dates"]:
            hints.append("Consider temporal relevance for search results")
        if entities["locations"]:
            hints.append("Include geographical context in search")
        
        return hints
