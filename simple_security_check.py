#!/usr/bin/env python3
"""
Simple security check for real API keys and secrets before GitHub upload.
"""

import os
import re
import glob
from typing import List, Dict, <PERSON><PERSON>

def check_for_real_secrets():
    """Check for actual API keys and secrets."""
    print("🔒 CHECKING FOR REAL API KEYS AND SECRETS")
    print("=" * 50)
    
    # Patterns for real secrets
    real_secret_patterns = {
        'openai_keys': r'sk-[a-zA-Z0-9]{48}',
        'supabase_keys': r'eyJ[a-zA-Z0-9_-]{100,}',
        'actual_urls': r'https://[a-zA-Z0-9]+\.supabase\.co',
        'cohere_keys': r'[a-zA-Z0-9-]{40,60}(?=\s|$|"|\')(?!.*your_.*_key)',
    }
    
    # Files to check
    files_to_check = []
    for pattern in ['**/*.py', '**/*.env*', '**/*.json', '**/*.md', '**/*.txt']:
        files_to_check.extend(glob.glob(pattern, recursive=True))
    
    # Exclude safe files
    exclude_patterns = [
        '__pycache__', '.git', 'node_modules', 'htmlcov',
        '.env.example', 'security_check.py', 'simple_security_check.py'
    ]
    
    filtered_files = []
    for filepath in files_to_check:
        if not any(exclude in filepath for exclude in exclude_patterns):
            filtered_files.append(filepath)
    
    issues_found = []
    
    for filepath in filtered_files:
        try:
            with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                
            for secret_type, pattern in real_secret_patterns.items():
                matches = re.finditer(pattern, content)
                for match in matches:
                    # Skip template placeholders
                    if 'your_' in match.group().lower() or 'example' in match.group().lower():
                        continue
                    
                    line_num = content[:match.start()].count('\n') + 1
                    issues_found.append((filepath, secret_type, line_num, match.group()[:20] + '...'))
                    
        except Exception as e:
            print(f"Error reading {filepath}: {e}")
    
    if issues_found:
        print("❌ REAL SECRETS FOUND - DO NOT UPLOAD!")
        for filepath, secret_type, line_num, preview in issues_found:
            print(f"   {filepath}:{line_num} - {secret_type}: {preview}")
        return False
    else:
        print("✅ NO REAL SECRETS DETECTED")
        return True

def create_gitignore():
    """Create comprehensive .gitignore."""
    gitignore_content = """# Environment variables and secrets
.env
.env.local
.env.production
.env.staging
*.key
*.secret
config/secrets.json

# Database files
*.db
*.sqlite
*.sqlite3

# Logs
logs/
*.log

# Cache and temporary files
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
*.so
*.tmp
*.temp
temp/

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Node modules
node_modules/

# Build artifacts
build/
dist/
*.egg-info/

# Test coverage
.coverage
htmlcov/
.pytest_cache/

# Crawl reports (contain timestamps)
crawl_report_*.json

# MCP logs
mcp_*.log
"""
    
    with open('.gitignore', 'w') as f:
        f.write(gitignore_content)
    print("✅ Created .gitignore")

def create_env_example():
    """Create .env.example template."""
    env_example = """# EU Funds MCP Server Configuration
# Copy this file to .env and fill in your actual values

# OpenAI API Key for embeddings
OPENAI_API_KEY=your_openai_key_here

# Supabase Configuration
SUPABASE_URL=your_supabase_url_here
SUPABASE_KEY=your_supabase_key_here

# Cohere API Key for reranking
COHERE_API_KEY=your_cohere_key_here

# Optional: Logging level
LOG_LEVEL=INFO

# Optional: Custom settings
CRAWL_DEPTH=2
SIMILARITY_THRESHOLD=0.3
"""
    
    with open('.env.example', 'w') as f:
        f.write(env_example)
    print("✅ Created .env.example")

def main():
    """Main security check."""
    print("🔒 GITHUB UPLOAD SECURITY CHECK")
    print("=" * 40)
    
    # Check for real secrets
    is_safe = check_for_real_secrets()
    
    # Create security files
    create_gitignore()
    create_env_example()
    
    print("\n📋 SECURITY CHECKLIST:")
    print("✅ .gitignore created")
    print("✅ .env.example created")
    print("✅ Real API keys checked")
    
    if is_safe:
        print("\n🚀 PROJECT IS SAFE FOR GITHUB UPLOAD!")
        print("\n📝 FINAL STEPS:")
        print("1. Review all files one more time")
        print("2. Ensure .env is in .gitignore")
        print("3. Test with a private repo first")
        print("4. Upload to GitHub")
        return True
    else:
        print("\n❌ FIX SECURITY ISSUES BEFORE UPLOAD!")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
