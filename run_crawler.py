#!/usr/bin/env python3
"""
Script to run the EU Funds crawler with improved HTML cleaning.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.crawler.eu_funds_crawler import EUFundsCrawler

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('crawler.log')
    ]
)

logger = logging.getLogger(__name__)

async def main():
    """Run the EU Funds crawler."""
    print("🚀 Starting EU Funds Crawler for Bulgarian content...")
    print("📋 Configuration:")
    print("   - Depth: 1 (one level deep for more data)")
    print("   - Target: https://eufunds.bg/bg")
    print("   - HTML Cleaning: HtmlRAG-inspired approach")
    print("   - Language: Bulgarian optimization")
    print("   - Plan: Free Supabase - controlled data expansion")
    print()
    
    try:
        # Initialize crawler
        crawler = EUFundsCrawler()
        
        # Run Bulgarian page crawling
        print("🔍 Starting Bulgarian page crawling process...")
        results = await crawler.crawl_bulgarian_main_page()
        
        print(f"\n✅ Bulgarian page crawling completed successfully!")
        print(f"📊 Results summary:")

        if isinstance(results, dict):
            # Handle dictionary result format
            stats = results.get('crawl_statistics', {})
            print(f"   - Pages processed: {stats.get('pages_processed', 0)}")
            print(f"   - Content stored: {stats.get('content_stored', 0)}")
            print(f"   - Errors: {stats.get('errors', 0)}")
        else:
            print(f"   - Results: {results}")
        
        print(f"\n🎯 Ready for accuracy testing!")
        
    except Exception as e:
        logger.error(f"❌ Crawler failed: {e}")
        print(f"❌ Crawler failed: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = asyncio.run(main())
    if success:
        print("\n🎉 Crawler completed successfully!")
    else:
        print("\n❌ Crawler failed. Check logs for details.")
