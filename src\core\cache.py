"""
Intelligent caching system for EU Funds MCP Server.
Implements memory-based caching for frequent queries and embeddings.
"""

import asyncio
import hashlib
import json
import time
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timedelta
import logging

from src.core.models import SearchResult, EmbeddingResult

logger = logging.getLogger(__name__)


class IntelligentCache:
    """
    Memory-based intelligent cache for search results and embeddings.
    Optimized for Bulgarian EU funds content with TTL and LRU eviction.
    """
    
    def __init__(
        self,
        max_size: int = 1000,
        default_ttl: int = 3600,  # 1 hour
        embedding_ttl: int = 86400,  # 24 hours
        search_ttl: int = 1800,  # 30 minutes
    ):
        """
        Initialize intelligent cache.
        
        Args:
            max_size: Maximum number of cached items
            default_ttl: Default TTL in seconds
            embedding_ttl: TTL for embedding cache in seconds
            search_ttl: TTL for search results in seconds
        """
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.embedding_ttl = embedding_ttl
        self.search_ttl = search_ttl
        
        # Cache storage
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._access_times: Dict[str, float] = {}
        self._lock = asyncio.Lock()
        
        # Statistics
        self.stats = {
            "hits": 0,
            "misses": 0,
            "evictions": 0,
            "total_requests": 0
        }
        
        logger.info("🧠 Intelligent cache initialized")
    
    def _generate_key(self, prefix: str, data: Union[str, Dict, List]) -> str:
        """Generate cache key from data."""
        if isinstance(data, str):
            content = data
        else:
            content = json.dumps(data, sort_keys=True, ensure_ascii=False)
        
        # Create hash for Bulgarian content
        hash_obj = hashlib.sha256(content.encode('utf-8'))
        return f"{prefix}:{hash_obj.hexdigest()[:16]}"
    
    async def get(self, key: str) -> Optional[Any]:
        """Get item from cache."""
        async with self._lock:
            self.stats["total_requests"] += 1
            
            if key not in self._cache:
                self.stats["misses"] += 1
                return None
            
            item = self._cache[key]
            
            # Check TTL
            if time.time() > item["expires_at"]:
                del self._cache[key]
                del self._access_times[key]
                self.stats["misses"] += 1
                return None
            
            # Update access time for LRU
            self._access_times[key] = time.time()
            self.stats["hits"] += 1
            
            logger.debug(f"🎯 Cache hit for key: {key[:20]}...")
            return item["data"]
    
    async def set(
        self, 
        key: str, 
        data: Any, 
        ttl: Optional[int] = None
    ) -> None:
        """Set item in cache."""
        async with self._lock:
            if ttl is None:
                ttl = self.default_ttl
            
            # Evict if at max size
            if len(self._cache) >= self.max_size:
                await self._evict_lru()
            
            expires_at = time.time() + ttl
            self._cache[key] = {
                "data": data,
                "expires_at": expires_at,
                "created_at": time.time()
            }
            self._access_times[key] = time.time()
            
            logger.debug(f"💾 Cached item with key: {key[:20]}... (TTL: {ttl}s)")
    
    async def _evict_lru(self) -> None:
        """Evict least recently used item."""
        if not self._access_times:
            return
        
        # Find LRU item
        lru_key = min(self._access_times.keys(), key=lambda k: self._access_times[k])
        
        del self._cache[lru_key]
        del self._access_times[lru_key]
        self.stats["evictions"] += 1
        
        logger.debug(f"🗑️ Evicted LRU item: {lru_key[:20]}...")
    
    async def clear(self) -> None:
        """Clear all cache."""
        async with self._lock:
            self._cache.clear()
            self._access_times.clear()
            logger.info("🧹 Cache cleared")
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        async with self._lock:
            hit_rate = 0.0
            if self.stats["total_requests"] > 0:
                hit_rate = self.stats["hits"] / self.stats["total_requests"]
            
            return {
                "cache_size": len(self._cache),
                "max_size": self.max_size,
                "hit_rate": round(hit_rate, 3),
                "stats": self.stats.copy(),
                "memory_usage_mb": self._estimate_memory_usage()
            }
    
    def _estimate_memory_usage(self) -> float:
        """Estimate memory usage in MB."""
        import sys
        total_size = 0
        
        for key, item in self._cache.items():
            total_size += sys.getsizeof(key)
            total_size += sys.getsizeof(item)
            total_size += sys.getsizeof(item["data"])
        
        return round(total_size / (1024 * 1024), 2)


class SearchCache:
    """Specialized cache for search results."""
    
    def __init__(self, cache: IntelligentCache):
        self.cache = cache
        self.prefix = "search"
    
    async def get_search_results(
        self, 
        query: str, 
        max_results: int = 10
    ) -> Optional[List[SearchResult]]:
        """Get cached search results."""
        key = self.cache._generate_key(
            self.prefix, 
            {"query": query, "max_results": max_results}
        )
        
        cached_data = await self.cache.get(key)
        if cached_data:
            # Convert back to SearchResult objects
            return [SearchResult(**item) for item in cached_data]
        
        return None
    
    async def cache_search_results(
        self, 
        query: str, 
        max_results: int, 
        results: List[SearchResult]
    ) -> None:
        """Cache search results."""
        key = self.cache._generate_key(
            self.prefix, 
            {"query": query, "max_results": max_results}
        )
        
        # Convert SearchResult objects to dicts for JSON serialization
        serializable_results = [result.model_dump() for result in results]
        
        await self.cache.set(key, serializable_results, ttl=self.cache.search_ttl)


class EmbeddingCache:
    """Specialized cache for embeddings."""
    
    def __init__(self, cache: IntelligentCache):
        self.cache = cache
        self.prefix = "embedding"
    
    async def get_embedding(self, text: str) -> Optional[List[float]]:
        """Get cached embedding."""
        key = self.cache._generate_key(self.prefix, text)
        
        cached_data = await self.cache.get(key)
        if cached_data and isinstance(cached_data, dict):
            return cached_data.get("embedding")
        
        return None
    
    async def cache_embedding(self, text: str, embedding: List[float]) -> None:
        """Cache embedding."""
        key = self.cache._generate_key(self.prefix, text)
        
        data = {
            "text": text[:100],  # Store first 100 chars for debugging
            "embedding": embedding,
            "dimensions": len(embedding)
        }
        
        await self.cache.set(key, data, ttl=self.cache.embedding_ttl)


# Global cache instance
_global_cache: Optional[IntelligentCache] = None
_search_cache: Optional[SearchCache] = None
_embedding_cache: Optional[EmbeddingCache] = None


async def get_cache() -> IntelligentCache:
    """Get global cache instance."""
    global _global_cache
    if _global_cache is None:
        _global_cache = IntelligentCache()
    return _global_cache


async def get_search_cache() -> SearchCache:
    """Get search cache instance."""
    global _search_cache
    if _search_cache is None:
        cache = await get_cache()
        _search_cache = SearchCache(cache)
    return _search_cache


async def get_embedding_cache() -> EmbeddingCache:
    """Get embedding cache instance."""
    global _embedding_cache
    if _embedding_cache is None:
        cache = await get_cache()
        _embedding_cache = EmbeddingCache(cache)
    return _embedding_cache


async def cleanup_cache() -> None:
    """Cleanup cache resources."""
    global _global_cache, _search_cache, _embedding_cache
    
    if _global_cache:
        await _global_cache.clear()
        _global_cache = None
    
    _search_cache = None
    _embedding_cache = None
    
    logger.info("✅ Cache cleanup complete")
