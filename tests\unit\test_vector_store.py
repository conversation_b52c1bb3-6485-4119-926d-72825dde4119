"""
Unit tests for VectorStore
Tests Supabase integration, vector operations, and database functionality
"""

import pytest
import uuid
from unittest.mock import patch, MagicMock, AsyncMock
from src.core.vector_store import VectorStore
from src.core.models import SearchResult

@pytest.mark.unit
class TestVectorStore:
    """Test suite for vector store functionality."""
    
    @pytest.mark.asyncio
    async def test_initialization(self, vector_store):
        """Test vector store initialization."""
        assert vector_store is not None
        assert hasattr(vector_store, 'supabase')
        assert hasattr(vector_store, 'postgres_client')
    
    @pytest.mark.asyncio
    async def test_store_content_basic(self, vector_store):
        """Test basic content storage."""
        test_content = "Тест съдържание за съхранение в базата данни"
        test_vector = [0.1] * 384  # Mock embedding vector
        test_metadata = {
            "title": "Тест заглавие",
            "language": "bg",
            "content_type": "test",
            "quality_score": 0.8
        }
        
        with patch.object(vector_store, 'supabase') as mock_supabase:
            # Mock successful storage
            mock_supabase.table.return_value.insert.return_value.execute.return_value.data = [
                {"id": str(uuid.uuid4())}
            ]
            
            content_id = await vector_store.store_content(
                content=test_content,
                vector=test_vector,
                source_url="https://test.bg",
                metadata=test_metadata
            )
            
            assert content_id is not None
            assert isinstance(content_id, str)
    
    @pytest.mark.asyncio
    async def test_store_content_with_chunks(self, vector_store):
        """Test content storage with multiple chunks."""
        chunks = [
            {
                "content": "Първи чънк от съдържанието",
                "vector": [0.1] * 384,
                "metadata": {"chunk_index": 0}
            },
            {
                "content": "Втори чънк от съдържанието", 
                "vector": [0.2] * 384,
                "metadata": {"chunk_index": 1}
            }
        ]
        
        with patch.object(vector_store, 'supabase') as mock_supabase:
            # Mock successful storage
            mock_supabase.table.return_value.insert.return_value.execute.return_value.data = [
                {"id": str(uuid.uuid4())} for _ in chunks
            ]
            
            content_ids = await vector_store.store_content_chunks(
                chunks=chunks,
                source_url="https://test.bg",
                base_metadata={"language": "bg", "content_type": "test"}
            )
            
            assert len(content_ids) == len(chunks)
            assert all(isinstance(cid, str) for cid in content_ids)
    
    @pytest.mark.asyncio
    async def test_vector_search_basic(self, vector_store):
        """Test basic vector similarity search."""
        query_vector = [0.1] * 384
        
        with patch.object(vector_store, 'supabase') as mock_supabase:
            # Mock search results
            mock_results = [
                {
                    "id": str(uuid.uuid4()),
                    "content": "Тест резултат 1",
                    "title": "Заглавие 1",
                    "source_url": "https://test1.bg",
                    "metadata": {"language": "bg"},
                    "similarity": 0.85
                },
                {
                    "id": str(uuid.uuid4()),
                    "content": "Тест резултат 2", 
                    "title": "Заглавие 2",
                    "source_url": "https://test2.bg",
                    "metadata": {"language": "bg"},
                    "similarity": 0.75
                }
            ]
            
            mock_supabase.rpc.return_value.execute.return_value.data = mock_results
            
            results = await vector_store.vector_search(
                query_vector=query_vector,
                limit=10,
                similarity_threshold=0.7
            )
            
            assert len(results) == 2
            assert all(isinstance(result, SearchResult) for result in results)
            assert results[0].relevance_score >= results[1].relevance_score  # Sorted by similarity
    
    @pytest.mark.asyncio
    async def test_vector_search_with_filters(self, vector_store):
        """Test vector search with metadata filters."""
        query_vector = [0.1] * 384
        filters = {
            "language": "bg",
            "content_type": "program_info"
        }
        
        with patch.object(vector_store, 'supabase') as mock_supabase:
            mock_supabase.rpc.return_value.execute.return_value.data = []
            
            results = await vector_store.vector_search(
                query_vector=query_vector,
                limit=10,
                filters=filters
            )
            
            # Verify filters were applied in the query
            mock_supabase.rpc.assert_called_once()
            call_args = mock_supabase.rpc.call_args
            assert "filters" in call_args[1] or any("language" in str(arg) for arg in call_args)
    
    @pytest.mark.asyncio
    async def test_text_search_basic(self, vector_store):
        """Test full-text search functionality."""
        query = "европейски фондове България"
        
        with patch.object(vector_store, 'supabase') as mock_supabase:
            mock_results = [
                {
                    "id": str(uuid.uuid4()),
                    "content": "Европейски фондове за България са важни",
                    "title": "ЕС фондове",
                    "source_url": "https://test.bg",
                    "metadata": {"language": "bg"},
                    "rank": 0.8
                }
            ]
            
            mock_supabase.rpc.return_value.execute.return_value.data = mock_results
            
            results = await vector_store.text_search(
                query=query,
                limit=10
            )
            
            assert len(results) == 1
            assert isinstance(results[0], SearchResult)
            assert "европейски" in results[0].content.lower() or "фондове" in results[0].content.lower()
    
    @pytest.mark.asyncio
    async def test_hybrid_search(self, vector_store):
        """Test hybrid search combining vector and text search."""
        query = "иновации технологии"
        query_vector = [0.1] * 384
        
        with patch.object(vector_store, 'vector_search') as mock_vector_search, \
             patch.object(vector_store, 'text_search') as mock_text_search:
            
            # Mock vector search results
            mock_vector_search.return_value = [
                SearchResult(
                    title="Test Title 1",
                    content="Иновативни технологии в България",
                    source="https://test1.bg",
                    relevance_score=0.85,
                    metadata={"language": "bg"}
                )
            ]

            # Mock text search results
            mock_text_search.return_value = [
                SearchResult(
                    title="Test Title 2",
                    content="Технологични иновации за МСП",
                    source="https://test2.bg",
                    relevance_score=0.75,
                    metadata={"language": "bg"}
                )
            ]
            
            results = await vector_store.hybrid_search(
                query=query,
                query_vector=query_vector,
                limit=10,
                vector_weight=0.7,
                text_weight=0.3
            )
            
            assert len(results) > 0
            assert all(isinstance(result, SearchResult) for result in results)
    
    @pytest.mark.asyncio
    async def test_get_content_by_id(self, vector_store):
        """Test content retrieval by ID."""
        test_id = str(uuid.uuid4())
        
        with patch.object(vector_store, 'supabase') as mock_supabase:
            mock_content = {
                "id": test_id,
                "content": "Тест съдържание",
                "title": "Тест заглавие",
                "source_url": "https://test.bg",
                "metadata": {"language": "bg"},
                "created_at": "2025-01-01T00:00:00Z"
            }
            
            mock_supabase.table.return_value.select.return_value.eq.return_value.execute.return_value.data = [mock_content]
            
            content = await vector_store.get_content_by_id(test_id)
            
            assert content is not None
            assert content["id"] == test_id
            assert content["content"] == "Тест съдържание"
    
    @pytest.mark.asyncio
    async def test_update_content(self, vector_store):
        """Test content update functionality."""
        test_id = str(uuid.uuid4())
        updates = {
            "title": "Обновено заглавие",
            "metadata": {"language": "bg", "updated": True}
        }
        
        with patch.object(vector_store, 'supabase') as mock_supabase:
            mock_supabase.table.return_value.update.return_value.eq.return_value.execute.return_value.data = [
                {"id": test_id, **updates}
            ]
            
            success = await vector_store.update_content(test_id, updates)
            
            assert success
            mock_supabase.table.return_value.update.assert_called_once_with(updates)
    
    @pytest.mark.asyncio
    async def test_delete_content(self, vector_store):
        """Test content deletion."""
        test_id = str(uuid.uuid4())
        
        with patch.object(vector_store, 'supabase') as mock_supabase:
            mock_supabase.table.return_value.delete.return_value.eq.return_value.execute.return_value.data = [
                {"id": test_id}
            ]
            
            success = await vector_store.delete_content(test_id)
            
            assert success
            mock_supabase.table.return_value.delete.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_statistics(self, vector_store):
        """Test database statistics retrieval."""
        with patch.object(vector_store, 'supabase') as mock_supabase:
            # Mock content count response
            mock_count_response = MagicMock()
            mock_count_response.count = 150
            mock_supabase.table.return_value.select.return_value.execute.return_value = mock_count_response

            # Mock metadata response for languages and content types
            mock_metadata_response = MagicMock()
            mock_metadata_response.data = [
                {"metadata": {"language": "bg", "content_type": "program_info"}},
                {"metadata": {"language": "bg", "content_type": "general_info"}},
                {"metadata": {"language": "en", "content_type": "program_info"}},
            ]

            # Configure the mock to return different responses for different calls
            mock_supabase.table.return_value.select.return_value.execute.side_effect = [
                mock_count_response,  # First call for count
                mock_metadata_response  # Second call for metadata
            ]

            stats = await vector_store.get_statistics()

            assert isinstance(stats, dict)
            assert "total_content" in stats
            assert stats["total_content"] == 150
            assert "languages" in stats
            assert "content_types" in stats
    
    @pytest.mark.asyncio
    async def test_error_handling_connection(self, vector_store):
        """Test error handling for connection issues."""
        with patch.object(vector_store, 'supabase') as mock_supabase:
            # Simulate connection error
            mock_supabase.table.return_value.insert.return_value.execute.side_effect = Exception("Connection failed")

            # Should raise exception for connection errors
            with pytest.raises(Exception, match="Connection failed"):
                await vector_store.store_content(
                    content="Test content",
                    vector=[0.1] * 384,
                    source_url="https://test.bg",
                    metadata={"language": "bg"}
                )
    
    @pytest.mark.asyncio
    async def test_error_handling_invalid_vector(self, vector_store):
        """Test error handling for invalid vector dimensions."""
        invalid_vector = [0.1] * 100  # Wrong dimension
        
        with patch.object(vector_store, 'supabase') as mock_supabase:
            mock_supabase.table.return_value.insert.return_value.execute.side_effect = Exception("Invalid vector dimension")
            
            # Should raise exception for invalid vector dimensions
            with pytest.raises(Exception, match="Invalid vector dimension"):
                await vector_store.store_content(
                    content="Test content",
                    vector=invalid_vector,
                    source_url="https://test.bg",
                    metadata={"language": "bg"}
                )
    
    @pytest.mark.asyncio
    async def test_batch_operations(self, vector_store):
        """Test batch insert/update operations."""
        batch_data = [
            {
                "content": f"Тест съдържание {i}",
                "vector": [0.1 * i] * 384,
                "metadata": {"language": "bg", "index": i}
            }
            for i in range(5)
        ]
        
        with patch.object(vector_store, 'supabase') as mock_supabase:
            mock_supabase.table.return_value.insert.return_value.execute.return_value.data = [
                {"id": str(uuid.uuid4())} for _ in batch_data
            ]
            
            content_ids = await vector_store.batch_store_content(
                batch_data=batch_data,
                source_url="https://test.bg"
            )
            
            assert len(content_ids) == len(batch_data)
            assert all(isinstance(cid, str) for cid in content_ids)
    
    @pytest.mark.asyncio
    async def test_search_performance(self, vector_store):
        """Test search performance and response times."""
        import time
        
        query_vector = [0.1] * 384
        
        with patch.object(vector_store, 'supabase') as mock_supabase:
            # Mock fast response
            mock_supabase.rpc.return_value.execute.return_value.data = []
            
            start_time = time.time()
            results = await vector_store.vector_search(
                query_vector=query_vector,
                limit=10
            )
            end_time = time.time()
            
            # Should complete quickly (under 1 second for mocked operation)
            assert (end_time - start_time) < 1.0
            assert isinstance(results, list)
    
    @pytest.mark.asyncio
    async def test_concurrent_operations(self, vector_store):
        """Test concurrent database operations."""
        import asyncio
        
        async def store_content(index):
            return await vector_store.store_content(
                content=f"Concurrent content {index}",
                vector=[0.1 * index] * 384,
                source_url=f"https://test{index}.bg",
                metadata={"language": "bg", "index": index}
            )
        
        with patch.object(vector_store, 'supabase') as mock_supabase:
            mock_supabase.table.return_value.insert.return_value.execute.return_value.data = [
                {"id": str(uuid.uuid4())}
            ]
            
            # Run multiple operations concurrently
            tasks = [store_content(i) for i in range(5)]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # All operations should complete
            assert len(results) == 5
            # No exceptions should occur
            assert not any(isinstance(result, Exception) for result in results)
