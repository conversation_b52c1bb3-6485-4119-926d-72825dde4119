# EU Funds MCP Server - Global Rules for AI Assistant

## ГЛОБАЛЕН ЗАКОН
**спазваш правилата стриктно**

## Project Overview
You are developing the **EU Funds MCP Server** - an enterprise-grade RAG system that democratizes access to European funding programs information for Bulgaria. This is a "tool for tools" that provides accurate, verified, and current EU funds information to other LLMs.

## Context Engineering Principles
1. **Always read the complete context before any action**
2. **Follow documented patterns exactly - no deviations**
3. **Use working examples as templates**
4. **Validate every step against success criteria**
5. **Self-correct through comprehensive documentation**

## Core Technologies (2025 State-of-the-Art)
- **MCP Protocol**: v1.0 for AI assistant integration
- **Crawl4AI**: v0.6.0 for LLM-friendly web scraping
- **Supabase**: PostgreSQL with pgvector 0.7.0+ for vector storage
- **Embeddings**: multilingual-e5-large-instruct (1024 dimensions, optimized for Bulgarian)
- **Reranking**: cross-encoder/ms-marco-MiniLM-L-6-v2
- **Framework**: FastAPI with Pydantic v2 for async operations
- **Language**: Python 3.12+ with modern async patterns

## Production Configuration
```env
# Provided by user - USE EXACTLY AS PROVIDED
OPENAI_API_KEY=********************************************************************************************************************************************************************
MODEL_CHOICE=gpt-4o-mini
SUPABASE_URL=https://jbdpiowmhaxghnzvhxse.supabase.co
SUPABASE_SERVICE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpiZHBpb3dtaGF4Z2huenZoeHNlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzM4MjQwMCwiZXhwIjoyMDYyOTU4NDAwfQ.azR_6VXSMS-oVO9LElqtBKEbQZqKXfFrm3-CsW_sS-o
```

## Hybrid Search Configuration
- **Reranker Weights**: CE: 0.3, Hybrid: 0.7 (optimized for Bulgarian content)
- **HNSW Parameters**: m=16, ef_construction=64 for optimal performance
- **Chunk Strategy**: Context 7-inspired semantic chunking by headers

## Code Structure Rules
1. **File Size**: Maximum 150 lines per file for maintainability
2. **Module Organization**: Clear separation of concerns (core/, tools/, utils/)
3. **Async Patterns**: Use modern async/await throughout
4. **Error Handling**: Comprehensive try/catch with structured logging
5. **Type Hints**: Full Pydantic v2 models for all data structures

## Testing Requirements
1. **Unit Tests**: pytest with async support
2. **Integration Tests**: Real Supabase connection tests
3. **MCP Protocol Tests**: Validate all tool schemas
4. **Bulgarian Content Tests**: Verify embedding quality for Bulgarian text
5. **Performance Tests**: Sub-second response times for RAG queries

## Documentation Standards
1. **Docstrings**: Google style with Bulgarian language examples
2. **Comments**: Explain Bulgarian-specific processing logic
3. **README**: Complete setup and usage instructions
4. **API Docs**: Auto-generated OpenAPI documentation

## Bulgarian Language Optimization
1. **Text Processing**: Handle Cyrillic characters correctly
2. **Stopwords**: Bulgarian-specific stopword removal
3. **Tokenization**: Proper handling of Bulgarian morphology
4. **Search**: Full-text search with Bulgarian language support
5. **Embeddings**: Validate multilingual-e5-large-instruct performance

## MCP Server Requirements
1. **Protocol Compliance**: Full MCP v1.0 specification
2. **Tool Definitions**: Clear schemas with Bulgarian examples
3. **Error Responses**: Structured error messages
4. **Resource Management**: Proper connection pooling
5. **Health Checks**: Comprehensive system status endpoints

## Security & Performance
1. **API Keys**: Secure environment variable handling
2. **Rate Limiting**: Protect against abuse
3. **Caching**: Intelligent caching for repeated queries
4. **Monitoring**: Structured logging with correlation IDs
5. **Scalability**: Horizontal scaling support

## Success Criteria
1. **Functionality**: All MCP tools work correctly
2. **Performance**: Sub-second RAG responses
3. **Accuracy**: High-quality Bulgarian content retrieval
4. **Reliability**: 99.9% uptime with proper error handling
5. **Maintainability**: Clear code structure and documentation

## Context Engineering Workflow
1. **Read Context**: Always start by reading this file and PLANNING.md
2. **Check Examples**: Review examples/ folder for patterns
3. **Follow Plan**: Execute according to PLANNING.md phases
4. **Validate**: Test each component against success criteria
5. **Document**: Update context as needed for future sessions

## Critical Reminders
- **NO DEVIATIONS** from documented patterns
- **USE PROVIDED API KEYS** exactly as given
- **FOLLOW BULGARIAN LANGUAGE** requirements
- **VALIDATE EVERY STEP** against success criteria
- **SELF-CORRECT** through comprehensive testing
