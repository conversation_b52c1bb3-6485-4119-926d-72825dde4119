# CLAUDE.md - Глобални правила за EU Funds MCP Server

## 🔄 Project Awareness & Context
- **ВИНАГИ чети EU_FUNDS_MCP_PRP.md** в началото на всеки разговор
- **СЛЕДВАЙ СТРИКТНО стъпките** от документа без отклонения  
- **ИЗПОЛЗВАЙ ГЛОБАЛНИЯ ЗАКОН**: Преди всяко действие - прочети документа
- **НИКОГА не отклонявай** от документираните стъпки

## 🧱 Code Structure & Modularity  
- **Никога не създавай файл по-дълъг от 500 реда**
- **Използвай точната структура** от EU_FUNDS_MCP_PRP.md
- **Следвай Bulgarian EU Funds специфичните изисквания**
- **Всеки модул трябва да има ясна отговорност**

## 🧪 Testing & Reliability
- **Винаги създавай Pytest unit tests** за нови функции
- **Тествай с реални Bulgarian EU funds данни**
- **Валидирай с production API keys**
- **Всеки тест трябва да е независим**

## ✅ Task Completion
- **Отбелязвай завършени стъпки** веднага
- **Следвай точното време** за всяка стъпка (1-10 минути)
- **Валидирай успеха** преди преминаване към следващата стъпка

## 📎 Style & Conventions
- **Използвай Bulgarian language processing** оптимизации
- **Следвай enterprise-grade архитектурата**
- **Използвай точните технологии**: Crawl4AI v0.6.0, pgvector 0.7.0+, multilingual-e5-large-instruct
- **Async/await навсякъде** за MCP съвместимост

## 🚨 Critical Rules
- **НИКОГА не променяй API keys** в .env файла
- **ВИНАГИ използвай Pydantic v2** за валидация
- **ЗАДЪЛЖИТЕЛНО тествай** всяка промяна
- **СПРИ и питай** ако нещо не е ясно

## 🎯 Success Criteria
- Всички стъпки от EU_FUNDS_MCP_PRP.md са изпълнени точно
- Всички тестове преминават
- MCP сървърът стартира без грешки
- RAG системата връща точни резултати за български EU фондове
