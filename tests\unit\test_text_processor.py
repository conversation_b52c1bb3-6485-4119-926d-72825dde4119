"""
Unit tests for BulgarianTextProcessor
Tests Bulgarian language processing, chunking, and text analysis
"""

import pytest
from unittest.mock import patch, MagicMock
from src.core.text_processor import BulgarianTextProcessor
from src.core.models import TextChunk, ProcessingResult, ProcessedText

@pytest.mark.unit
@pytest.mark.bulgarian
class TestBulgarianTextProcessor:
    """Test suite for Bulgarian text processing functionality."""
    
    def test_initialization(self, text_processor):
        """Test processor initialization."""
        assert text_processor is not None
        assert hasattr(text_processor, 'stopwords')
        assert len(text_processor.stopwords) > 50  # Should have 80+ stopwords

    def test_language_analysis_bulgarian(self, text_processor, sample_bulgarian_text):
        """Test Bulgarian language analysis."""
        metrics = text_processor._analyze_language(sample_bulgarian_text)
        assert metrics.primary_language == "bg"
        assert metrics.confidence > 0.5
        assert metrics.cyrillic_ratio > 0.5

    def test_language_analysis_english(self, text_processor, sample_english_text):
        """Test English language analysis."""
        metrics = text_processor._analyze_language(sample_english_text)
        assert metrics.primary_language == "en"
        assert metrics.confidence > 0.5
        assert metrics.cyrillic_ratio < 0.1

    def test_clean_text_basic(self, text_processor):
        """Test basic text cleaning."""
        dirty_text = "  Това е   тест  \n\n  с много   spaces  "
        cleaned = text_processor._clean_text(dirty_text)
        assert "Това е" in cleaned
        assert "тест" in cleaned
        assert "много" in cleaned

    def test_clean_text_html(self, text_processor):
        """Test HTML tag removal."""
        html_text = "<div>Това е <strong>важен</strong> текст</div>"
        cleaned = text_processor._clean_text(html_text)
        assert "<" not in cleaned
        assert ">" not in cleaned
        assert "важен" in cleaned

    def test_advanced_html_cleaning(self, text_processor):
        """Test advanced HTML cleaning with BeautifulSoup."""
        complex_html = """
        <html>
        <head>
            <title>ЕС Фондове</title>
            <script>alert('test');</script>
            <style>.test { color: red; }</style>
        </head>
        <body>
            <nav class="navigation">
                <a href="/home">Начало</a>
                <a href="/about">За нас</a>
            </nav>
            <header>
                <h1>Европейски фондове за България</h1>
            </header>
            <main>
                <h2>Програми за финансиране</h2>
                <p>Това е <strong>важна</strong> информация за <em>финансиране</em>.</p>
                <ul>
                    <li>Програма 1: Иновации</li>
                    <li>Програма 2: Образование</li>
                    <li>Програма 3: МСП</li>
                </ul>
                <table>
                    <tr>
                        <th>Програма</th>
                        <th>Бюджет</th>
                        <th>Срок</th>
                    </tr>
                    <tr>
                        <td>Иновации</td>
                        <td>10 млн. евро</td>
                        <td>31.12.2024</td>
                    </tr>
                </table>
            </main>
            <aside class="sidebar">
                <div class="ad-banner">Реклама тук</div>
            </aside>
            <footer>
                <p>Copyright 2024</p>
            </footer>
        </body>
        </html>
        """

        cleaned = text_processor._clean_text(complex_html)

        # Should preserve important content
        assert "Европейски фондове за България" in cleaned
        assert "Програми за финансиране" in cleaned
        assert "важна" in cleaned
        assert "финансиране" in cleaned
        assert "• Програма 1: Иновации" in cleaned
        assert "• Програма 2: Образование" in cleaned
        assert "• Програма 3: МСП" in cleaned
        assert "Иновации | 10 млн. евро | 31.12.2024" in cleaned

        # Should remove unwanted elements
        assert "alert('test')" not in cleaned
        assert ".test { color: red; }" not in cleaned
        assert "Начало" not in cleaned  # Navigation
        assert "За нас" not in cleaned  # Navigation
        assert "Реклама тук" not in cleaned  # Ads
        assert "Copyright" not in cleaned  # Footer
        assert "<script>" not in cleaned
        assert "<style>" not in cleaned
        assert "<nav>" not in cleaned

    def test_normalize_cyrillic(self, text_processor):
        """Test Cyrillic text normalization."""
        cyrillic_text = "Това е тест с български символи"
        normalized = text_processor._normalize_cyrillic(cyrillic_text)
        assert "Това" in normalized
        assert "български" in normalized

    def test_quality_score_calculation_high(self, text_processor):
        """Test quality score calculation for high-quality content."""
        high_quality_text = """
        Оперативна програма "Иновации и конкурентоспособност" предоставя финансиране
        за проекти в областта на научните изследвания, технологичното развитие и
        иновациите. Програмата подкрепя малки и средни предприятия чрез различни
        финансови инструменти и мерки за повишаване на тяхната конкурентоспособност.
        """
        extracted_info = text_processor._extract_funding_info(high_quality_text)
        language_metrics = text_processor._analyze_language(high_quality_text)
        score = text_processor._calculate_quality_score(high_quality_text, extracted_info, language_metrics)
        assert score > 0.3  # Should be reasonable quality

    def test_quality_score_calculation_low(self, text_processor):
        """Test quality score calculation for low-quality content."""
        low_quality_text = "Кратък."
        extracted_info = text_processor._extract_funding_info(low_quality_text)
        language_metrics = text_processor._analyze_language(low_quality_text)
        score = text_processor._calculate_quality_score(low_quality_text, extracted_info, language_metrics)
        assert score < 0.8  # Should be lower quality

    def test_funding_info_extraction(self, text_processor):
        """Test extraction of funding information."""
        funding_text = "ОПИК програма предоставя безвъзмездна помощ за МСП до 200000 лева"
        extracted = text_processor._extract_funding_info(funding_text)

        assert isinstance(extracted, dict)
        assert "programs" in extracted
        assert "budgets" in extracted
        assert "deadlines" in extracted
        assert "eligibility" in extracted

    def test_chunk_creation(self, text_processor):
        """Test text chunk creation."""
        test_text = "Това е тест текст за създаване на чънк"
        chunk = text_processor._create_chunk(test_text, 0)

        assert isinstance(chunk, TextChunk)
        assert chunk.chunk_id == 0
        assert chunk.content == test_text
        assert chunk.text == test_text
        assert chunk.language == "bg"
        assert chunk.word_count > 0
    
    @pytest.mark.asyncio
    async def test_process_text_complete(self, text_processor, sample_bulgarian_text):
        """Test complete text processing pipeline."""
        result = await text_processor.process_text(
            sample_bulgarian_text,
            source_url="https://test.bg"
        )
        
        assert isinstance(result, ProcessedText)
        assert len(result.chunks) > 0
        assert result.language_metrics.primary_language == "bg"
        assert result.quality_score > 0
    
    @pytest.mark.asyncio
    async def test_process_text_empty(self, text_processor):
        """Test processing empty text."""
        result = await text_processor.process_text("", source_url="https://test.bg")
        
        assert isinstance(result, ProcessedText)
        assert len(result.chunks) == 0
        assert result.quality_score == 0.0
    
    @pytest.mark.asyncio
    async def test_process_text_html_content(self, text_processor):
        """Test processing HTML content."""
        html_content = """
        <html>
        <head><title>Тест страница</title></head>
        <body>
        <h1>Европейски фондове</h1>
        <p>Това е <strong>важна</strong> информация за <em>финансиране</em>.</p>
        <div>Допълнителни детайли тук.</div>
        </body>
        </html>
        """
        
        result = await text_processor.process_text(html_content, source_url="https://test.bg")
        
        assert isinstance(result, ProcessedText)
        assert len(result.chunks) > 0
        # HTML tags should be removed from cleaned text
        assert "<html>" not in result.cleaned_text
        assert "Европейски фондове" in result.cleaned_text
    
    def test_bulgarian_specific_processing(self, text_processor):
        """Test Bulgarian-specific text processing features."""
        bulgarian_text = "Министерството на европейските фондове обяви нова процедура"
        
        # Test that Bulgarian-specific terms are preserved
        cleaned = text_processor._clean_text(bulgarian_text)
        assert "Министерството" in cleaned
        assert "европейските" in cleaned
        assert "процедура" in cleaned

        # Test language analysis
        metrics = text_processor._analyze_language(bulgarian_text)
        assert metrics.primary_language == "bg"
    
    def test_funding_terms_detection(self, text_processor):
        """Test detection of EU funding-related terms."""
        funding_text = "ОПИК програма финансиране европейски фондове безвъзмездна помощ"
        
        # Should detect funding-related terms
        extracted_info = text_processor._extract_funding_info(funding_text)
        language_metrics = text_processor._analyze_language(funding_text)
        quality_score = text_processor._calculate_quality_score(funding_text, extracted_info, language_metrics)

        # Funding-related content should get quality boost
        generic_text = "Това е обикновен текст без специални термини"
        generic_extracted = text_processor._extract_funding_info(generic_text)
        generic_metrics = text_processor._analyze_language(generic_text)
        generic_score = text_processor._calculate_quality_score(generic_text, generic_extracted, generic_metrics)
        
        assert quality_score > generic_score
