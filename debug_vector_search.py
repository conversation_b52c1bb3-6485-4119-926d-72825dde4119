#!/usr/bin/env python3
"""
Debug vector search to understand why it's not working.
"""

import asyncio
import sys
import os
from supabase import create_client, Client
from dotenv import load_dotenv
import openai

# Load environment variables
load_dotenv()

async def debug_vector_search():
    """Debug vector search functionality."""
    print("🔍 DEBUG VECTOR SEARCH")
    print("=" * 50)
    
    # Initialize Supabase client
    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_SERVICE_KEY") or os.getenv("SUPABASE_KEY")
    supabase: Client = create_client(url, key)
    
    # Initialize OpenAI
    openai.api_key = os.getenv("OPENAI_API_KEY")
    
    # Check data count
    print("📊 ПРОВЕРКА НА ДАННИТЕ:")
    try:
        result = supabase.table('eu_funds_content').select("id, title, embedding").limit(5).execute()
        
        print(f"   Общо записи: {len(result.data)}")
        
        if not result.data:
            print(f"   ❌ Няма данни!")
            return
        
        # Check embeddings
        embeddings_count = 0
        for i, record in enumerate(result.data):
            embedding = record.get('embedding')
            title = record.get('title', 'N/A')
            
            if embedding is not None:
                embeddings_count += 1
                if i < 2:  # Show first 2
                    print(f"   Запис {i+1}: {title[:50]}...")
                    print(f"     Embedding тип: {type(embedding)}")
                    print(f"     Embedding размер: {len(embedding) if hasattr(embedding, '__len__') else 'N/A'}")
                    
                    # Check if it's a string representation
                    if isinstance(embedding, str):
                        print(f"     Embedding preview: {embedding[:100]}...")
                    elif isinstance(embedding, list):
                        print(f"     Embedding preview: {embedding[:5]}...")
        
        print(f"   Записи с embeddings: {embeddings_count}")
        
    except Exception as e:
        print(f"   ❌ Грешка при проверка: {e}")
        return
    
    # Test vector search with different approaches
    print(f"\n🧪 ТЕСТВАНЕ НА VECTOR SEARCH:")
    
    # Create test embedding
    try:
        response = openai.embeddings.create(
            model="text-embedding-3-small",
            input="програма финансиране"
        )
        
        test_embedding = response.data[0].embedding
        print(f"   ✅ Тестов embedding: {len(test_embedding)} dimensions")
        
        # Test 1: match_eu_funds_content with different thresholds
        print(f"\n   🔍 Тест 1: match_eu_funds_content с различни прагове")
        
        thresholds = [0.0, 0.1, 0.3, 0.5, 0.8]
        for threshold in thresholds:
            try:
                result = supabase.rpc('match_eu_funds_content', {
                    'query_embedding': test_embedding,
                    'match_threshold': threshold,
                    'match_count': 5
                }).execute()
                
                print(f"     Праг {threshold}: {len(result.data)} резултата")
                
                if result.data and threshold == 0.0:  # Show details for threshold 0
                    for i, item in enumerate(result.data[:2]):
                        similarity = item.get('similarity', 0)
                        title = item.get('title', 'N/A')
                        print(f"       Резултат {i+1}: {title[:40]}... (sim: {similarity:.3f})")
                
            except Exception as e:
                print(f"     Праг {threshold}: грешка - {e}")
        
        # Test 2: Check if embeddings are NULL
        print(f"\n   🔍 Тест 2: Проверка за NULL embeddings")
        try:
            # Count records with non-null embeddings
            result = supabase.rpc('count_non_null_embeddings').execute()
            print(f"     Резултат: {result.data if result.data else 'Функцията не съществува'}")
        except Exception as e:
            print(f"     Грешка: {e}")
            
            # Alternative check using SQL
            try:
                # This won't work directly, but let's try
                print(f"     Алтернативна проверка...")
                result = supabase.table('eu_funds_content').select("id").not_.is_("embedding", "null").limit(5).execute()
                print(f"     Записи с non-null embedding: {len(result.data)}")
            except Exception as e2:
                print(f"     Алтернативна проверка грешка: {e2}")
        
        # Test 3: Manual similarity calculation
        print(f"\n   🔍 Тест 3: Manual similarity проверка")
        try:
            # Get one record and try to calculate similarity manually
            result = supabase.table('eu_funds_content').select("*").limit(1).execute()
            
            if result.data:
                record = result.data[0]
                stored_embedding = record.get('embedding')
                
                if stored_embedding:
                    print(f"     Запис: {record.get('title', 'N/A')[:40]}...")
                    print(f"     Stored embedding тип: {type(stored_embedding)}")
                    
                    # Try to parse if it's a string
                    if isinstance(stored_embedding, str):
                        try:
                            import json
                            parsed_embedding = json.loads(stored_embedding)
                            print(f"     Parsed embedding размер: {len(parsed_embedding)}")
                            
                            # Calculate cosine similarity manually
                            import numpy as np
                            
                            vec1 = np.array(test_embedding)
                            vec2 = np.array(parsed_embedding)
                            
                            similarity = np.dot(vec1, vec2) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))
                            print(f"     Manual similarity: {similarity:.3f}")
                            
                        except Exception as parse_error:
                            print(f"     Parse грешка: {parse_error}")
                    
                    elif isinstance(stored_embedding, list):
                        print(f"     List embedding размер: {len(stored_embedding)}")
                        
                        # Calculate similarity
                        import numpy as np
                        
                        vec1 = np.array(test_embedding)
                        vec2 = np.array(stored_embedding)
                        
                        similarity = np.dot(vec1, vec2) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))
                        print(f"     Manual similarity: {similarity:.3f}")
                
        except Exception as e:
            print(f"     Manual test грешка: {e}")
        
    except Exception as e:
        print(f"   ❌ OpenAI грешка: {e}")

if __name__ == "__main__":
    asyncio.run(debug_vector_search())
