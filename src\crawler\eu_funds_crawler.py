"""
EU Funds Crawler with Crawl4AI
Crawls EU funding websites with depth=0 for initial testing
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
import json
import re
from urllib.parse import urljoin, urlparse

from crawl4ai import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CrawlerRunConfig, LLMConfig
from crawl4ai.extraction_strategy import LLMExtractionStrategy

from src.core.config import settings
from src.core.text_processor import BulgarianTextProcessor
from src.core.embeddings import EmbeddingProcessor
from src.core.vector_store import VectorStore
from src.core.models import CrawledContent, ProcessedText

logger = logging.getLogger(__name__)

class EUFundsCrawler:
    """
    EU Funds crawler with Crawl4AI integration.
    Configured for depth=0 initial testing with minimal data collection.
    """
    
    def __init__(self):
        """Initialize EU Funds crawler."""
        self.crawler = None
        self.text_processor = BulgarianTextProcessor()
        self.embedding_processor = EmbeddingProcessor()
        self.vector_store = VectorStore()
        
        # Crawler configuration
        self.crawl_depth = settings.crawl_depth  # Set to 0 for initial testing
        self.max_pages = 10  # Limit for initial testing
        self.delay = settings.crawl_delay
        self.timeout = settings.crawl_timeout
        
        # EU funding websites for initial testing
        self.test_urls = [
            # Bulgarian EU funding sites (primary)
            "https://www.eufunds.bg/bg",  # Updated to Bulgarian language version
            "https://prsr.bg/",

            # EU official sites (limited)
            "https://ec.europa.eu/info/funding-tenders_en",
            "https://ec.europa.eu/regional_policy/funding_en",
        ]
        
        # Skip LLM extraction for initial testing - use basic crawling
        self.extraction_strategy = None
        
        # Statistics
        self.crawl_stats = {
            "pages_crawled": 0,
            "pages_processed": 0,
            "content_stored": 0,
            "errors": 0,
            "start_time": None,
            "end_time": None
        }
    
    async def initialize(self) -> None:
        """Initialize crawler and dependencies."""
        try:
            logger.info("🕷️ Initializing EU Funds Crawler...")
            
            # Initialize dependencies (only those that need async initialization)
            await self.embedding_processor.initialize()
            await self.vector_store.initialize()
            
            # Initialize Crawl4AI crawler
            self.crawler = AsyncWebCrawler(
                verbose=True,
                delay=self.delay,
                timeout=self.timeout,
                max_depth=self.crawl_depth,  # depth=0 for initial testing
                word_count_threshold=50,  # Minimum words per page
                only_text=False,  # Keep HTML structure for better extraction
                remove_overlay_elements=True,
                social_media_domains=[]  # Skip social media
            )
            
            await self.crawler.start()
            logger.info("✅ EU Funds Crawler initialized successfully")
            logger.info(f"📋 Configuration: depth={self.crawl_depth}, max_pages={self.max_pages}")
            
        except Exception as e:
            logger.error(f"❌ Crawler initialization failed: {e}")
            raise
    
    async def crawl_test_sites(self) -> Dict[str, Any]:
        """
        Crawl test EU funding sites with depth=0.
        Returns crawling results and statistics.
        """
        try:
            logger.info("🚀 Starting EU Funds crawling (depth=0 test mode)...")
            self.crawl_stats["start_time"] = datetime.utcnow()
            
            crawled_content = []
            
            for url in self.test_urls[:3]:  # Limit to first 3 URLs for testing
                try:
                    logger.info(f"🔍 Crawling: {url}")
                    
                    # Configure crawl for this URL (basic crawling without LLM extraction)
                    config = CrawlerRunConfig(
                        word_count_threshold=50,
                        only_text=False,
                        remove_overlay_elements=True
                    )
                    
                    # Crawl the page
                    result = await self.crawler.arun(url=url, config=config)
                    
                    if result.success:
                        self.crawl_stats["pages_crawled"] += 1
                        
                        # Process the crawled content
                        content = await self._process_crawled_page(url, result)
                        if content:
                            crawled_content.append(content)
                            self.crawl_stats["pages_processed"] += 1
                            
                            # Store in vector database
                            await self._store_content(content)
                            self.crawl_stats["content_stored"] += 1
                            
                        logger.info(f"✅ Successfully processed: {url}")
                    else:
                        logger.warning(f"⚠️ Failed to crawl: {url} - {result.error_message}")
                        self.crawl_stats["errors"] += 1
                    
                    # Delay between requests
                    await asyncio.sleep(self.delay)
                    
                except Exception as e:
                    logger.error(f"❌ Error crawling {url}: {e}")
                    self.crawl_stats["errors"] += 1
                    continue
            
            self.crawl_stats["end_time"] = datetime.utcnow()
            
            # Generate crawl report
            report = self._generate_crawl_report(crawled_content)
            
            logger.info("✅ EU Funds crawling completed!")
            logger.info(f"📊 Results: {self.crawl_stats['pages_processed']} pages processed, {self.crawl_stats['content_stored']} content items stored")
            
            return report
            
        except Exception as e:
            logger.error(f"❌ Crawling failed: {e}")
            self.crawl_stats["end_time"] = datetime.utcnow()
            raise
    
    async def _process_crawled_page(self, url: str, crawl_result) -> Optional[CrawledContent]:
        """Process a single crawled page."""
        try:
            # Extract basic information
            raw_text = crawl_result.cleaned_html or crawl_result.markdown or ""
            if len(raw_text.strip()) < 50:
                logger.warning(f"⚠️ Page too short, skipping: {url}")
                return None
            
            # Extract basic metadata (no LLM extraction for initial testing)
            extracted_data = {
                "title": getattr(crawl_result, 'title', '') or '',
                "content_type": "general_info",
                "language": "unknown"
            }
            
            # Process text with Bulgarian processor
            processed_text = await self.text_processor.process_text(raw_text)
            
            # Determine content type and language
            content_type = self._determine_content_type(raw_text, extracted_data)
            language = self._detect_language(raw_text)
            
            # Create crawled content object
            content = CrawledContent(
                url=url,
                title=extracted_data.get("title", crawl_result.metadata.get("title", "")),
                content=raw_text,
                processed_content=processed_text,
                metadata={
                    "content_type": content_type,
                    "language": language,
                    "crawl_timestamp": datetime.utcnow().isoformat(),
                    "word_count": len(raw_text.split()),
                    "extracted_data": extracted_data,
                    "domain": urlparse(url).netloc
                },
                crawl_timestamp=datetime.utcnow()
            )
            
            return content
            
        except Exception as e:
            logger.error(f"❌ Error processing page {url}: {e}")
            return None
    
    def _determine_content_type(self, text: str, extracted_data: Dict) -> str:
        """Determine the type of content based on text analysis."""
        text_lower = text.lower()
        
        # Check extracted data first
        if extracted_data.get("content_type"):
            return extracted_data["content_type"]
        
        # Analyze text content
        if any(word in text_lower for word in ["програма", "program", "финансиране", "funding"]):
            return "funding_program"
        elif any(word in text_lower for word in ["критерии", "criteria", "условия", "eligibility"]):
            return "eligibility_criteria"
        elif any(word in text_lower for word in ["срок", "deadline", "кандидатстване", "application"]):
            return "deadline_info"
        elif any(word in text_lower for word in ["новини", "news", "съобщение", "announcement"]):
            return "news"
        else:
            return "general_info"
    
    def _detect_language(self, text: str) -> str:
        """Detect content language (Bulgarian vs English)."""
        # Simple heuristic based on Cyrillic characters
        cyrillic_chars = len(re.findall(r'[а-яё]', text.lower()))
        total_chars = len(re.findall(r'[a-zа-яё]', text.lower()))
        
        if total_chars > 0 and cyrillic_chars / total_chars > 0.3:
            return "bg"
        else:
            return "en"
    
    async def _store_content(self, content: CrawledContent) -> None:
        """Store crawled content in vector database."""
        try:
            # Generate embeddings for the processed content
            if content.processed_content and content.processed_content.chunks:
                for chunk in content.processed_content.chunks:
                    # Generate embedding for this chunk
                    embedding_result = await self.embedding_processor.embed_text(chunk.text)
                    embedding = embedding_result.vector if embedding_result else None

                    # Store each chunk as a separate vector
                    await self.vector_store.store_content(
                        content=chunk.text,
                        embedding=embedding,
                        metadata={
                            **content.metadata,
                            "chunk_id": chunk.chunk_id,
                            "chunk_type": chunk.chunk_type,
                            "source_url": content.url,
                            "title": content.title or f"Content from {content.url}"
                        },
                        source_url=content.url
                    )
            else:
                # Generate embedding for full content
                embedding_result = await self.embedding_processor.embed_text(content.content)
                embedding = embedding_result.vector if embedding_result else None

                # Store full content if no chunks
                await self.vector_store.store_content(
                    content=content.content,
                    embedding=embedding,
                    metadata={
                        **content.metadata,
                        "title": content.title or f"Content from {content.url}"
                    },
                    source_url=content.url
                )
                
        except Exception as e:
            logger.error(f"❌ Error storing content for {content.url}: {e}")
            raise
    
    def _generate_crawl_report(self, crawled_content: List[CrawledContent]) -> Dict[str, Any]:
        """Generate comprehensive crawl report."""
        total_time = (
            self.crawl_stats["end_time"] - self.crawl_stats["start_time"]
        ).total_seconds() if self.crawl_stats["end_time"] and self.crawl_stats["start_time"] else 0
        
        # Analyze content types and languages
        content_types = {}
        languages = {}
        domains = {}
        
        for content in crawled_content:
            content_type = content.metadata.get("content_type", "unknown")
            language = content.metadata.get("language", "unknown")
            domain = content.metadata.get("domain", "unknown")
            
            content_types[content_type] = content_types.get(content_type, 0) + 1
            languages[language] = languages.get(language, 0) + 1
            domains[domain] = domains.get(domain, 0) + 1
        
        return {
            "crawl_statistics": self.crawl_stats,
            "performance": {
                "total_time_seconds": total_time,
                "pages_per_second": self.crawl_stats["pages_crawled"] / total_time if total_time > 0 else 0,
                "success_rate": (
                    self.crawl_stats["pages_processed"] / self.crawl_stats["pages_crawled"]
                    if self.crawl_stats["pages_crawled"] > 0 else 0
                )
            },
            "content_analysis": {
                "content_types": content_types,
                "languages": languages,
                "domains": domains,
                "total_content_items": len(crawled_content)
            },
            "configuration": {
                "crawl_depth": self.crawl_depth,
                "max_pages": self.max_pages,
                "delay": self.delay,
                "timeout": self.timeout
            },
            "timestamp": datetime.utcnow().isoformat()
        }
    
    async def cleanup(self):
        """Clean up crawler resources."""
        try:
            if self.crawler:
                await self.crawler.close()
            
            # Cleanup only components that need async cleanup
            if self.embedding_processor:
                await self.embedding_processor.cleanup()
            if self.vector_store:
                await self.vector_store.cleanup()
            
            logger.info("✅ EU Funds Crawler cleanup complete")
            
        except Exception as e:
            logger.error(f"❌ Crawler cleanup failed: {e}")
