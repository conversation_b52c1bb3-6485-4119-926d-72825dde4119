Metadata-Version: 2.4
Name: eu-funds-mcp-server
Version: 0.1.0
Summary: Enterprise-Grade MCP Server for EU Funding Programs
Author-email: EU Funds MCP Team <<EMAIL>>
License: MIT
Classifier: Development Status :: 3 - Alpha
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.12
Description-Content-Type: text/markdown
Requires-Dist: mcp>=1.0.0
Requires-Dist: fastapi>=0.115.0
Requires-Dist: uvicorn[standard]>=0.32.0
Requires-Dist: crawl4ai>=0.6.0
Requires-Dist: playwright>=1.48.0
Requires-Dist: beautifulsoup4>=4.12.0
Requires-Dist: sentence-transformers>=4.1.0
Requires-Dist: openai>=1.54.0
Requires-Dist: cohere>=5.11.0
Requires-Dist: supabase>=2.15.0
Requires-Dist: psycopg2-binary>=2.9.0
Requires-Dist: pgvector>=0.3.0
Requires-Dist: pydantic>=2.10.0
Requires-Dist: pydantic-settings>=2.7.0
Requires-Dist: python-dotenv>=1.0.0
Requires-Dist: loguru>=0.7.0
Requires-Dist: redis>=5.2.0
Requires-Dist: httpx>=0.28.0
Requires-Dist: prometheus-client>=0.22.0
Requires-Dist: asyncio-mqtt>=0.16.0
Requires-Dist: aiofiles>=24.1.0
Provides-Extra: dev
Requires-Dist: pytest>=8.3.0; extra == "dev"
Requires-Dist: pytest-asyncio>=0.24.0; extra == "dev"
Requires-Dist: pytest-cov>=6.0.0; extra == "dev"
Requires-Dist: black>=24.10.0; extra == "dev"
Requires-Dist: ruff>=0.8.0; extra == "dev"
Requires-Dist: mypy>=1.13.0; extra == "dev"
Requires-Dist: pre-commit>=4.0.0; extra == "dev"
Provides-Extra: test
Requires-Dist: pytest>=8.3.0; extra == "test"
Requires-Dist: pytest-asyncio>=0.24.0; extra == "test"
Requires-Dist: pytest-mock>=3.14.0; extra == "test"
Requires-Dist: httpx>=0.28.0; extra == "test"
Requires-Dist: respx>=0.22.0; extra == "test"
