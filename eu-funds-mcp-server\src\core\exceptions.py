"""
Custom exceptions for EU Funds MCP Server.
Provides structured error handling across all modules.
"""

from typing import Optional, Dict, Any


class EUFundsMCPError(Exception):
    """Base exception for EU Funds MCP Server."""

    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        original_error: Optional[Exception] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        self.original_error = original_error

    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for JSON serialization."""
        return {
            "error_type": self.__class__.__name__,
            "message": self.message,
            "error_code": self.error_code,
            "details": self.details,
            "original_error": str(self.original_error) if self.original_error else None
        }


class ConfigurationError(EUFundsMCPError):
    """Raised when configuration is invalid or missing."""
    pass


class DatabaseError(EUFundsMCPError):
    """Raised when database operations fail."""
    pass


class CrawlerError(EUFundsMCPError):
    """Raised when web crawling operations fail."""
    pass


class RAGError(EUFundsMCPError):
    """Raised when RAG system operations fail."""
    pass


class MCPServerError(EUFundsMCPError):
    """Raised when MCP server operations fail."""
    pass


class EmbeddingError(RAGError):
    """Raised when embedding generation fails."""
    pass


class SearchError(RAGError):
    """Raised when search operations fail."""
    pass


class ContentExtractionError(CrawlerError):
    """Raised when content extraction fails."""
    pass


class ValidationError(EUFundsMCPError):
    """Raised when data validation fails."""
    pass
