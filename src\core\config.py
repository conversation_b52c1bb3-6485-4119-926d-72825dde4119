"""
Configuration Management for EU Funds MCP Server

This module provides comprehensive configuration management using Pydantic Settings
with environment variables and validation. Based on examples/config_example.py.
"""

import os
from typing import Optional, List
from pydantic import Field, validator
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    """
    Application settings with environment variable support and validation.
    
    All settings can be overridden via environment variables.
    Example: OPENAI_API_KEY=your_key python app.py
    """
    
    # Server Configuration
    host: str = Field(default="0.0.0.0", description="Server host address")
    port: int = Field(default=8051, description="Server port")
    debug: bool = Field(default=False, description="Enable debug mode")
    
    # OpenAI Configuration
    openai_api_key: str = Field(..., description="OpenAI API key for embeddings")
    model_choice: str = Field(default="gpt-4o-mini", description="OpenAI model for text processing")
    
    # Supabase Configuration
    supabase_url: str = Field(..., description="Supabase project URL")
    supabase_service_key: str = Field(..., description="Supabase service role key")
    SUPABASE_DB_PASSWORD: str = Field(default="", description="Supabase database password")
    
    # Embedding Configuration
    embedding_model: str = Field(
        default="sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2",
        description="Embedding model for Bulgarian language support"
    )
    embedding_dimensions: int = Field(default=384, description="Embedding vector dimensions")
    
    # Search Configuration
    max_search_results: int = Field(default=50, description="Maximum search results to return")
    similarity_threshold: float = Field(default=0.7, description="Minimum similarity threshold")
    
    # Reranking Configuration
    use_reranking: bool = Field(default=True, description="Enable cross-encoder reranking")
    reranker_model: str = Field(
        default="cross-encoder/ms-marco-MiniLM-L-6-v2",
        description="Cross-encoder model for reranking"
    )
    reranker_weight_ce: float = Field(default=0.3, description="Cross-encoder weight")
    reranker_weight_hybrid: float = Field(default=0.7, description="Hybrid search weight")
    
    # Database Configuration
    db_pool_size: int = Field(default=10, description="Database connection pool size")
    db_max_overflow: int = Field(default=20, description="Database connection pool overflow")
    db_timeout: int = Field(default=30, description="Database connection timeout (seconds)")
    
    # HNSW Index Configuration
    hnsw_m: int = Field(default=16, description="HNSW index M parameter")
    hnsw_ef_construction: int = Field(default=64, description="HNSW index ef_construction parameter")
    
    # Crawling Configuration
    crawl_delay: float = Field(default=1.0, description="Delay between crawl requests (seconds)")
    crawl_timeout: int = Field(default=30, description="Crawl request timeout (seconds)")
    max_concurrent_crawls: int = Field(default=5, description="Maximum concurrent crawl operations")
    crawl_depth: int = Field(default=1, description="Crawling depth (0=current page only, 1=one level deep, etc.)")
    
    # Bulgarian Language Configuration
    bulgarian_stopwords: List[str] = Field(
        default_factory=lambda: [
            "и", "в", "на", "за", "с", "от", "до", "по", "при", "към", "без", "над", "под",
            "между", "пред", "зад", "около", "освен", "въпреки", "благодарение", "поради",
            "че", "да", "ако", "когато", "докато", "преди", "след", "както", "защото",
            "но", "а", "или", "нито", "дори", "само", "още", "вече", "все", "пак",
            "този", "тази", "това", "тези", "такъв", "такава", "такова", "такива",
            "който", "която", "което", "които", "чий", "чия", "чие", "чии",
            "аз", "ти", "той", "тя", "то", "ние", "вие", "те", "мен", "теб", "него", "нея",
            "нас", "вас", "тях", "ми", "ти", "му", "й", "ни", "ви", "им", "си", "се"
        ],
        description="Bulgarian stopwords for text processing"
    )
    
    # Caching Configuration
    cache_ttl: int = Field(default=3600, description="Cache TTL in seconds")
    cache_max_size: int = Field(default=1000, description="Maximum cache entries")
    
    # Logging Configuration
    log_level: str = Field(default="INFO", description="Logging level")
    log_format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="Log format string"
    )
    
    # Performance Configuration
    request_timeout: int = Field(default=30, description="Request timeout (seconds)")
    max_content_length: int = Field(default=10485760, description="Maximum content length (10MB)")
    
    # Security Configuration
    allowed_origins: List[str] = Field(
        default_factory=lambda: ["*"],
        description="CORS allowed origins"
    )
    rate_limit_requests: int = Field(default=100, description="Rate limit requests per minute")
    
    @validator("openai_api_key")
    def validate_openai_key(cls, v):
        """Validate OpenAI API key format."""
        if not v.startswith("sk-"):
            raise ValueError("OpenAI API key must start with 'sk-'")
        if len(v) < 20:
            raise ValueError("OpenAI API key appears to be too short")
        return v
    
    @validator("supabase_url")
    def validate_supabase_url(cls, v):
        """Validate Supabase URL format."""
        if not v.startswith("https://"):
            raise ValueError("Supabase URL must start with 'https://'")
        if not v.endswith(".supabase.co"):
            raise ValueError("Supabase URL must end with '.supabase.co'")
        return v
    
    @validator("supabase_service_key")
    def validate_supabase_key(cls, v):
        """Validate Supabase service key format."""
        if not v.startswith("eyJ"):
            raise ValueError("Supabase service key appears to be invalid (should be JWT)")
        return v
    
    @validator("similarity_threshold")
    def validate_similarity_threshold(cls, v):
        """Validate similarity threshold range."""
        if not 0.0 <= v <= 1.0:
            raise ValueError("Similarity threshold must be between 0.0 and 1.0")
        return v
    
    @validator("reranker_weight_ce", "reranker_weight_hybrid")
    def validate_reranker_weights(cls, v):
        """Validate reranker weight range."""
        if not 0.0 <= v <= 1.0:
            raise ValueError("Reranker weights must be between 0.0 and 1.0")
        return v
    
    @validator("port")
    def validate_port(cls, v):
        """Validate port range."""
        if not 1 <= v <= 65535:
            raise ValueError("Port must be between 1 and 65535")
        return v
    
    @validator("log_level")
    def validate_log_level(cls, v):
        """Validate log level."""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"Log level must be one of: {valid_levels}")
        return v.upper()
    
    class Config:
        """Pydantic configuration."""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        
        # Environment variable prefixes
        env_prefix = ""
        
        # Field aliases for environment variables
        fields = {
            "openai_api_key": {"env": "OPENAI_API_KEY"},
            "model_choice": {"env": "MODEL_CHOICE"},
            "supabase_url": {"env": "SUPABASE_URL"},
            "supabase_service_key": {"env": "SUPABASE_SERVICE_KEY"},
        }

    def get_database_url(self) -> str:
        """Get formatted database URL for Supabase connection."""
        return f"{self.supabase_url}/rest/v1/"
    
    def get_embedding_config(self) -> dict:
        """Get embedding configuration dictionary."""
        return {
            "model": self.embedding_model,
            "dimensions": self.embedding_dimensions,
            "api_key": self.openai_api_key
        }
    
    def get_search_config(self) -> dict:
        """Get search configuration dictionary."""
        return {
            "max_results": self.max_search_results,
            "similarity_threshold": self.similarity_threshold,
            "use_reranking": self.use_reranking,
            "reranker_model": self.reranker_model,
            "weights": {
                "ce": self.reranker_weight_ce,
                "hybrid": self.reranker_weight_hybrid
            }
        }
    
    def get_hnsw_config(self) -> dict:
        """Get HNSW index configuration."""
        return {
            "m": self.hnsw_m,
            "ef_construction": self.hnsw_ef_construction
        }
    
    def get_bulgarian_config(self) -> dict:
        """Get Bulgarian language processing configuration."""
        return {
            "stopwords": self.bulgarian_stopwords,
            "embedding_model": self.embedding_model,
            "language_code": "bg"
        }

# Global settings instance
settings = Settings()
