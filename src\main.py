"""
EU Funds MCP Server - Main Application

This is the main entry point for the EU Funds MCP Server.
Implements FastMCP server with Bulgarian language support and enterprise-grade patterns.
"""

import asyncio
import logging
import sys
from typing import List, Optional, Dict, Any
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import mcp
from mcp.server.fastmcp import FastMCP

from src.core.config import settings
from src.core.models import (
    SearchRequest, SearchResult, FundingProgram, EligibilityAnalysis,
    DeadlineInfo, DataSource, HealthStatus, ErrorResponse
)

# Configure structured logging
logging.basicConfig(
    level=getattr(logging, settings.log_level),
    format=settings.log_format
)
logger = logging.getLogger(__name__)

# Global instances - will be initialized when needed
db_manager: Optional[Any] = None
embedding_processor: Optional[Any] = None
search_engine: Optional[Any] = None

# Create FastMCP app
app = FastMCP("EU Funds MCP Server")

# Initialize global components on startup
async def initialize_components():
    """Initialize global components."""
    global db_manager, embedding_processor, search_engine

    logger.info("🚀 Initializing EU Funds MCP Server...")

    try:
        # TODO: Initialize database connection
        # db_manager = DatabaseManager(settings)
        # await db_manager.initialize()
        logger.info("✅ Database connection placeholder ready")

        # TODO: Initialize embedding processor
        # embedding_processor = EmbeddingProcessor(settings)
        # await embedding_processor.initialize()
        logger.info("✅ Embedding processor placeholder ready")

        # TODO: Initialize search engine
        # search_engine = HybridSearchEngine(db_manager, embedding_processor, settings)
        # await search_engine.initialize()
        logger.info("✅ Search engine placeholder ready")

        logger.info("🎉 EU Funds MCP Server initialization complete")

    except Exception as e:
        logger.error(f"❌ Failed to initialize server: {e}")
        raise

# Initialize components when module is imported
# asyncio.create_task(initialize_components())

@app.tool()
async def search_eu_funds(request: SearchRequest) -> List[SearchResult]:
    """
    Search for EU funding information with Bulgarian language support.
    
    Example queries:
    - "програми за финансиране на МСП" (SME funding programs)
    - "Horizon Europe Bulgaria eligibility"
    - "структурни фондове 2021-2027"
    
    Args:
        request: Search request with query, optional source filter, and limit
        
    Returns:
        List of search results with relevance scores
    """
    try:
        logger.info(f"🔍 Searching EU funds: {request.query}")
        
        # TODO: Implement actual search when search_engine is ready
        # if not search_engine:
        #     raise RuntimeError("Search engine not initialized")
        # 
        # results = await search_engine.search(
        #     query=request.query,
        #     source_filter=request.source_filter,
        #     limit=request.limit
        # )
        
        # Placeholder implementation
        mock_results = [
            SearchResult(
                title="Horizon Europe - България",
                content="Horizon Europe е най-голямата програма на ЕС за изследвания и иновации...",
                source="https://ec.europa.eu/info/research-and-innovation/funding/funding-opportunities/funding-programmes-and-open-calls/horizon-europe_bg",
                relevance_score=0.95,
                metadata={"language": "bg", "program_type": "research"}
            ),
            SearchResult(
                title="Структурни фондове 2021-2027",
                content="Оперативните програми за периода 2021-2027 в България...",
                source="https://www.eufunds.bg/bg/operational-programmes",
                relevance_score=0.88,
                metadata={"language": "bg", "program_type": "structural"}
            )
        ]
        
        logger.info(f"✅ Found {len(mock_results)} results for query: {request.query}")
        return mock_results
        
    except Exception as e:
        logger.error(f"❌ Search failed: {e}")
        raise HTTPException(status_code=500, detail=f"Search operation failed: {str(e)}")

@app.tool()
async def get_funding_programs(
    category: Optional[str] = None,
    deadline_after: Optional[str] = None
) -> List[FundingProgram]:
    """
    Get available EU funding programs with optional filtering.
    
    Args:
        category: Program category (e.g., "research", "agriculture", "digital")
        deadline_after: ISO date string to filter programs with deadlines after this date
    
    Returns:
        List of funding programs with details
        
    Example usage:
    - get_funding_programs(category="research")
    - get_funding_programs(deadline_after="2025-03-01")
    """
    try:
        logger.info(f"📋 Getting funding programs: category={category}, deadline_after={deadline_after}")
        
        # TODO: Implement actual database query when db_manager is ready
        # if not db_manager:
        #     raise RuntimeError("Database manager not initialized")
        # 
        # programs = await db_manager.get_funding_programs(
        #     category=category,
        #     deadline_after=deadline_after
        # )
        
        # Placeholder implementation
        mock_programs = [
            FundingProgram(
                name="Horizon Europe / Хоризонт Европа",
                description="Програма за изследвания и иновации на ЕС за периода 2021-2027",
                eligibility=[
                    "Университети и изследователски институции",
                    "Малки и средни предприятия (МСП)",
                    "Големи предприятия"
                ],
                deadline="2025-04-15",
                budget="95.5 млрд. евро",
                contact="<EMAIL>"
            ),
            FundingProgram(
                name="Digital Europe Programme / Програма Цифрова Европа",
                description="Програма за цифрова трансформация и технологии",
                eligibility=[
                    "Публични организации",
                    "Частни предприятия",
                    "НПО и изследователски центрове"
                ],
                deadline="2025-05-30",
                budget="7.6 млрд. евро",
                contact="<EMAIL>"
            )
        ]
        
        logger.info(f"✅ Found {len(mock_programs)} funding programs")
        return mock_programs
        
    except Exception as e:
        logger.error(f"❌ Failed to get funding programs: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve funding programs: {str(e)}")

@app.tool()
async def analyze_eligibility(
    program_name: str,
    organization_type: str,
    location: str = "Bulgaria"
) -> EligibilityAnalysis:
    """
    Analyze eligibility for a specific EU funding program.
    
    Args:
        program_name: Name of the funding program
        organization_type: Type of organization (e.g., "SME", "university", "NGO")
        location: Location/country (default: "Bulgaria")
    
    Returns:
        Detailed eligibility analysis with recommendations
    """
    try:
        logger.info(f"🔍 Analyzing eligibility: {program_name} for {organization_type} in {location}")
        
        # TODO: Implement actual eligibility analysis when search_engine is ready
        # Placeholder implementation
        analysis = EligibilityAnalysis(
            program_name=program_name,
            organization_type=organization_type,
            location=location,
            eligible=True,
            confidence=0.85,
            requirements=[
                "Организацията трябва да е регистрирана в България",
                "Минимум 2 години дейност",
                "Релевантен опит в проектната област"
            ],
            recommendations=[
                "Подгответе детайлно проектно предложение",
                "Осигурете съответствие с правилата за държавна помощ",
                "Разгледайте възможности за партньорство"
            ],
            supporting_documents=[
                "https://ec.europa.eu/info/funding-tenders/opportunities/docs/2021-2027/horizon/guidance/programme-guide_horizon_en.pdf",
                "https://www.eufunds.bg/bg/node/7890"
            ]
        )
        
        logger.info(f"✅ Eligibility analysis complete for {program_name}")
        return analysis
        
    except Exception as e:
        logger.error(f"❌ Eligibility analysis failed: {e}")
        raise HTTPException(status_code=500, detail=f"Eligibility analysis failed: {str(e)}")

@app.tool()
async def health_check() -> HealthStatus:
    """Health check endpoint for monitoring."""
    try:
        # Check component status
        components = {
            "database": "placeholder" if not db_manager else "healthy",
            "embeddings": "placeholder" if not embedding_processor else "healthy",
            "search": "placeholder" if not search_engine else "healthy",
            "config": "healthy"
        }
        
        # TODO: Add actual health checks when components are implemented
        # if db_manager:
        #     await db_manager.health_check()
        # if embedding_processor:
        #     await embedding_processor.health_check()
        
        return HealthStatus(
            status="healthy",
            timestamp=asyncio.get_event_loop().time(),
            components=components,
            version="0.1.0"
        )
    except Exception as e:
        logger.error(f"❌ Health check failed: {e}")
        return HealthStatus(
            status="unhealthy",
            timestamp=asyncio.get_event_loop().time(),
            components={"error": str(e)},
            version="0.1.0"
        )

if __name__ == "__main__":
    import uvicorn
    
    logger.info(f"🚀 Starting EU Funds MCP Server on {settings.host}:{settings.port}")
    
    # Run the server
    uvicorn.run(
        "src.main:app",
        host=settings.host,
        port=settings.port,
        log_level=settings.log_level.lower(),
        reload=settings.debug
    )
