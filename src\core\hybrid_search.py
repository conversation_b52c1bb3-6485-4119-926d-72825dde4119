"""
Hybrid Search Engine for EU Funds MCP Server
Combines vector similarity search with full-text search for optimal results
"""

import logging
import asyncio
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import numpy as np
from collections import defaultdict

from src.core.config import settings
from src.core.vector_store import VectorStore
from src.core.embeddings import EmbeddingProcessor
from src.core.models import (
    HybridSearchResult,
    VectorSearchResult,
    SearchMetrics
)

# Cross-encoder for reranking
try:
    from sentence_transformers import CrossEncoder
    CROSS_ENCODER_AVAILABLE = True
except ImportError:
    CROSS_ENCODER_AVAILABLE = False
    logger.warning("⚠️ CrossEncoder not available - reranking disabled")

logger = logging.getLogger(__name__)

class HybridSearchEngine:
    """
    Advanced hybrid search engine combining:
    - Vector similarity search for semantic understanding
    - Full-text search for exact keyword matching
    - Reciprocal Rank Fusion (RRF) for result combination
    - Bulgarian language optimization
    """
    
    def __init__(self):
        """Initialize hybrid search engine."""
        self.vector_store = VectorStore()
        self.embedding_processor = EmbeddingProcessor()
        self.initialized = False

        # Search configuration with user-specified weights
        self.default_weights = {
            "vector": 0.7,      # Hybrid search weight (user specified: 0.7)
            "text": 0.3,        # Keyword matching weight
            "cross_encoder": 0.3  # Cross-encoder weight (user specified: 0.3)
        }

        # Cross-encoder for reranking
        self.cross_encoder = None
        self.cross_encoder_model_name = "cross-encoder/ms-marco-MiniLM-L-6-v2"

        # Bulgarian language optimization
        self.bulgarian_boost = 1.2  # Boost for Bulgarian content
        self.funding_terms_boost = 1.3  # Boost for funding-related terms
        
    async def initialize(self) -> bool:
        """Initialize all search components."""
        try:
            logger.info("🔍 Initializing hybrid search engine...")
            
            # Initialize vector store
            try:
                await self.vector_store.initialize()
            except Exception as e:
                raise Exception(f"Vector store initialization failed: {e}")

            # Initialize embedding processor
            try:
                await self.embedding_processor.initialize()
            except Exception as e:
                raise Exception(f"Embedding processor initialization failed: {e}")

            # Initialize cross-encoder for reranking
            await self._initialize_cross_encoder()

            self.initialized = True
            logger.info("✅ Hybrid search engine initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Hybrid search initialization failed: {e}")
            return False

    async def _initialize_cross_encoder(self):
        """Initialize cross-encoder model for reranking."""
        try:
            if not CROSS_ENCODER_AVAILABLE:
                logger.warning("⚠️ CrossEncoder not available - skipping reranking initialization")
                return

            logger.info(f"🧠 Loading cross-encoder model: {self.cross_encoder_model_name}")

            # Load model in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            self.cross_encoder = await loop.run_in_executor(
                None,
                lambda: CrossEncoder(self.cross_encoder_model_name)
            )

            logger.info("✅ Cross-encoder model loaded successfully")

        except Exception as e:
            logger.error(f"❌ Failed to load cross-encoder: {e}")
            self.cross_encoder = None

    async def search(
        self,
        query: str,
        limit: int = 10,
        search_type: str = "hybrid",
        weights: Dict[str, float] = None,
        filters: Dict[str, Any] = None,
        similarity_threshold: float = 0.3
    ) -> HybridSearchResult:
        """
        Perform hybrid search combining vector and text search.
        
        Args:
            query: Search query in Bulgarian or English
            limit: Maximum number of results
            search_type: "hybrid", "vector", or "text"
            weights: Custom weights for search components
            filters: Additional filters (language, date, etc.)
            similarity_threshold: Minimum similarity score
        """
        try:
            start_time = datetime.utcnow()
            
            if not self.initialized:
                await self.initialize()
            
            # Use provided weights or defaults
            search_weights = weights or self.default_weights.copy()
            
            logger.info(f"🔍 Performing {search_type} search: '{query[:50]}...'")
            
            if search_type == "vector":
                return await self._vector_only_search(query, limit, similarity_threshold, filters)
            elif search_type == "text":
                return await self._text_only_search(query, limit, filters)
            else:  # hybrid
                return await self._hybrid_search(query, limit, search_weights, similarity_threshold, filters)
                
        except Exception as e:
            logger.error(f"❌ Search failed: {e}")
            raise
    
    async def _vector_only_search(
        self,
        query: str,
        limit: int,
        similarity_threshold: float,
        filters: Dict[str, Any] = None
    ) -> HybridSearchResult:
        """Perform vector-only semantic search."""
        try:
            # Generate query embedding
            query_result = await self.embedding_processor.embed_text(
                query,
                is_query=True,
                use_bulgarian_prefix=True
            )
            
            # Perform vector search
            vector_results = await self.vector_store.vector_search(
                query_embedding=query_result.vector,
                limit=limit,
                similarity_threshold=similarity_threshold,
                filters=filters
            )
            
            # Convert SearchResult objects to dict format for processing
            vector_results_dict = []
            for result in vector_results:
                vector_results_dict.append({
                    'content_id': result.source,  # Use source as ID
                    'content': result.content,
                    'similarity_score': result.relevance_score,
                    'metadata': result.metadata,
                    'source_url': result.source
                })

            # Apply Bulgarian and funding term boosts
            boosted_results = self._apply_content_boosts(vector_results_dict, query)

            processing_time = (datetime.utcnow() - datetime.fromisoformat(query_result.timestamp.replace('Z', '+00:00'))).total_seconds()

            return HybridSearchResult(
                results=boosted_results[:limit],
                total_results=len(boosted_results),
                vector_results=len(vector_results),
                text_results=0,
                processing_time=processing_time,
                fusion_method="vector_only",
                weights={"vector": 1.0, "text": 0.0}
            )
            
        except Exception as e:
            logger.error(f"❌ Vector search failed: {e}")
            raise
    
    async def _text_only_search(
        self,
        query: str,
        limit: int,
        filters: Dict[str, Any] = None
    ) -> HybridSearchResult:
        """Perform text-only keyword search."""
        try:
            # Use Supabase full-text search
            search_params = {
                'search_query': query,
                'match_count': limit
            }
            
            if filters:
                search_params.update(filters)
            
            # Execute text search via Supabase function
            response = self.vector_store.supabase.rpc('search_eu_funds_text', search_params).execute()
            
            results = []
            for item in response.data:
                results.append({
                    'content_id': item['id'],
                    'content': item['content'],
                    'similarity_score': item.get('rank', 0.5),  # Text search rank
                    'metadata': item['metadata'],
                    'source_url': item['source_url']
                })
            
            # Apply content boosts
            boosted_results = self._apply_content_boosts(results, query)
            
            return HybridSearchResult(
                results=boosted_results[:limit],
                total_results=len(boosted_results),
                vector_results=0,
                text_results=len(results),
                processing_time=0.1,  # Approximate text search time
                fusion_method="text_only",
                weights={"vector": 0.0, "text": 1.0}
            )
            
        except Exception as e:
            logger.error(f"❌ Text search failed: {e}")
            raise
    
    async def _hybrid_search(
        self,
        query: str,
        limit: int,
        weights: Dict[str, float],
        similarity_threshold: float,
        filters: Dict[str, Any] = None
    ) -> HybridSearchResult:
        """Perform hybrid search with Reciprocal Rank Fusion."""
        try:
            # Perform both searches concurrently
            vector_task = self._vector_only_search(query, limit * 2, similarity_threshold, filters)
            text_task = self._text_only_search(query, limit * 2, filters)
            
            vector_result, text_result = await asyncio.gather(vector_task, text_task)
            
            # Apply Reciprocal Rank Fusion
            fused_results = self._reciprocal_rank_fusion(
                vector_results=vector_result.results,
                text_results=text_result.results,
                weights=weights,
                k=60  # RRF parameter
            )
            
            # Apply cross-encoder reranking if available
            if self.cross_encoder and weights.get('cross_encoder', 0) > 0:
                reranked_results = await self._cross_encoder_rerank(
                    query=query,
                    results=fused_results,
                    cross_encoder_weight=weights.get('cross_encoder', 0.3),
                    top_k=min(limit * 3, len(fused_results))  # Rerank top candidates
                )
            else:
                reranked_results = fused_results

            # Apply final content boosts
            final_results = self._apply_content_boosts(reranked_results, query)

            total_processing_time = max(vector_result.processing_time, text_result.processing_time)
            
            return HybridSearchResult(
                results=final_results[:limit],
                total_results=len(final_results),
                vector_results=vector_result.vector_results,
                text_results=text_result.text_results,
                processing_time=total_processing_time,
                fusion_method="reciprocal_rank_fusion",
                weights=weights
            )
            
        except Exception as e:
            logger.error(f"❌ Hybrid search failed: {e}")
            raise
    
    def _reciprocal_rank_fusion(
        self,
        vector_results: List[Dict[str, Any]],
        text_results: List[Dict[str, Any]],
        weights: Dict[str, float],
        k: int = 60
    ) -> List[Dict[str, Any]]:
        """
        Apply Reciprocal Rank Fusion to combine search results.
        RRF formula: score = Σ(weight / (k + rank))
        """
        try:
            # Create content ID to result mapping
            all_results = {}
            
            # Process vector results
            for rank, result in enumerate(vector_results, 1):
                content_id = result['content_id']
                rrf_score = weights.get('vector', 0.7) / (k + rank)
                
                if content_id not in all_results:
                    all_results[content_id] = result.copy()
                    all_results[content_id]['rrf_score'] = rrf_score
                    all_results[content_id]['vector_rank'] = rank
                    all_results[content_id]['text_rank'] = None
                else:
                    all_results[content_id]['rrf_score'] += rrf_score
                    all_results[content_id]['vector_rank'] = rank
            
            # Process text results
            for rank, result in enumerate(text_results, 1):
                content_id = result['content_id']
                rrf_score = weights.get('text', 0.3) / (k + rank)
                
                if content_id not in all_results:
                    all_results[content_id] = result.copy()
                    all_results[content_id]['rrf_score'] = rrf_score
                    all_results[content_id]['vector_rank'] = None
                    all_results[content_id]['text_rank'] = rank
                else:
                    all_results[content_id]['rrf_score'] += rrf_score
                    all_results[content_id]['text_rank'] = rank
            
            # Sort by RRF score
            sorted_results = sorted(
                all_results.values(),
                key=lambda x: x['rrf_score'],
                reverse=True
            )
            
            logger.info(f"✅ RRF fusion: {len(sorted_results)} unique results")
            return sorted_results
            
        except Exception as e:
            logger.error(f"❌ RRF fusion failed: {e}")
            return []
    
    def _apply_content_boosts(
        self,
        results: List[Dict[str, Any]],
        query: str
    ) -> List[Dict[str, Any]]:
        """Apply content-specific boosts for Bulgarian and funding terms."""
        try:
            boosted_results = []
            query_lower = query.lower()
            
            # Bulgarian funding terms for boosting
            funding_terms = [
                'програма', 'финансиране', 'кандидатстване', 'проект',
                'европейски', 'фонд', 'грант', 'субсидия', 'безвъзмездна'
            ]
            
            for result in results:
                boosted_result = result.copy()
                boost_factor = 1.0
                
                # Bulgarian content boost
                if result.get('metadata', {}).get('language') == 'bg':
                    boost_factor *= self.bulgarian_boost
                
                # Funding terms boost
                content_lower = result.get('content', '').lower()
                funding_matches = sum(1 for term in funding_terms if term in content_lower)
                if funding_matches > 0:
                    boost_factor *= (self.funding_terms_boost ** min(funding_matches, 3))
                
                # Query term boost
                query_terms = query_lower.split()
                query_matches = sum(1 for term in query_terms if term in content_lower)
                if query_matches > 0:
                    boost_factor *= (1.1 ** min(query_matches, 5))
                
                # Apply boost to score
                original_score = result.get('similarity_score', result.get('rrf_score', 0.5))
                boosted_result['similarity_score'] = min(original_score * boost_factor, 1.0)
                boosted_result['boost_factor'] = boost_factor
                
                boosted_results.append(boosted_result)
            
            # Re-sort by boosted scores
            boosted_results.sort(key=lambda x: x['similarity_score'], reverse=True)
            
            return boosted_results
            
        except Exception as e:
            logger.error(f"❌ Content boost failed: {e}")
            return results

    async def _cross_encoder_rerank(
        self,
        query: str,
        results: List[Dict[str, Any]],
        cross_encoder_weight: float = 0.3,
        top_k: int = 20
    ) -> List[Dict[str, Any]]:
        """
        Rerank search results using cross-encoder model.

        This implements state-of-the-art reranking using cross-encoder/ms-marco-MiniLM-L-6-v2
        which can significantly improve relevance by considering query-document interactions.
        """
        try:
            if not self.cross_encoder or len(results) == 0:
                return results

            logger.debug(f"🧠 Cross-encoder reranking {len(results)} results...")

            # Take top candidates for reranking (computational efficiency)
            candidates = results[:top_k]

            # Prepare query-document pairs for cross-encoder
            query_doc_pairs = []
            for result in candidates:
                # Use content or title for reranking
                document_text = result.get('content', result.get('title', ''))[:512]  # Limit length
                query_doc_pairs.append([query, document_text])

            # Get cross-encoder scores in thread pool
            loop = asyncio.get_event_loop()
            cross_encoder_scores = await loop.run_in_executor(
                None,
                lambda: self.cross_encoder.predict(query_doc_pairs)
            )

            # Combine original scores with cross-encoder scores
            reranked_results = []
            for i, result in enumerate(candidates):
                result_copy = result.copy()

                # Get original score
                original_score = result.get('similarity_score', result.get('rrf_score', 0.5))

                # Get cross-encoder score (normalize from logits to 0-1 range)
                ce_score = float(cross_encoder_scores[i])
                ce_score_normalized = 1.0 / (1.0 + np.exp(-ce_score))  # Sigmoid normalization

                # Combine scores using user-specified weights
                # User specified: CE: 0.3, Hybrid: 0.7
                hybrid_weight = 1.0 - cross_encoder_weight
                final_score = (hybrid_weight * original_score) + (cross_encoder_weight * ce_score_normalized)

                result_copy['similarity_score'] = final_score
                result_copy['cross_encoder_score'] = ce_score_normalized
                result_copy['original_score'] = original_score

                reranked_results.append(result_copy)

            # Add remaining results without reranking
            reranked_results.extend(results[top_k:])

            # Sort by final combined score
            reranked_results.sort(key=lambda x: x['similarity_score'], reverse=True)

            logger.debug(f"✅ Cross-encoder reranking complete")
            return reranked_results

        except Exception as e:
            logger.error(f"❌ Cross-encoder reranking failed: {e}")
            return results  # Fallback to original results

    async def cleanup(self):
        """Clean up search engine resources."""
        try:
            await self.vector_store.cleanup()
            await self.embedding_processor.cleanup()
            logger.info("✅ Hybrid search engine cleanup complete")
            
        except Exception as e:
            logger.error(f"❌ Search engine cleanup failed: {e}")
