"""
Embedding Processing for EU Funds MCP Server

This module provides multilingual-e5-large-instruct integration with
batch processing, Bulgarian optimization, and vector storage.
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional, Tuple
import numpy as np
from datetime import datetime
import json

import openai
from sentence_transformers import SentenceTransformer
import torch

from src.core.config import settings
from src.core.models import EmbeddingResult, BatchEmbeddingResult, EmbeddingMetrics
from src.core.database import db_manager

logger = logging.getLogger(__name__)

class EmbeddingProcessor:
    """
    Advanced embedding processor optimized for Bulgarian EU funding content.
    
    Features:
    - multilingual-e5-large-instruct for Bulgarian optimization
    - Batch processing for efficiency
    - Query vs document embedding strategies
    - Vector storage optimization in Supabase
    - Embedding quality validation
    """
    
    def __init__(self):
        """Initialize embedding processor with optimized configuration."""
        self.model: Optional[SentenceTransformer] = None
        self.openai_client: Optional[openai.AsyncOpenAI] = None
        self.initialized = False
        
        # Model configuration
        self.model_name = settings.embedding_model  # multilingual-e5-large
        self.dimensions = settings.embedding_dimensions  # 1024
        self.max_batch_size = 32
        self.max_sequence_length = 512
        
        # Bulgarian optimization settings
        self.bulgarian_prefixes = {
            "query": "query: ",
            "document": "passage: ",
            "funding_query": "Търсене на финансиране: ",
            "funding_document": "Документ за финансиране: "
        }
        
        # Performance tracking
        self.embedding_cache = {}
        self.cache_max_size = 1000
        self.stats = {
            "total_embeddings": 0,
            "cache_hits": 0,
            "batch_operations": 0,
            "avg_processing_time": 0.0
        }
    
    async def initialize(self) -> None:
        """Initialize embedding models and clients."""
        try:
            logger.info("🧠 Initializing embedding processor...")
            
            # Initialize OpenAI client for fallback
            self.openai_client = openai.AsyncOpenAI(
                api_key=settings.openai_api_key
            )
            
            # Initialize SentenceTransformer model
            logger.info(f"📥 Loading {self.model_name}...")
            
            # Check if CUDA is available
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
            logger.info(f"🔧 Using device: {self.device}")

            # Load model with optimized settings
            self.model = SentenceTransformer(
                self.model_name,
                device=self.device,
                trust_remote_code=True
            )
            
            # Optimize model for inference
            self.model.eval()
            if self.device == "cuda":
                self.model.half()  # Use FP16 for faster inference
            
            # Test embedding generation directly (avoid recursion)
            test_text = "Тест за българско финансиране от ЕС"
            test_vector = self.model.encode([test_text], convert_to_tensor=False)[0]

            if test_vector is not None and len(test_vector) == self.dimensions:
                logger.info(f"✅ Embedding processor initialized: {self.dimensions}D vectors")
                self.initialized = True
            else:
                raise ValueError("Test embedding failed")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize embedding processor: {e}")
            raise
    
    async def embed_text(
        self,
        text: str,
        is_query: bool = False,
        use_bulgarian_prefix: bool = True,
        cache_key: Optional[str] = None
    ) -> Optional[EmbeddingResult]:
        """
        Generate embedding for single text.
        
        Args:
            text: Text to embed
            is_query: Whether this is a query (vs document)
            use_bulgarian_prefix: Use Bulgarian-specific prefixes
            cache_key: Optional cache key for reuse
            
        Returns:
            EmbeddingResult with vector and metadata
        """
        if not self.initialized:
            await self.initialize()
        
        try:
            # Check for empty or None text
            if text is None or not text or not text.strip():
                return EmbeddingResult(
                    success=False,
                    text=str(text) if text is not None else "",
                    contextual_text=None,
                    vector=None,
                    dimensions=0,
                    model_name=self.model_name,
                    processing_time=0.0,
                    is_query=is_query,
                    language="unknown",
                    timestamp=datetime.now().isoformat(),
                    error="Empty or None text provided"
                )

            start_time = asyncio.get_event_loop().time()

            # Check cache first
            if cache_key and cache_key in self.embedding_cache:
                self.stats["cache_hits"] += 1
                return self.embedding_cache[cache_key]
            
            # Prepare text with appropriate prefix
            processed_text = self._prepare_text_for_embedding(
                text, is_query, use_bulgarian_prefix
            )
            
            # Generate embedding
            vector = await self._generate_embedding(processed_text)
            
            if vector is None:
                return None
            
            # Calculate processing time
            processing_time = asyncio.get_event_loop().time() - start_time
            
            # Create result
            result = EmbeddingResult(
                success=True,
                text=text,
                contextual_text=None,  # No context for regular embedding
                vector=vector.tolist(),
                dimensions=len(vector),
                model_name=self.model_name,
                processing_time=processing_time,
                is_query=is_query,
                language=self._detect_language(text),
                timestamp=datetime.now().isoformat()
            )
            
            # Update cache
            if cache_key:
                self._update_cache(cache_key, result)
            
            # Update stats
            self.stats["total_embeddings"] += 1
            self._update_avg_processing_time(processing_time)
            
            logger.debug(f"✅ Generated embedding: {len(vector)}D in {processing_time:.3f}s")
            return result
            
        except Exception as e:
            logger.error(f"❌ Failed to generate embedding: {e}")
            return EmbeddingResult(
                success=False,
                text=str(text) if text is not None else "",
                contextual_text=None,
                vector=None,
                dimensions=0,
                model_name=self.model_name,
                processing_time=0.0,
                is_query=is_query,
                language=self._detect_language(text) if text else "unknown",
                timestamp=datetime.now().isoformat(),
                error=str(e)
            )

    async def embed_text_with_context(
        self,
        text: str,
        document_context: Optional[str] = None,
        is_query: bool = False,
        use_bulgarian_prefix: bool = True,
        cache_key: Optional[str] = None
    ) -> Optional[EmbeddingResult]:
        """
        Generate contextual embedding using Anthropic's contextual retrieval technique.

        This revolutionary approach adds explanatory context to each chunk before embedding,
        reducing retrieval failures by 49-67% according to Anthropic's research.

        Args:
            text: The text to embed
            document_context: Additional context about the document (title, source, etc.)
            is_query: Whether this is a query (vs document)
            use_bulgarian_prefix: Use Bulgarian-specific prefixes
            cache_key: Optional cache key for performance

        Returns:
            EmbeddingResult with contextual embedding
        """
        if not self.initialized:
            await self.initialize()

        try:
            start_time = asyncio.get_event_loop().time()

            # Create contextual text using Anthropic's technique
            contextual_text = self._create_contextual_text(text, document_context)

            # Check cache first (with contextual key)
            contextual_cache_key = f"ctx_{cache_key}_{hash(contextual_text)}" if cache_key else None
            if contextual_cache_key and contextual_cache_key in self.embedding_cache:
                self.stats["cache_hits"] += 1
                cached_result = self.embedding_cache[contextual_cache_key]
                logger.debug(f"📋 Cache hit for contextual embedding: {contextual_cache_key[:20]}...")
                return cached_result

            # Prepare text with appropriate prefix
            processed_text = self._prepare_text_for_embedding(
                contextual_text, is_query, use_bulgarian_prefix
            )

            # Generate embedding
            vector = await self._generate_embedding(processed_text)

            if vector is None:
                logger.warning(f"⚠️ Failed to generate contextual embedding for text: {text[:50]}...")
                return None

            processing_time = asyncio.get_event_loop().time() - start_time

            # Create result
            result = EmbeddingResult(
                success=True,
                vector=vector.tolist() if hasattr(vector, 'tolist') else vector,
                text=text,  # Store original text, not contextual
                contextual_text=contextual_text,  # Store contextual version
                dimensions=len(vector),
                model_name=self.model_name,
                processing_time=processing_time,
                is_query=is_query,
                language=self._detect_language(text),
                timestamp=datetime.now().isoformat()
            )

            # Cache result
            if contextual_cache_key:
                self._cache_embedding(contextual_cache_key, result)

            # Update stats
            self.stats["total_embeddings"] += 1
            self._update_avg_processing_time(processing_time)

            logger.debug(f"✅ Generated contextual embedding: {len(vector)}D in {processing_time:.3f}s")
            return result

        except Exception as e:
            logger.error(f"❌ Contextual embedding failed: {e}")
            return EmbeddingResult(
                success=False,
                vector=[],
                text=text,
                contextual_text=None,
                dimensions=0,
                model_name=self.model_name,
                processing_time=0.0,
                is_query=is_query,
                language=self._detect_language(text) if text else "unknown",
                timestamp=datetime.now().isoformat(),
                error=str(e)
            )

    async def embed_batch(
        self,
        texts: List[str],
        is_query: bool = False,
        use_bulgarian_prefix: bool = True,
        batch_size: Optional[int] = None
    ) -> BatchEmbeddingResult:
        """
        Generate embeddings for multiple texts efficiently.
        
        Args:
            texts: List of texts to embed
            is_query: Whether these are queries (vs documents)
            use_bulgarian_prefix: Use Bulgarian-specific prefixes
            batch_size: Custom batch size (default: self.max_batch_size)
            
        Returns:
            BatchEmbeddingResult with all embeddings and metrics
        """
        if not self.initialized:
            await self.initialize()
        
        try:
            start_time = asyncio.get_event_loop().time()
            batch_size = batch_size or self.max_batch_size
            
            logger.info(f"🔄 Processing batch of {len(texts)} texts...")
            
            # Process in batches
            all_embeddings = []
            failed_indices = []
            
            for i in range(0, len(texts), batch_size):
                batch_texts = texts[i:i + batch_size]
                
                # Prepare texts with prefixes
                processed_texts = [
                    self._prepare_text_for_embedding(text, is_query, use_bulgarian_prefix)
                    for text in batch_texts
                ]
                
                # Generate embeddings for batch
                batch_vectors = await self._generate_batch_embeddings(processed_texts)
                
                # Process results
                for j, (original_text, vector) in enumerate(zip(batch_texts, batch_vectors)):
                    # Check if original text was invalid (None or empty)
                    if original_text is None or (isinstance(original_text, str) and not original_text.strip()):
                        failed_indices.append(i + j)
                        continue

                    if vector is not None:
                        embedding = EmbeddingResult(
                            success=True,
                            text=str(original_text),
                            contextual_text=None,  # No context for batch embedding
                            vector=vector.tolist(),
                            dimensions=len(vector),
                            model_name=self.model_name,
                            processing_time=0.0,  # Will be calculated for batch
                            is_query=is_query,
                            language=self._detect_language(original_text),
                            timestamp=datetime.now().isoformat()
                        )
                        all_embeddings.append(embedding)
                    else:
                        failed_indices.append(i + j)
                
                # Small delay between batches to prevent overload
                if i + batch_size < len(texts):
                    await asyncio.sleep(0.1)
            
            # Calculate metrics
            total_time = asyncio.get_event_loop().time() - start_time
            success_rate = len(all_embeddings) / max(len(texts), 1)
            avg_time_per_embedding = total_time / max(len(all_embeddings), 1)
            
            # Update processing time for all embeddings
            for embedding in all_embeddings:
                embedding.processing_time = avg_time_per_embedding
            
            # Create batch result
            result = BatchEmbeddingResult(
                embeddings=all_embeddings,
                total_texts=len(texts),
                successful_embeddings=len(all_embeddings),
                failed_indices=failed_indices,
                total_processing_time=total_time,
                avg_time_per_embedding=avg_time_per_embedding,
                success_rate=success_rate,
                batch_size=batch_size,
                model_name=self.model_name,
                timestamp=datetime.now().isoformat()
            )
            
            # Update stats
            self.stats["batch_operations"] += 1
            self.stats["total_embeddings"] += len(all_embeddings)
            
            logger.info(f"✅ Batch completed: {len(all_embeddings)}/{len(texts)} successful in {total_time:.2f}s")
            return result
            
        except Exception as e:
            logger.error(f"❌ Batch embedding failed: {e}")
            raise
    
    def _prepare_text_for_embedding(
        self,
        text: str,
        is_query: bool,
        use_bulgarian_prefix: bool
    ) -> str:
        """Prepare text with appropriate prefixes for optimal embedding."""
        # Handle None or empty text
        if text is None or not text or not text.strip():
            return ""

        # Truncate if too long
        if len(text) > self.max_sequence_length * 4:  # Rough character estimate
            text = text[:self.max_sequence_length * 4]
        
        # Choose appropriate prefix
        if use_bulgarian_prefix:
            if self._detect_language(text) == "bg":
                prefix = self.bulgarian_prefixes["funding_query" if is_query else "funding_document"]
            else:
                prefix = self.bulgarian_prefixes["query" if is_query else "document"]
        else:
            prefix = self.bulgarian_prefixes["query" if is_query else "document"]
        
        return prefix + text
    
    async def _generate_embedding(self, text: str) -> Optional[np.ndarray]:
        """Generate single embedding using SentenceTransformer."""
        try:
            # Run in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            embedding = await loop.run_in_executor(
                None,
                lambda: self.model.encode(
                    text,
                    convert_to_tensor=False,
                    normalize_embeddings=True,
                    show_progress_bar=False
                )
            )
            
            return np.array(embedding, dtype=np.float32)
            
        except Exception as e:
            logger.error(f"❌ SentenceTransformer embedding failed: {e}")
            
            # Fallback to OpenAI if available
            try:
                return await self._generate_openai_embedding(text)
            except Exception as fallback_error:
                logger.error(f"❌ OpenAI fallback failed: {fallback_error}")
                return None
    
    async def _generate_batch_embeddings(self, texts: List[str]) -> List[Optional[np.ndarray]]:
        """Generate batch embeddings using SentenceTransformer."""
        try:
            # Run in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            embeddings = await loop.run_in_executor(
                None,
                lambda: self.model.encode(
                    texts,
                    convert_to_tensor=False,
                    normalize_embeddings=True,
                    show_progress_bar=False,
                    batch_size=min(len(texts), self.max_batch_size)
                )
            )
            
            return [np.array(emb, dtype=np.float32) for emb in embeddings]
            
        except Exception as e:
            logger.error(f"❌ Batch embedding failed: {e}")
            
            # Fallback to individual processing
            results = []
            for text in texts:
                embedding = await self._generate_embedding(text)
                results.append(embedding)
            
            return results
    
    async def _generate_openai_embedding(self, text: str) -> Optional[np.ndarray]:
        """Generate embedding using OpenAI as fallback."""
        try:
            response = await self.openai_client.embeddings.create(
                model="text-embedding-3-large",
                input=text,
                dimensions=self.dimensions
            )
            
            embedding = response.data[0].embedding
            return np.array(embedding, dtype=np.float32)
            
        except Exception as e:
            logger.error(f"❌ OpenAI embedding failed: {e}")
            return None
    
    def _detect_language(self, text: str) -> str:
        """Simple language detection for Bulgarian vs English."""
        if not text:
            return "unknown"

        cyrillic_chars = len([c for c in text if '\u0400' <= c <= '\u04FF'])
        total_chars = len([c for c in text if c.isalpha()])

        if total_chars == 0:
            return "unknown"

        cyrillic_ratio = cyrillic_chars / total_chars
        return "bg" if cyrillic_ratio > 0.3 else "en"
    
    def _update_cache(self, key: str, result: EmbeddingResult) -> None:
        """Update embedding cache with size limit."""
        if len(self.embedding_cache) >= self.cache_max_size:
            # Remove oldest entry (simple FIFO)
            oldest_key = next(iter(self.embedding_cache))
            del self.embedding_cache[oldest_key]
        
        self.embedding_cache[key] = result
    
    def _update_avg_processing_time(self, new_time: float) -> None:
        """Update average processing time with exponential moving average."""
        alpha = 0.1  # Smoothing factor
        if self.stats["avg_processing_time"] == 0.0:
            self.stats["avg_processing_time"] = new_time
        else:
            self.stats["avg_processing_time"] = (
                alpha * new_time + (1 - alpha) * self.stats["avg_processing_time"]
            )
    
    async def store_embeddings(
        self,
        embeddings: List[EmbeddingResult],
        metadata: Dict[str, Any]
    ) -> List[str]:
        """Store embeddings in Supabase with metadata."""
        try:
            logger.info(f"💾 Storing {len(embeddings)} embeddings...")
            
            stored_ids = []
            for embedding in embeddings:
                # Create document record
                doc_id = await db_manager.store_document(
                    title=metadata.get("title", ""),
                    content=embedding.text,
                    source=metadata.get("source", ""),
                    embedding=embedding.vector,
                    metadata={
                        "language": embedding.language,
                        "model": embedding.model,
                        "dimensions": embedding.dimensions,
                        "is_query": embedding.is_query,
                        "processing_time": embedding.processing_time,
                        **metadata
                    }
                )
                stored_ids.append(doc_id)
            
            logger.info(f"✅ Stored {len(stored_ids)} embeddings successfully")
            return stored_ids
            
        except Exception as e:
            logger.error(f"❌ Failed to store embeddings: {e}")
            raise
    
    def get_stats(self) -> EmbeddingMetrics:
        """Get embedding processing statistics."""
        return EmbeddingMetrics(
            total_embeddings=self.stats["total_embeddings"],
            cache_hits=self.stats["cache_hits"],
            cache_hit_rate=self.stats["cache_hits"] / max(self.stats["total_embeddings"], 1),
            batch_operations=self.stats["batch_operations"],
            avg_processing_time=self.stats["avg_processing_time"],
            model_name=self.model_name,
            dimensions=self.dimensions,
            cache_size=len(self.embedding_cache)
        )
    
    def calculate_similarity(self, vector1: List[float], vector2: List[float]) -> float:
        """
        Calculate cosine similarity between two vectors.

        Args:
            vector1: First embedding vector
            vector2: Second embedding vector

        Returns:
            Cosine similarity score between -1 and 1
        """
        try:
            import numpy as np
            from sklearn.metrics.pairwise import cosine_similarity

            # Convert to numpy arrays and reshape for sklearn
            v1 = np.array(vector1).reshape(1, -1)
            v2 = np.array(vector2).reshape(1, -1)

            # Calculate cosine similarity
            similarity = cosine_similarity(v1, v2)[0][0]
            return float(similarity)

        except Exception as e:
            logger.error(f"❌ Failed to calculate similarity: {e}")
            return 0.0

    def get_model_info(self) -> dict:
        """
        Get information about the current embedding model.

        Returns:
            Dictionary with model information
        """
        return {
            "model_name": self.model_name,
            "dimensions": self.dimensions,
            "max_sequence_length": self.max_sequence_length,
            "device": self.device,
            "initialized": self.initialized,
            "stats": self.stats.copy(),
            "cache_size": len(self.embedding_cache),
            "bulgarian_optimized": True
        }

    def _create_contextual_text(self, text: str, document_context: Optional[str] = None) -> str:
        """
        Create contextual text using Anthropic's contextual retrieval technique.

        This method adds explanatory context to each chunk before embedding,
        which significantly improves retrieval accuracy by providing more context
        about what the chunk contains and where it comes from.
        """
        # Base context for EU funding documents
        base_context = "Този текст е част от документ за европейско финансиране в България."

        # Add document-specific context if provided
        if document_context:
            base_context += f" {document_context}"

        # Analyze text content and add relevant context
        text_lower = text.lower()

        # Add context based on content type
        if any(term in text_lower for term in ["програма", "схема", "мярка"]):
            base_context += " Съдържа информация за финансови програми и схеми."

        if any(term in text_lower for term in ["бюджет", "сума", "евро", "лева", "финансиране"]):
            base_context += " Включва финансова информация и бюджети."

        if any(term in text_lower for term in ["условия", "критерии", "изисквания"]):
            base_context += " Описва условия и критерии за кандидатстване."

        if any(term in text_lower for term in ["срок", "дата", "до", "преди"]):
            base_context += " Съдържа важни срокове и дати."

        if any(term in text_lower for term in ["мсп", "малки и средни предприятия", "предприемачество"]):
            base_context += " Фокусира се върху малки и средни предприятия."

        if any(term in text_lower for term in ["иновации", "технологии", "изследвания"]):
            base_context += " Свързано с иновации и научни изследвания."

        # Combine context with original text
        contextual_text = f"{base_context}\n\nСъдържание: {text}"

        return contextual_text

    async def cleanup(self) -> None:
        """Cleanup embedding processor resources."""
        try:
            logger.info("🧹 Cleaning up embedding processor...")
            
            # Clear cache
            self.embedding_cache.clear()
            
            # Clear model from memory
            if self.model:
                del self.model
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
            
            self.initialized = False
            logger.info("✅ Embedding processor cleanup complete")
            
        except Exception as e:
            logger.error(f"❌ Embedding processor cleanup failed: {e}")

# Global embedding processor instance
embedding_processor = EmbeddingProcessor()
