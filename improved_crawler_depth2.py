#!/usr/bin/env python3
"""
Improved crawler for depth 2 with proper error handling and more comprehensive crawling.
"""

import asyncio
import sys
import os
from datetime import datetime
import json
from typing import List, Dict, Any

sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.crawler.eu_funds_crawler import EUFundsCrawler

class ImprovedDepth2Crawler:
    """Improved crawler that actually crawls multiple pages with depth 2."""
    
    def __init__(self):
        self.crawler = EUFundsCrawler()
        
        # Configure for depth 2 with more comprehensive coverage
        self.crawler.crawl_depth = 2
        self.crawler.max_pages = 50  # Reasonable limit for depth 2
        self.crawler.delay = 1.0
        self.crawler.timeout = 30
        
        # Expand the URL list for better coverage
        self.crawler.test_urls = [
            # Main Bulgarian pages
            "https://www.eufunds.bg/bg",
            "https://www.eufunds.bg/bg/programi",
            "https://www.eufunds.bg/bg/novini", 
            "https://www.eufunds.bg/bg/sabitiya",
            
            # Specific program pages
            "https://www.eufunds.bg/bg/opik",
            "https://www.eufunds.bg/bg/oprd", 
            "https://www.eufunds.bg/bg/opnoir",
            "https://www.eufunds.bg/bg/oprhr",
            
            # Additional EU sources (limited)
            "https://ec.europa.eu/info/funding-tenders_en",
        ]
        
        self.crawl_stats = {
            "start_time": None,
            "end_time": None,
            "pages_attempted": 0,
            "pages_successful": 0,
            "chunks_stored": 0,
            "errors": 0
        }
    
    async def run_comprehensive_crawling(self) -> Dict[str, Any]:
        """Run comprehensive crawling with depth 2."""
        print("🚀 СТАРТИРАНЕ НА ПОДОБРЕНО CRAWLING - DEPTH 2")
        print("=" * 60)
        
        self.crawl_stats["start_time"] = datetime.now()
        print(f"⏰ Започва в: {self.crawl_stats['start_time'].strftime('%H:%M:%S')}")
        
        print(f"\n⚙️ КОНФИГУРАЦИЯ:")
        print(f"   📊 Дълбочина: {self.crawler.crawl_depth}")
        print(f"   📄 Макс страници: {self.crawler.max_pages}")
        print(f"   🌐 Начални URL-и: {len(self.crawler.test_urls)}")
        print(f"   ⏱️ Delay: {self.crawler.delay}s")
        print()
        
        try:
            # Initialize crawler
            await self.crawler.initialize()
            
            # Process each URL
            all_results = []
            
            for i, url in enumerate(self.crawler.test_urls):
                print(f"📄 Обработка {i+1}/{len(self.crawler.test_urls)}: {url}")
                self.crawl_stats["pages_attempted"] += 1
                
                try:
                    # Crawl single URL (this will follow links based on depth)
                    result = await self._crawl_single_url(url)
                    
                    if result and result.get("success"):
                        all_results.append(result)
                        self.crawl_stats["pages_successful"] += 1
                        self.crawl_stats["chunks_stored"] += result.get("chunks_stored", 0)
                        print(f"   ✅ Успешно: {result.get('chunks_stored', 0)} chunks")
                    else:
                        self.crawl_stats["errors"] += 1
                        print(f"   ❌ Неуспешно")
                    
                    # Respectful delay
                    await asyncio.sleep(self.crawler.delay)
                    
                except Exception as e:
                    self.crawl_stats["errors"] += 1
                    print(f"   ❌ Грешка: {e}")
                    continue
            
            self.crawl_stats["end_time"] = datetime.now()
            
            # Generate final report
            return self._generate_final_report(all_results)
            
        except Exception as e:
            print(f"❌ КРИТИЧНА ГРЕШКА: {e}")
            self.crawl_stats["end_time"] = datetime.now()
            return self._generate_error_report(str(e))
    
    async def _crawl_single_url(self, url: str) -> Dict[str, Any]:
        """Crawl a single URL and return results."""
        try:
            # Use the existing crawler method but with better error handling
            if "eufunds.bg/bg" in url:
                result = await self.crawler.crawl_bulgarian_main_page()
            else:
                result = await self.crawler.crawl_test_sites()
            
            return {
                "success": True,
                "url": url,
                "chunks_stored": result.get("total_chunks", 0),
                "pages_crawled": result.get("pages_crawled", 1),
                "details": result
            }
            
        except Exception as e:
            return {
                "success": False,
                "url": url,
                "error": str(e),
                "chunks_stored": 0
            }
    
    def _generate_final_report(self, results: List[Dict]) -> Dict[str, Any]:
        """Generate comprehensive final report."""
        duration = (self.crawl_stats["end_time"] - self.crawl_stats["start_time"]).total_seconds()
        
        successful_results = [r for r in results if r.get("success")]
        total_chunks = sum(r.get("chunks_stored", 0) for r in successful_results)
        
        report = {
            "success": True,
            "summary": {
                "duration_seconds": duration,
                "pages_attempted": self.crawl_stats["pages_attempted"],
                "pages_successful": self.crawl_stats["pages_successful"],
                "total_chunks_stored": total_chunks,
                "errors": self.crawl_stats["errors"],
                "success_rate": (self.crawl_stats["pages_successful"] / self.crawl_stats["pages_attempted"] * 100) if self.crawl_stats["pages_attempted"] > 0 else 0
            },
            "detailed_results": results,
            "recommendations": self._generate_recommendations(total_chunks, self.crawl_stats["errors"])
        }
        
        return report
    
    def _generate_error_report(self, error: str) -> Dict[str, Any]:
        """Generate error report."""
        duration = (self.crawl_stats["end_time"] - self.crawl_stats["start_time"]).total_seconds() if self.crawl_stats["end_time"] else 0
        
        return {
            "success": False,
            "error": error,
            "summary": {
                "duration_seconds": duration,
                "pages_attempted": self.crawl_stats["pages_attempted"],
                "pages_successful": self.crawl_stats["pages_successful"],
                "errors": self.crawl_stats["errors"]
            }
        }
    
    def _generate_recommendations(self, total_chunks: int, errors: int) -> List[str]:
        """Generate recommendations based on results."""
        recommendations = []
        
        if total_chunks < 50:
            recommendations.append("Малко данни - препоръчвам увеличаване на depth или добавяне на повече URL-и")
        elif total_chunks > 200:
            recommendations.append("Много данни - отлично покритие за RAG система")
        else:
            recommendations.append("Добро количество данни за качествен RAG")
        
        if errors > 3:
            recommendations.append("Много грешки - проверете мрежовата връзка и URL-ите")
        elif errors == 0:
            recommendations.append("Перфектно изпълнение без грешки")
        
        return recommendations
    
    def print_final_report(self, report: Dict[str, Any]):
        """Print formatted final report."""
        print("\n🎉 CRAWLING ЗАВЪРШЕН!")
        print("=" * 50)
        
        if report["success"]:
            summary = report["summary"]
            print(f"⏱️ Общо време: {summary['duration_seconds']:.1f}s")
            print(f"📄 Опитани страници: {summary['pages_attempted']}")
            print(f"✅ Успешни страници: {summary['pages_successful']}")
            print(f"💾 Общо chunks: {summary['total_chunks_stored']}")
            print(f"❌ Грешки: {summary['errors']}")
            print(f"📊 Успеваемост: {summary['success_rate']:.1f}%")
            
            if report.get("recommendations"):
                print(f"\n💡 ПРЕПОРЪКИ:")
                for rec in report["recommendations"]:
                    print(f"   • {rec}")
        else:
            print(f"❌ CRAWLING НЕУСПЕШЕН: {report['error']}")

async def main():
    """Main function."""
    crawler = ImprovedDepth2Crawler()
    report = await crawler.run_comprehensive_crawling()
    crawler.print_final_report(report)
    
    # Save report to file
    with open(f"crawl_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json", "w", encoding="utf-8") as f:
        json.dump(report, f, ensure_ascii=False, indent=2, default=str)
    
    return report

if __name__ == "__main__":
    asyncio.run(main())
