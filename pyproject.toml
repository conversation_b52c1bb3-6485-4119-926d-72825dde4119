[project]
name = "eu-funds-mcp"
version = "0.1.0"
description = "Enterprise-grade MCP server for EU funding programs information in Bulgaria"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "crawl4ai==0.6.2",
    "mcp==1.7.1",
    "fastmcp==2.3.4",
    "supabase==2.15.1",
    "openai==1.71.0",
    "python-dotenv==1.0.1",
    "sentence-transformers>=4.1.0",
    "pydantic==2.10.5",
    "pydantic-settings==2.7.1",
    "fastapi==0.115.6",
    "uvicorn==0.34.0",
    "loguru==0.7.3",
    "asyncpg==0.30.0",
    "httpx==0.28.1",
    "beautifulsoup4==4.12.3",
    "lxml==5.3.0",
    "nltk==3.9.1",
    "scikit-learn==1.6.0",
    "numpy==2.2.1",
    "pandas==2.2.3",
    "aiofiles==24.1.0",
    "tenacity==9.0.0",
    "redis==5.2.1",
    "prometheus-client==0.21.1"
]

[project.optional-dependencies]
dev = [
    "pytest==8.3.4",
    "pytest-asyncio==0.25.0",
    "pytest-cov==6.0.0",
    "black==24.10.0",
    "isort==5.13.2",
    "flake8==7.1.1",
    "mypy==1.14.0",
    "pre-commit==4.0.1"
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.black]
line-length = 88
target-version = ['py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["eu_funds_mcp"]

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
asyncio_mode = "auto"
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "bulgarian: marks tests for Bulgarian language processing"
]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/.venv/*"
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:"
]
