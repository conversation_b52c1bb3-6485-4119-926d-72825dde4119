"""
Database Management for EU Funds MCP Server

This module provides Supabase integration with connection pooling,
pgvector support, and Bulgarian language optimizations.
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import json

from supabase import create_client, Client
from supabase.client import ClientOptions
import asyncpg
from pgvector.asyncpg import register_vector

from src.core.config import settings
from src.core.models import SearchResult, FundingProgram, DataSource, DatabaseMetrics

logger = logging.getLogger(__name__)

class DatabaseManager:
    """
    Database manager for Supabase with pgvector support.
    
    Handles connection pooling, schema management, and Bulgarian language
    optimized queries for EU funding information.
    """
    
    def __init__(self):
        """Initialize database manager with Supabase configuration."""
        self.supabase: Optional[Client] = None
        self.pool: Optional[asyncpg.Pool] = None
        self.initialized = False
        
        # Database configuration
        self.db_config = {
            "host": self._extract_host_from_url(settings.supabase_url),
            "port": 5432,
            "database": "postgres",
            "user": "postgres",
            "password": self._extract_password_from_service_key(),
            "min_size": 5,
            "max_size": settings.db_pool_size,
            "command_timeout": settings.db_timeout,
        }
    
    def _extract_host_from_url(self, url: str) -> str:
        """Extract database host from Supabase URL."""
        # Extract project ID from URL like https://jbdpiowmhaxghnzvhxse.supabase.co
        project_id = url.replace("https://", "").replace(".supabase.co", "")
        return f"db.{project_id}.supabase.co"
    
    def _extract_password_from_service_key(self) -> str:
        """Extract database password from service key (placeholder)."""
        # In production, this would decode the JWT to get the actual password
        # For now, we'll use a placeholder approach
        return "your_db_password_here"  # TODO: Implement proper JWT decoding
    
    async def initialize(self) -> None:
        """Initialize database connections and schema."""
        try:
            logger.info("🔌 Initializing Supabase client...")
            
            # Initialize Supabase client
            self.supabase = create_client(
                settings.supabase_url,
                settings.supabase_service_key,
                options=ClientOptions(
                    postgrest_client_timeout=settings.db_timeout,
                    storage_client_timeout=settings.db_timeout,
                )
            )
            
            # Test Supabase connection
            response = self.supabase.table("_health_check").select("*").limit(1).execute()
            logger.info("✅ Supabase client initialized successfully")
            
            # TODO: Initialize asyncpg pool for direct PostgreSQL access
            # This would be needed for pgvector operations
            # self.pool = await asyncpg.create_pool(**self.db_config)
            # await register_vector(self.pool)
            logger.info("✅ Database pool placeholder ready")
            
            # Initialize schema
            await self._ensure_schema()
            
            self.initialized = True
            logger.info("🎉 Database initialization complete")
            
        except Exception as e:
            logger.error(f"❌ Database initialization failed: {e}")
            raise
    
    async def _ensure_schema(self) -> None:
        """Ensure required database schema exists."""
        try:
            logger.info("📋 Ensuring database schema...")
            
            # TODO: Create tables if they don't exist
            # This would include:
            # - documents table with pgvector embeddings
            # - funding_programs table
            # - data_sources table
            # - search_logs table
            
            # For now, just log that schema check is complete
            logger.info("✅ Database schema placeholder ready")
            
        except Exception as e:
            logger.error(f"❌ Schema initialization failed: {e}")
            raise
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform database health check."""
        try:
            if not self.supabase:
                return {"status": "unhealthy", "error": "Supabase client not initialized"}
            
            # Test basic connectivity
            start_time = asyncio.get_event_loop().time()
            response = self.supabase.table("_health_check").select("*").limit(1).execute()
            end_time = asyncio.get_event_loop().time()
            
            return {
                "status": "healthy",
                "response_time": end_time - start_time,
                "connection_pool": "placeholder",
                "active_connections": "placeholder"
            }
            
        except Exception as e:
            logger.error(f"❌ Database health check failed: {e}")
            return {"status": "unhealthy", "error": str(e)}
    
    async def search_documents(
        self,
        query_embedding: List[float],
        text_query: str,
        limit: int = 10,
        similarity_threshold: float = 0.7,
        source_filter: Optional[str] = None
    ) -> List[SearchResult]:
        """
        Search documents using hybrid vector + text search.
        
        Args:
            query_embedding: Query vector embedding
            text_query: Original text query for keyword search
            limit: Maximum number of results
            similarity_threshold: Minimum similarity threshold
            source_filter: Optional source filter
            
        Returns:
            List of search results with relevance scores
        """
        try:
            logger.info(f"🔍 Searching documents: {text_query[:50]}...")
            
            # TODO: Implement actual hybrid search when schema is ready
            # This would combine:
            # 1. Vector similarity search using pgvector
            # 2. Full-text search using PostgreSQL tsvector
            # 3. Bulgarian language specific optimizations
            
            # Placeholder implementation
            mock_results = [
                SearchResult(
                    title="Horizon Europe - България",
                    content="Horizon Europe е най-голямата програма на ЕС за изследвания и иновации...",
                    source="https://ec.europa.eu/info/research-and-innovation/funding/funding-opportunities/funding-programmes-and-open-calls/horizon-europe_bg",
                    relevance_score=0.95,
                    metadata={"language": "bg", "program_type": "research"}
                ),
                SearchResult(
                    title="Структурни фондове 2021-2027",
                    content="Оперативните програми за периода 2021-2027 в България...",
                    source="https://www.eufunds.bg/bg/operational-programmes",
                    relevance_score=0.88,
                    metadata={"language": "bg", "program_type": "structural"}
                )
            ]
            
            logger.info(f"✅ Found {len(mock_results)} documents")
            return mock_results
            
        except Exception as e:
            logger.error(f"❌ Document search failed: {e}")
            raise
    
    async def get_funding_programs(
        self,
        category: Optional[str] = None,
        deadline_after: Optional[str] = None,
        limit: int = 50
    ) -> List[FundingProgram]:
        """
        Get funding programs with optional filtering.
        
        Args:
            category: Program category filter
            deadline_after: Deadline filter (ISO date string)
            limit: Maximum number of results
            
        Returns:
            List of funding programs
        """
        try:
            logger.info(f"📋 Getting funding programs: category={category}")
            
            # TODO: Implement actual database query
            # This would query the funding_programs table with filters
            
            # Placeholder implementation
            mock_programs = [
                FundingProgram(
                    name="Horizon Europe / Хоризонт Европа",
                    description="Програма за изследвания и иновации на ЕС за периода 2021-2027",
                    eligibility=[
                        "Университети и изследователски институции",
                        "Малки и средни предприятия (МСП)",
                        "Големи предприятия"
                    ],
                    deadline="2025-04-15",
                    budget="95.5 млрд. евро",
                    contact="<EMAIL>"
                )
            ]
            
            logger.info(f"✅ Found {len(mock_programs)} funding programs")
            return mock_programs
            
        except Exception as e:
            logger.error(f"❌ Failed to get funding programs: {e}")
            raise
    
    async def store_document(
        self,
        title: str,
        content: str,
        source: str,
        embedding: List[float],
        metadata: Dict[str, Any]
    ) -> str:
        """
        Store document with embedding in database.
        
        Args:
            title: Document title
            content: Document content
            source: Source URL or identifier
            embedding: Document embedding vector
            metadata: Additional metadata
            
        Returns:
            Document ID
        """
        try:
            logger.info(f"💾 Storing document: {title[:50]}...")
            
            # TODO: Implement actual document storage
            # This would insert into documents table with pgvector embedding
            
            # Placeholder implementation
            doc_id = f"doc_{datetime.now().timestamp()}"
            logger.info(f"✅ Document stored with ID: {doc_id}")
            return doc_id
            
        except Exception as e:
            logger.error(f"❌ Failed to store document: {e}")
            raise
    
    async def get_data_sources(self) -> List[DataSource]:
        """Get all configured data sources."""
        try:
            logger.info("📊 Getting data sources...")
            
            # TODO: Implement actual data sources query
            
            # Placeholder implementation
            mock_sources = [
                DataSource(
                    name="European Commission",
                    url="https://ec.europa.eu",
                    reliability_score=0.95,
                    last_updated="2025-01-07T10:00:00Z",
                    content_type="official",
                    language="bg"
                ),
                DataSource(
                    name="EU Funds Bulgaria",
                    url="https://www.eufunds.bg",
                    reliability_score=0.90,
                    last_updated="2025-01-07T09:30:00Z",
                    content_type="national",
                    language="bg"
                )
            ]
            
            logger.info(f"✅ Found {len(mock_sources)} data sources")
            return mock_sources
            
        except Exception as e:
            logger.error(f"❌ Failed to get data sources: {e}")
            raise
    
    async def log_search_metrics(self, metrics: DatabaseMetrics) -> None:
        """Log search performance metrics."""
        try:
            # TODO: Implement metrics logging to database
            logger.info(f"📊 Search metrics: {metrics.operation} took {metrics.execution_time:.3f}s")
            
        except Exception as e:
            logger.error(f"❌ Failed to log metrics: {e}")
    
    async def cleanup(self) -> None:
        """Cleanup database connections."""
        try:
            logger.info("🧹 Cleaning up database connections...")
            
            if self.pool:
                await self.pool.close()
                logger.info("✅ Database pool closed")
            
            # Supabase client doesn't need explicit cleanup
            self.initialized = False
            logger.info("✅ Database cleanup complete")
            
        except Exception as e:
            logger.error(f"❌ Database cleanup failed: {e}")

# Global database manager instance
db_manager = DatabaseManager()
