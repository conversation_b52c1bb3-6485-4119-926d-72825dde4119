CREATE EXTENSION IF NOT EXISTS vector;

ALTER TABLE eu_funds_content ADD COLUMN IF NOT EXISTS embedding vector(1536);

CREATE INDEX IF NOT EXISTS idx_eu_funds_content_embedding ON eu_funds_content USING ivfflat (embedding vector_cosine_ops);


        CREATE OR REPLACE FUNCTION match_eu_funds_content(
            query_embedding vector(1536),
            match_threshold float DEFAULT 0.3,
            match_count int DEFAULT 10
        )
        RETURNS TABLE (
            id UUID,
            title TEXT,
            content TEXT,
            source_url TEXT,
            content_type TEXT,
            language TEXT,
            metadata JSONB,
            quality_score FLOAT,
            similarity FLOAT
        )
        LANGUAGE plpgsql
        AS $$
        #variable_conflict use_column
        BEGIN
            RETURN QUERY
            SELECT
                eu_funds_content.id,
                eu_funds_content.title,
                eu_funds_content.content,
                eu_funds_content.source_url,
                eu_funds_content.content_type,
                eu_funds_content.language,
                eu_funds_content.metadata,
                eu_funds_content.quality_score,
                1 - (eu_funds_content.embedding <=> query_embedding) AS similarity
            FROM eu_funds_content
            WHERE eu_funds_content.embedding IS NOT NULL
                AND 1 - (eu_funds_content.embedding <=> query_embedding) > match_threshold
            ORDER BY eu_funds_content.embedding <=> query_embedding
            LIMIT match_count;
        END;
        $$;
        

