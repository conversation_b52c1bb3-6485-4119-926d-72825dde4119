"""
Vector Store Implementation for EU Funds MCP Server
Handles Supabase/pgvector integration for semantic search
"""

import logging
import asyncio
import time
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import numpy as np
import httpx
from supabase import create_client, Client
from pgvector.psycopg2 import register_vector
import psycopg2
from psycopg2.extras import RealDictCursor

from src.core.config import settings
from src.core.models import (
    EmbeddingResult,
    VectorSearchResult,
    HybridSearchResult,
    SearchResult,
    SearchMetrics
)
from src.core.cache import get_search_cache, get_embedding_cache

logger = logging.getLogger(__name__)

class VectorStore:
    """
    Advanced vector store with hybrid search capabilities.
    Integrates Supabase/PostgreSQL with pgvector for semantic search.
    """
    
    def __init__(self):
        """Initialize vector store with optimized connection pooling."""
        self.supabase: Optional[Client] = None
        self.pg_connection: Optional[psycopg2.connection] = None
        self.postgres_client = None  # For test compatibility
        self.embedding_dimension = 384  # paraphrase-multilingual-MiniLM-L12-v2
        self.initialized = False

        # Connection pooling configuration
        self.http_client = None
        self.max_connections = 20
        self.max_keepalive_connections = 5
        self.keepalive_expiry = 30

        # Cache instances
        self.search_cache = None
        self.embedding_cache = None
        
    async def initialize(self) -> bool:
        """Initialize Supabase connection and database schema."""
        try:
            logger.info("🔌 Initializing vector store connection...")
            
            # Initialize HTTP client with connection pooling
            self.http_client = httpx.AsyncClient(
                limits=httpx.Limits(
                    max_connections=self.max_connections,
                    max_keepalive_connections=self.max_keepalive_connections,
                    keepalive_expiry=self.keepalive_expiry
                ),
                timeout=httpx.Timeout(30.0)
            )

            # Initialize Supabase client
            self.supabase = create_client(
                settings.supabase_url,
                settings.supabase_service_key
            )

            # Initialize cache instances
            self.search_cache = await get_search_cache()
            self.embedding_cache = await get_embedding_cache()
            logger.info("🧠 Cache instances initialized")

            # Test connection (skip table check for demo)
            try:
                response = self.supabase.table('eu_funds_content').select('id').limit(1).execute()
                logger.info("✅ Supabase connection established with existing tables")
            except Exception as e:
                logger.warning(f"⚠️ Tables don't exist yet (expected for demo): {e}")
                logger.info("✅ Supabase connection established (demo mode)")

            # Initialize PostgreSQL connection for vector operations
            await self._init_pg_connection()

            # Ensure database schema exists
            await self._ensure_schema()

            self.initialized = True
            logger.info("✅ Vector store initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Vector store initialization failed: {e}")
            return False
    
    async def _init_pg_connection(self):
        """Initialize direct PostgreSQL connection for vector operations."""
        try:
            # Extract connection details from Supabase URL
            db_url = settings.supabase_url.replace('https://', '')
            project_id = db_url.split('.')[0]

            # Connect to PostgreSQL directly
            self.pg_connection = psycopg2.connect(
                host=f"db.{project_id}.supabase.co",
                database="postgres",
                user="postgres",
                password=settings.SUPABASE_DB_PASSWORD,  # Need to add this to config
                port=5432
            )
            
            # Register pgvector
            register_vector(self.pg_connection)
            logger.info("✅ PostgreSQL connection with pgvector established")
            
        except Exception as e:
            logger.warning(f"⚠️ Direct PostgreSQL connection failed: {e}")
            logger.info("📝 Will use Supabase REST API for vector operations")
    
    async def _ensure_schema(self):
        """Ensure required database tables and indexes exist."""
        try:
            # Check if tables exist via Supabase
            tables_to_check = [
                'eu_funds_content',
                'eu_funds_embeddings', 
                'eu_funds_search_logs'
            ]
            
            for table in tables_to_check:
                try:
                    response = self.supabase.table(table).select('*').limit(1).execute()
                    logger.info(f"✅ Table '{table}' exists")
                except Exception:
                    logger.warning(f"⚠️ Table '{table}' may not exist - will create if needed")
            
            logger.info("✅ Database schema validation complete")
            
        except Exception as e:
            logger.error(f"❌ Schema validation failed: {e}")
            raise
    
    async def store_content(
        self,
        content: str,
        vector: List[float] = None,
        embedding: List[float] = None,
        metadata: Dict[str, Any] = None,
        source_url: str = ""
    ) -> str:
        """Store content with its embedding in the vector database."""
        try:
            if not self.initialized:
                await self.initialize()

            # Handle both vector and embedding parameters for compatibility
            final_embedding = vector or embedding
            if final_embedding is None:
                raise ValueError("Either 'vector' or 'embedding' parameter must be provided")

            # Prepare metadata with defaults
            metadata = metadata or {}

            # Prepare content record
            content_data = {
                'content': content,
                'source_url': source_url,
                'title': metadata.get('title', f'Content from {source_url}'),
                'metadata': metadata,
                'created_at': datetime.utcnow().isoformat(),
                'language': metadata.get('language', 'unknown'),
                'content_type': metadata.get('content_type', 'text'),
                'quality_score': metadata.get('quality_score', 0.0)
            }

            # Insert content
            content_response = self.supabase.table('eu_funds_content').insert(content_data).execute()
            content_id = content_response.data[0]['id']

            # Store embedding
            embedding_data = {
                'content_id': content_id,
                'embedding': final_embedding,
                'model': metadata.get('embedding_model', 'unknown'),
                'dimensions': len(final_embedding),
                'created_at': datetime.utcnow().isoformat()
            }
            
            self.supabase.table('eu_funds_embeddings').insert(embedding_data).execute()
            
            logger.info(f"✅ Stored content with ID: {content_id}")
            return content_id
            
        except Exception as e:
            logger.error(f"❌ Failed to store content: {e}")
            raise
    
    async def store_batch(
        self,
        contents: List[str],
        embeddings: List[List[float]],
        metadatas: List[Dict[str, Any]],
        source_urls: List[str] = None
    ) -> List[str]:
        """Store multiple content items with embeddings efficiently."""
        try:
            if not source_urls:
                source_urls = [""] * len(contents)
            
            content_ids = []
            
            # Batch insert contents
            content_batch = []
            for i, (content, metadata, url) in enumerate(zip(contents, metadatas, source_urls)):
                content_batch.append({
                    'content': content,
                    'source_url': url,
                    'metadata': metadata,
                    'created_at': datetime.utcnow().isoformat(),
                    'language': metadata.get('language', 'unknown'),
                    'content_type': metadata.get('content_type', 'text'),
                    'quality_score': metadata.get('quality_score', 0.0)
                })
            
            content_response = self.supabase.table('eu_funds_content').insert(content_batch).execute()
            content_ids = [item['id'] for item in content_response.data]
            
            # Batch insert embeddings
            embedding_batch = []
            for i, (content_id, embedding, metadata) in enumerate(zip(content_ids, embeddings, metadatas)):
                embedding_batch.append({
                    'content_id': content_id,
                    'embedding': embedding,
                    'model': metadata.get('embedding_model', 'unknown'),
                    'dimensions': len(embedding),
                    'created_at': datetime.utcnow().isoformat()
                })
            
            self.supabase.table('eu_funds_embeddings').insert(embedding_batch).execute()
            
            logger.info(f"✅ Stored batch of {len(content_ids)} items")
            return content_ids
            
        except Exception as e:
            logger.error(f"❌ Failed to store batch: {e}")
            raise
    
    async def vector_search(
        self,
        query_embedding: List[float] = None,
        query_vector: List[float] = None,
        limit: int = 10,
        similarity_threshold: float = 0.3,
        filters: Dict[str, Any] = None
    ) -> List[SearchResult]:
        """Perform vector similarity search with intelligent caching."""
        try:
            start_time = time.time()

            if not self.initialized:
                await self.initialize()

            # Handle both query_embedding and query_vector parameters for compatibility
            final_embedding = query_embedding or query_vector
            if final_embedding is None:
                raise ValueError("Either 'query_embedding' or 'query_vector' parameter must be provided")

            # Check cache first
            if self.search_cache:
                cache_key = f"vector_{hash(str(final_embedding))}_{limit}_{similarity_threshold}"
                cached_results = await self.search_cache.cache.get(cache_key)
                if cached_results:
                    logger.debug(f"🎯 Vector search cache hit")
                    return [SearchResult(**item) for item in cached_results]

            # Use Supabase RPC for vector similarity search
            search_params = {
                'query_embedding': final_embedding,
                'match_threshold': similarity_threshold,
                'match_count': limit
            }
            
            # Add filters if provided
            if filters:
                search_params.update(filters)
            
            # Execute vector search via Supabase function
            response = self.supabase.rpc('match_eu_funds_content', search_params).execute()
            
            results = []
            for item in response.data:
                results.append(SearchResult(
                    title=item.get('title', 'Untitled'),
                    content=item['content'],
                    source=item.get('source_url', ''),
                    relevance_score=item.get('similarity', 0.0),
                    metadata=item.get('metadata', {})
                ))

            # Cache results for future use
            if self.search_cache and results:
                cache_key = f"vector_{hash(str(final_embedding))}_{limit}_{similarity_threshold}"
                serializable_results = [result.model_dump() for result in results]
                await self.search_cache.cache.set(cache_key, serializable_results, ttl=self.search_cache.cache.search_ttl)
                logger.debug(f"💾 Cached vector search results")

            return results

        except Exception as e:
            logger.error(f"❌ Vector search failed: {e}")
            raise
    
    async def store_content_chunks(
        self,
        chunks: List[Dict[str, Any]],
        source_url: str,
        base_metadata: Dict[str, Any] = None
    ) -> List[str]:
        """Store multiple content chunks with embeddings."""
        try:
            content_ids = []
            base_metadata = base_metadata or {}

            for chunk in chunks:
                content = chunk.get('content', '')
                embedding = chunk.get('embedding', [])
                metadata = {**base_metadata, **chunk.get('metadata', {})}

                content_id = await self.store_content(
                    content=content,
                    embedding=embedding,
                    metadata=metadata,
                    source_url=source_url
                )
                content_ids.append(content_id)

            return content_ids

        except Exception as e:
            logger.error(f"❌ Failed to store content chunks: {e}")
            raise

    async def text_search(
        self,
        query: str,
        limit: int = 10,
        filters: Dict[str, Any] = None
    ) -> List[SearchResult]:
        """Perform text-based search using PostgreSQL full-text search."""
        try:
            # Use Supabase RPC for text search
            response = self.supabase.rpc(
                'search_eu_funds_text',
                {
                    'search_query': query,
                    'match_limit': limit,
                    'similarity_threshold': 0.1
                }
            ).execute()

            results = []
            for item in response.data:
                results.append(SearchResult(
                    title=item.get('title', 'Untitled'),
                    content=item['content'],
                    source=item.get('source_url', ''),
                    relevance_score=item.get('rank', 0.0),
                    metadata=item.get('metadata', {})
                ))

            return results

        except Exception as e:
            logger.error(f"❌ Text search failed: {e}")
            raise

    async def hybrid_search(
        self,
        query: str,
        query_vector: List[float],
        limit: int = 10,
        vector_weight: float = 0.7,
        text_weight: float = 0.3
    ) -> List[SearchResult]:
        """Perform hybrid search combining vector and text search."""
        try:
            # Get vector search results
            vector_results = await self.vector_search(
                query_embedding=query_vector,
                limit=limit * 2  # Get more for fusion
            )

            # Get text search results
            text_results = await self.text_search(
                query=query,
                limit=limit * 2  # Get more for fusion
            )

            # Simple fusion by combining and sorting by weighted scores
            combined_results = {}

            # Add vector results with weight
            for i, result in enumerate(vector_results):
                result_id = result.source  # Use source as unique identifier
                score = result.relevance_score * vector_weight
                combined_results[result_id] = SearchResult(
                    title=result.title,
                    content=result.content,
                    source=result.source,
                    relevance_score=score,
                    metadata={**result.metadata, 'vector_rank': i + 1}
                )

            # Add text results with weight
            for i, result in enumerate(text_results):
                result_id = result.source
                text_score = result.relevance_score * text_weight

                if result_id in combined_results:
                    # Combine scores
                    existing = combined_results[result_id]
                    combined_results[result_id] = SearchResult(
                        title=existing.title,
                        content=existing.content,
                        source=existing.source,
                        relevance_score=existing.relevance_score + text_score,
                        metadata={**existing.metadata, 'text_rank': i + 1}
                    )
                else:
                    combined_results[result_id] = SearchResult(
                        title=result.title,
                        content=result.content,
                        source=result.source,
                        relevance_score=text_score,
                        metadata={**result.metadata, 'text_rank': i + 1}
                    )

            # Sort by hybrid score and return top results
            sorted_results = sorted(
                combined_results.values(),
                key=lambda x: x.relevance_score,
                reverse=True
            )

            return sorted_results[:limit]

        except Exception as e:
            logger.error(f"❌ Hybrid search failed: {e}")
            raise

    async def get_content_by_id(self, content_id: str) -> Optional[Dict[str, Any]]:
        """Retrieve content by ID."""
        try:
            response = self.supabase.table('eu_funds_content').select('*').eq('id', content_id).execute()

            if response.data:
                return response.data[0]
            return None

        except Exception as e:
            logger.error(f"❌ Failed to get content by ID: {e}")
            raise

    async def update_content(self, content_id: str, updates: Dict[str, Any]) -> bool:
        """Update content by ID."""
        try:
            response = self.supabase.table('eu_funds_content').update(updates).eq('id', content_id).execute()
            return len(response.data) > 0

        except Exception as e:
            logger.error(f"❌ Failed to update content: {e}")
            return False

    async def delete_content(self, content_id: str) -> bool:
        """Delete content by ID."""
        try:
            response = self.supabase.table('eu_funds_content').delete().eq('id', content_id).execute()
            return len(response.data) > 0

        except Exception as e:
            logger.error(f"❌ Failed to delete content: {e}")
            return False

    async def get_statistics(self) -> Dict[str, Any]:
        """Get database statistics."""
        try:
            # Get total content count
            content_response = self.supabase.table('eu_funds_content').select('id', count='exact').execute()
            total_content = content_response.count or 0

            # Get content by language
            lang_response = self.supabase.table('eu_funds_content').select('metadata').execute()
            languages = {}
            content_types = {}

            for item in lang_response.data:
                metadata = item.get('metadata', {})
                lang = metadata.get('language', 'unknown')
                content_type = metadata.get('content_type', 'unknown')

                languages[lang] = languages.get(lang, 0) + 1
                content_types[content_type] = content_types.get(content_type, 0) + 1

            return {
                'total_content': total_content,
                'languages': languages,
                'content_types': content_types
            }

        except Exception as e:
            logger.error(f"❌ Failed to get statistics: {e}")
            raise

    async def batch_store_content(
        self,
        batch_data: List[Dict[str, Any]],
        source_url: str
    ) -> List[str]:
        """Store multiple content items in batch."""
        try:
            content_ids = []

            for item in batch_data:
                content_id = await self.store_content(
                    content=item['content'],
                    embedding=item['vector'],
                    metadata=item['metadata'],
                    source_url=source_url
                )
                content_ids.append(content_id)

            return content_ids

        except Exception as e:
            logger.error(f"❌ Failed to batch store content: {e}")
            raise

    async def cleanup(self):
        """Clean up database connections."""
        try:
            if self.pg_connection:
                self.pg_connection.close()
                logger.info("✅ PostgreSQL connection closed")

            # Supabase client doesn't need explicit cleanup
            logger.info("✅ Vector store cleanup complete")

        except Exception as e:
            logger.error(f"❌ Cleanup failed: {e}")
