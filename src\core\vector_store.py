"""
Vector Store Implementation for EU Funds MCP Server
Handles Supabase/pgvector integration for semantic search
"""

import logging
import asyncio
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import numpy as np
from supabase import create_client, Client
from pgvector.psycopg2 import register_vector
import psycopg2
from psycopg2.extras import RealDictCursor

from src.core.config import settings
from src.core.models import (
    EmbeddingResult, 
    VectorSearchResult, 
    HybridSearchResult,
    SearchMetrics
)

logger = logging.getLogger(__name__)

class VectorStore:
    """
    Advanced vector store with hybrid search capabilities.
    Integrates Supabase/PostgreSQL with pgvector for semantic search.
    """
    
    def __init__(self):
        """Initialize vector store with Supabase connection."""
        self.supabase: Optional[Client] = None
        self.pg_connection: Optional[psycopg2.connection] = None
        self.embedding_dimension = 384  # paraphrase-multilingual-MiniLM-L12-v2
        self.initialized = False
        
    async def initialize(self) -> bool:
        """Initialize Supabase connection and database schema."""
        try:
            logger.info("🔌 Initializing vector store connection...")
            
            # Initialize Supabase client
            self.supabase = create_client(
                settings.supabase_url,
                settings.supabase_service_key
            )
            
            # Test connection (skip table check for demo)
            try:
                response = self.supabase.table('eu_funds_content').select('id').limit(1).execute()
                logger.info("✅ Supabase connection established with existing tables")
            except Exception as e:
                logger.warning(f"⚠️ Tables don't exist yet (expected for demo): {e}")
                logger.info("✅ Supabase connection established (demo mode)")
            
            # Initialize PostgreSQL connection for vector operations
            await self._init_pg_connection()
            
            # Ensure database schema exists
            await self._ensure_schema()
            
            self.initialized = True
            logger.info("✅ Vector store initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Vector store initialization failed: {e}")
            return False
    
    async def _init_pg_connection(self):
        """Initialize direct PostgreSQL connection for vector operations."""
        try:
            # Extract connection details from Supabase URL
            db_url = settings.supabase_url.replace('https://', '')
            project_id = db_url.split('.')[0]

            # Connect to PostgreSQL directly
            self.pg_connection = psycopg2.connect(
                host=f"db.{project_id}.supabase.co",
                database="postgres",
                user="postgres",
                password=settings.SUPABASE_DB_PASSWORD,  # Need to add this to config
                port=5432
            )
            
            # Register pgvector
            register_vector(self.pg_connection)
            logger.info("✅ PostgreSQL connection with pgvector established")
            
        except Exception as e:
            logger.warning(f"⚠️ Direct PostgreSQL connection failed: {e}")
            logger.info("📝 Will use Supabase REST API for vector operations")
    
    async def _ensure_schema(self):
        """Ensure required database tables and indexes exist."""
        try:
            # Check if tables exist via Supabase
            tables_to_check = [
                'eu_funds_content',
                'eu_funds_embeddings', 
                'eu_funds_search_logs'
            ]
            
            for table in tables_to_check:
                try:
                    response = self.supabase.table(table).select('*').limit(1).execute()
                    logger.info(f"✅ Table '{table}' exists")
                except Exception:
                    logger.warning(f"⚠️ Table '{table}' may not exist - will create if needed")
            
            logger.info("✅ Database schema validation complete")
            
        except Exception as e:
            logger.error(f"❌ Schema validation failed: {e}")
            raise
    
    async def store_content(
        self,
        content: str,
        embedding: List[float],
        metadata: Dict[str, Any],
        source_url: str = ""
    ) -> str:
        """Store content with its embedding in the vector database."""
        try:
            if not self.initialized:
                await self.initialize()
            
            # Prepare content record
            content_data = {
                'content': content,
                'source_url': source_url,
                'metadata': metadata,
                'created_at': datetime.utcnow().isoformat(),
                'language': metadata.get('language', 'unknown'),
                'content_type': metadata.get('content_type', 'text'),
                'quality_score': metadata.get('quality_score', 0.0)
            }
            
            # Insert content
            content_response = self.supabase.table('eu_funds_content').insert(content_data).execute()
            content_id = content_response.data[0]['id']
            
            # Store embedding
            embedding_data = {
                'content_id': content_id,
                'embedding': embedding,
                'model': metadata.get('embedding_model', 'unknown'),
                'dimensions': len(embedding),
                'created_at': datetime.utcnow().isoformat()
            }
            
            self.supabase.table('eu_funds_embeddings').insert(embedding_data).execute()
            
            logger.info(f"✅ Stored content with ID: {content_id}")
            return content_id
            
        except Exception as e:
            logger.error(f"❌ Failed to store content: {e}")
            raise
    
    async def store_batch(
        self,
        contents: List[str],
        embeddings: List[List[float]],
        metadatas: List[Dict[str, Any]],
        source_urls: List[str] = None
    ) -> List[str]:
        """Store multiple content items with embeddings efficiently."""
        try:
            if not source_urls:
                source_urls = [""] * len(contents)
            
            content_ids = []
            
            # Batch insert contents
            content_batch = []
            for i, (content, metadata, url) in enumerate(zip(contents, metadatas, source_urls)):
                content_batch.append({
                    'content': content,
                    'source_url': url,
                    'metadata': metadata,
                    'created_at': datetime.utcnow().isoformat(),
                    'language': metadata.get('language', 'unknown'),
                    'content_type': metadata.get('content_type', 'text'),
                    'quality_score': metadata.get('quality_score', 0.0)
                })
            
            content_response = self.supabase.table('eu_funds_content').insert(content_batch).execute()
            content_ids = [item['id'] for item in content_response.data]
            
            # Batch insert embeddings
            embedding_batch = []
            for i, (content_id, embedding, metadata) in enumerate(zip(content_ids, embeddings, metadatas)):
                embedding_batch.append({
                    'content_id': content_id,
                    'embedding': embedding,
                    'model': metadata.get('embedding_model', 'unknown'),
                    'dimensions': len(embedding),
                    'created_at': datetime.utcnow().isoformat()
                })
            
            self.supabase.table('eu_funds_embeddings').insert(embedding_batch).execute()
            
            logger.info(f"✅ Stored batch of {len(content_ids)} items")
            return content_ids
            
        except Exception as e:
            logger.error(f"❌ Failed to store batch: {e}")
            raise
    
    async def vector_search(
        self,
        query_embedding: List[float],
        limit: int = 10,
        similarity_threshold: float = 0.3,
        filters: Dict[str, Any] = None
    ) -> VectorSearchResult:
        """Perform vector similarity search."""
        try:
            start_time = datetime.utcnow()
            
            if not self.initialized:
                await self.initialize()
            
            # Use Supabase RPC for vector similarity search
            search_params = {
                'query_embedding': query_embedding,
                'match_threshold': similarity_threshold,
                'match_count': limit
            }
            
            # Add filters if provided
            if filters:
                search_params.update(filters)
            
            # Execute vector search via Supabase function
            response = self.supabase.rpc('match_eu_funds_content', search_params).execute()
            
            results = []
            for item in response.data:
                results.append({
                    'content_id': item['id'],
                    'content': item['content'],
                    'similarity_score': item['similarity'],
                    'metadata': item['metadata'],
                    'source_url': item['source_url']
                })
            
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            
            return VectorSearchResult(
                results=results,
                total_results=len(results),
                processing_time=processing_time,
                similarity_threshold=similarity_threshold,
                search_type="vector",
                query_embedding_dim=len(query_embedding)
            )
            
        except Exception as e:
            logger.error(f"❌ Vector search failed: {e}")
            raise
    
    async def cleanup(self):
        """Clean up database connections."""
        try:
            if self.pg_connection:
                self.pg_connection.close()
                logger.info("✅ PostgreSQL connection closed")
            
            # Supabase client doesn't need explicit cleanup
            logger.info("✅ Vector store cleanup complete")
            
        except Exception as e:
            logger.error(f"❌ Cleanup failed: {e}")
