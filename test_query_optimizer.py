#!/usr/bin/env python3
"""
Test script for query optimizer functionality.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.core.query_optimizer import QueryOptimizer, QueryType

def test_query_optimizer():
    """Test query optimizer with sample queries."""
    optimizer = QueryOptimizer()
    
    test_queries = [
        "Какви програми се споменават в документа?",
        "Какво финансиране се предлага за МСП?",
        "Какви са условията за кандидатстване?",
        "Кои програми са достъпни за малки предприятия?",
        "Horizon Europe програма условия",
        "ОПИК финансиране размер"
    ]
    
    print("🧪 ТЕСТВАНЕ НА QUERY OPTIMIZER")
    print("=" * 50)
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n{i}. Въпрос: '{query}'")
        
        analysis = optimizer.analyze_query(query)
        
        print(f"   Тип: {analysis.query_type.value}")
        print(f"   Увереност: {analysis.confidence:.2f}")
        print(f"   Програми: {[p.name for p in analysis.detected_programs]}")
        print(f"   Филтри: {analysis.metadata_filters}")
        print(f"   Подсказки: {analysis.optimization_hints[:2]}")  # First 2 hints

if __name__ == "__main__":
    test_query_optimizer()
