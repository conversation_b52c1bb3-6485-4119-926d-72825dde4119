#!/usr/bin/env python3
"""
Add embedding column to existing eu_funds_content table.
"""

import asyncio
import sys
import os
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def add_embedding_column():
    """Add embedding column to eu_funds_content table."""
    print("🔧 ДОБАВЯНЕ НА EMBEDDING КОЛОНА")
    print("=" * 50)
    
    # Initialize Supabase client
    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_SERVICE_KEY") or os.getenv("SUPABASE_KEY")
    supabase: Client = create_client(url, key)
    
    # SQL commands to execute
    sql_commands = [
        # Enable vector extension
        "CREATE EXTENSION IF NOT EXISTS vector;",
        
        # Add embedding column
        "ALTER TABLE eu_funds_content ADD COLUMN IF NOT EXISTS embedding vector(1536);",
        
        # Create index for vector search
        "CREATE INDEX IF NOT EXISTS idx_eu_funds_content_embedding ON eu_funds_content USING ivfflat (embedding vector_cosine_ops);",
        
        # Create RPC function for vector search
        """
        CREATE OR REPLACE FUNCTION match_eu_funds_content(
            query_embedding vector(1536),
            match_threshold float DEFAULT 0.3,
            match_count int DEFAULT 10
        )
        RETURNS TABLE (
            id UUID,
            title TEXT,
            content TEXT,
            source_url TEXT,
            content_type TEXT,
            language TEXT,
            metadata JSONB,
            quality_score FLOAT,
            similarity FLOAT
        )
        LANGUAGE plpgsql
        AS $$
        #variable_conflict use_column
        BEGIN
            RETURN QUERY
            SELECT
                eu_funds_content.id,
                eu_funds_content.title,
                eu_funds_content.content,
                eu_funds_content.source_url,
                eu_funds_content.content_type,
                eu_funds_content.language,
                eu_funds_content.metadata,
                eu_funds_content.quality_score,
                1 - (eu_funds_content.embedding <=> query_embedding) AS similarity
            FROM eu_funds_content
            WHERE eu_funds_content.embedding IS NOT NULL
                AND 1 - (eu_funds_content.embedding <=> query_embedding) > match_threshold
            ORDER BY eu_funds_content.embedding <=> query_embedding
            LIMIT match_count;
        END;
        $$;
        """
    ]
    
    print(f"📝 Изпълняване на {len(sql_commands)} SQL команди...")
    
    for i, sql in enumerate(sql_commands):
        try:
            print(f"   {i+1}/{len(sql_commands)}: ", end="")
            
            # Execute SQL using raw SQL (this might not work directly)
            # We'll try different approaches
            
            if i == 0:  # Extension
                print("създаване на vector extension...")
                # This might already exist
                
            elif i == 1:  # Add column
                print("добавяне на embedding колона...")
                # Try to add column
                
            elif i == 2:  # Create index
                print("създаване на index...")
                
            elif i == 3:  # Create function
                print("създаване на RPC функция...")
            
            print("✅ (симулирано)")
            
        except Exception as e:
            print(f"❌ грешка: {e}")
    
    print(f"\n💡 ВАЖНО:")
    print(f"   Тези SQL команди трябва да се изпълнят в Supabase SQL Editor:")
    print(f"   1. Отидете в Supabase Dashboard")
    print(f"   2. SQL Editor")
    print(f"   3. Изпълнете командите от add_embedding_column.sql")
    
    # Save SQL to file for manual execution
    with open('add_embedding_column.sql', 'w', encoding='utf-8') as f:
        for sql in sql_commands:
            f.write(sql + '\n\n')
    
    print(f"   📄 SQL командите са записани в add_embedding_column.sql")
    
    # Test current table structure
    print(f"\n🔍 ТЕКУЩА СТРУКТУРА НА ТАБЛИЦАТА:")
    try:
        result = supabase.table('eu_funds_content').select("*").limit(1).execute()
        if result.data:
            columns = list(result.data[0].keys())
            print(f"   Колони: {columns}")
            
            if 'embedding' in columns:
                print(f"   ✅ Embedding колона съществува")
            else:
                print(f"   ❌ Embedding колона не съществува")
        else:
            print(f"   ⚠️ Няма данни в таблицата")
    except Exception as e:
        print(f"   ❌ Грешка: {e}")

if __name__ == "__main__":
    asyncio.run(add_embedding_column())
