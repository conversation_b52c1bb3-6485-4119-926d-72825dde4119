"""
Simple test for embedding processor with new model
"""

import asyncio
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_embedding():
    """Test embedding with new model."""
    logger.info("🧪 Testing Embedding Processor...")
    
    try:
        # Import fresh
        from src.core.config import settings
        logger.info(f"📋 Model configured: {settings.embedding_model}")
        logger.info(f"📋 Dimensions: {settings.embedding_dimensions}")
        
        from src.core.embeddings import EmbeddingProcessor
        
        # Create new instance
        processor = EmbeddingProcessor()
        
        # Initialize
        await processor.initialize()
        
        # Test embedding
        result = await processor.embed_text(
            text="Търся финансиране за иновативен проект в България",
            is_query=True,
            use_bulgarian_prefix=True
        )
        
        if result:
            logger.info(f"✅ Embedding generated successfully:")
            logger.info(f"   Model: {result.model}")
            logger.info(f"   Dimensions: {result.dimensions}")
            logger.info(f"   Language: {result.language}")
            logger.info(f"   Processing time: {result.processing_time:.3f}s")
            
            # Test batch
            texts = [
                "EU funding for Bulgarian startups",
                "Оперативна програма за конкурентоспособност",
                "Horizon Europe research grants"
            ]
            
            batch_result = await processor.embed_batch(texts, is_query=False)
            logger.info(f"✅ Batch embedding completed:")
            logger.info(f"   Success rate: {batch_result.success_rate:.2f}")
            logger.info(f"   Total embeddings: {batch_result.successful_embeddings}")
            
            await processor.cleanup()
            return True
        else:
            logger.error("❌ No embedding result")
            return False
            
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_embedding())
    if success:
        print("🎉 Embedding test PASSED!")
    else:
        print("💥 Embedding test FAILED!")
