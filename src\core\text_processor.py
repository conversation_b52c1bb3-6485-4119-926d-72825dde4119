"""
Bulgarian Language Processing for EU Funds MCP Server

This module provides specialized text processing for Bulgarian language content,
including Cyrillic handling, morphological analysis, and semantic chunking.
"""

import re
import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import unicodedata

from bs4 import BeautifulSoup, Comment
from langchain_text_splitters import RecursiveCharacterTextSplitter
import tiktoken
from src.core.config import settings
from src.core.models import ProcessedText, TextChunk, LanguageMetrics

logger = logging.getLogger(__name__)

class BulgarianTextProcessor:
    """
    Advanced text processor optimized for Bulgarian EU funding content.
    
    Features:
    - Cyrillic text normalization and cleaning
    - Bulgarian-specific stopword removal
    - Morphological analysis for better search
    - Semantic chunking by content structure
    - Quality validation for Bulgarian content
    """
    
    def __init__(self):
        """Initialize processor with Bulgarian language configuration."""
        
        # Extended Bulgarian stopwords (80 words from config + additional)
        self.stopwords = set(settings.bulgarian_stopwords + [
            # Additional domain-specific stopwords
            "може", "трябва", "следва", "според", "относно", "въпреки",
            "освен", "включително", "например", "тоест", "както", "така",
            "също", "още", "вече", "само", "дори", "почти", "много",
            "малко", "повече", "по-малко", "най-много", "най-малко"
        ])
        
        # Bulgarian morphological patterns
        self.morphological_patterns = {
            # Common Bulgarian suffixes for normalization
            "noun_suffixes": [
                "ите", "ата", "ето", "то", "та", "ът", "ят", "ия", "ия"
            ],
            "verb_suffixes": [
                "ва", "ме", "те", "ат", "ят", "еш", "иш", "ем", "им"
            ],
            "adjective_suffixes": [
                "ни", "на", "но", "ен", "ин", "ов", "ев", "ски", "цки"
            ]
        }
        
        # Funding-specific terminology patterns
        self.funding_patterns = {
            "program_indicators": [
                r"програма\s+\d{4}-\d{4}",
                r"оперативна\s+програма",
                r"хоризонт\s+европа",
                r"структурни\s+фондове",
                r"кохезионен\s+фонд"
            ],
            "deadline_patterns": [
                r"срок\s+за\s+кандидатстване[:\s]*(\d{1,2}[\.\/]\d{1,2}[\.\/]\d{4})",
                r"крайна\s+дата[:\s]*(\d{1,2}[\.\/]\d{1,2}[\.\/]\d{4})",
                r"до\s+(\d{1,2}[\.\/]\d{1,2}[\.\/]\d{4})",
                r"(\d{1,2}[\.\/]\d{1,2}[\.\/]\d{4})\s+г\."
            ],
            "budget_patterns": [
                r"бюджет[:\s]*(\d+(?:\.\d+)?)\s*(млн\.?|милиона?)\s*(лв\.?|евро?)",
                r"размер\s+на\s+финансирането[:\s]*(\d+(?:\.\d+)?)",
                r"максимален\s+размер[:\s]*(\d+(?:\.\d+)?)"
            ],
            "eligibility_patterns": [
                r"критерии\s+за\s+допустимост",
                r"условия\s+за\s+участие",
                r"изисквания\s+към\s+кандидатите",
                r"целеви\s+групи"
            ]
        }
        
        # Text quality indicators
        self.quality_indicators = {
            "positive": [
                "програма", "финансиране", "кандидатстване", "проект",
                "безвъзмездна помощ", "европейски фондове", "критерии",
                "допустимост", "срок", "бюджет", "дейности"
            ],
            "negative": [
                "реклама", "продажба", "купуване", "търговия",
                "лична информация", "cookies", "политика"
            ]
        }
    
    async def process_text(
        self,
        text: str,
        source_url: str = "",
        preserve_structure: bool = True
    ) -> ProcessedText:
        """
        Process Bulgarian text with comprehensive analysis.
        
        Args:
            text: Raw text content
            source_url: Source URL for context
            preserve_structure: Whether to preserve document structure
            
        Returns:
            ProcessedText with analysis and chunks
        """
        try:
            logger.info(f"📝 Processing Bulgarian text ({len(text)} chars)...")
            
            # Step 1: Text normalization and cleaning
            normalized_text = self._normalize_cyrillic(text)
            cleaned_text = self._clean_text(normalized_text)
            
            # Step 2: Language detection and validation
            language_metrics = self._analyze_language(cleaned_text)
            
            # Step 3: Extract structured information
            extracted_info = self._extract_funding_info(cleaned_text)
            
            # Step 4: Semantic chunking
            chunks = await self._create_semantic_chunks(
                cleaned_text, preserve_structure
            )
            
            # Step 5: Quality assessment
            quality_score = self._calculate_quality_score(
                cleaned_text, extracted_info, language_metrics
            )
            
            # Create processed text result
            processed = ProcessedText(
                original_text=text,
                cleaned_text=cleaned_text,
                language_metrics=language_metrics,
                extracted_info=extracted_info,
                chunks=chunks,
                quality_score=quality_score,
                processing_timestamp=datetime.now().isoformat(),
                source_url=source_url
            )
            
            logger.info(f"✅ Text processed: {len(chunks)} chunks, quality: {quality_score:.2f}")
            return processed

        except Exception as e:
            logger.error(f"❌ Text processing failed: {e}")
            raise

    async def process_text_with_context(
        self,
        text: str,
        source_url: str = "",
        preserve_structure: bool = True
    ) -> ProcessedText:
        """
        Process Bulgarian text with contextual embeddings using Anthropic's technique.

        This method enhances the standard text processing by adding contextual information
        to each chunk before embedding, significantly improving retrieval accuracy.

        Args:
            text: Raw text content
            source_url: Source URL for context
            preserve_structure: Whether to preserve document structure

        Returns:
            ProcessedText with contextual embeddings
        """
        try:
            logger.info(f"🧠 Processing Bulgarian text with contextual embeddings ({len(text)} chars)...")

            # Step 1: Standard text processing
            processed = await self.process_text(text, source_url, preserve_structure)

            # Step 2: Create document context for embeddings
            document_context = self._create_document_context(text, source_url, processed.extracted_info)

            # Step 3: Enhance chunks with contextual embeddings
            enhanced_chunks = []
            for chunk in processed.chunks:
                # Create contextual version of the chunk
                enhanced_chunk = await self._enhance_chunk_with_context(chunk, document_context)
                enhanced_chunks.append(enhanced_chunk)

            # Update processed text with enhanced chunks
            processed.chunks = enhanced_chunks

            logger.info(f"✅ Enhanced {len(enhanced_chunks)} chunks with contextual embeddings")
            return processed

        except Exception as e:
            logger.error(f"❌ Contextual text processing failed: {e}")
            # Fallback to standard processing
            return await self.process_text(text, source_url, preserve_structure)
    
    def _normalize_cyrillic(self, text: str) -> str:
        """Normalize Cyrillic text and handle encoding issues."""
        # Unicode normalization
        text = unicodedata.normalize('NFC', text)
        
        # Fix common encoding issues
        replacements = {
            'Ð°': 'а', 'Ð±': 'б', 'Ð²': 'в', 'Ð³': 'г', 'Ð´': 'д',
            'Ðµ': 'е', 'Ð¶': 'ж', 'Ð·': 'з', 'Ð¸': 'и', 'Ð¹': 'й',
            'Ðº': 'к', 'Ð»': 'л', 'Ð¼': 'м', 'Ð½': 'н', 'Ð¾': 'о',
            'Ð¿': 'п', 'Ñ€': 'р', 'Ñ': 'с', 'Ñ‚': 'т', 'Ñƒ': 'у',
            'Ñ„': 'ф', 'Ñ…': 'х', 'Ñ†': 'ц', 'Ñ‡': 'ч', 'Ñˆ': 'ш',
            'Ñ‰': 'щ', 'ÑŠ': 'ъ', 'ÑŒ': 'ь', 'ÑŽ': 'ю', 'Ñ': 'я'
        }
        
        for wrong, correct in replacements.items():
            text = text.replace(wrong, correct)
        
        return text
    
    def _clean_text(self, text: str) -> str:
        """
        Advanced HTML content cleaning with BeautifulSoup.

        Removes unwanted elements while preserving semantic structure
        and optimizing for Bulgarian language content.
        """
        try:
            # Check if text contains HTML
            if '<' in text and '>' in text:
                return self._clean_html_content(text)
            else:
                return self._clean_plain_text(text)
        except Exception as e:
            logger.warning(f"⚠️ HTML cleaning failed, falling back to basic cleaning: {e}")
            return self._clean_plain_text(text)

    def _clean_html_content(self, html_content: str) -> str:
        """
        Clean HTML content using HtmlRAG-inspired approach.

        Based on research from "HtmlRAG: HTML is Better Than Plain Text for Modeling Retrieved Knowledge in RAG Systems"
        Preserves semantic structure while removing noise for optimal RAG performance.
        """
        try:
            # Parse HTML with BeautifulSoup
            soup = BeautifulSoup(html_content, 'lxml')

            # Step 1: Remove completely useless elements (HtmlRAG approach)
            # These elements provide no semantic value for RAG
            noise_tags = [
                'script', 'style', 'noscript', 'meta', 'link', 'title',
                'iframe', 'object', 'embed', 'applet', 'param'
            ]

            for tag in noise_tags:
                for element in soup.find_all(tag):
                    element.decompose()

            # Step 2: Remove navigation and UI elements but preserve main content
            # More conservative approach - only remove clear navigation/UI
            ui_patterns = [
                r'^nav$', r'^menu$', r'^sidebar$', r'^footer$',
                r'^header-nav', r'^main-nav', r'^top-nav',
                r'^breadcrumb', r'^pagination', r'^search-form',
                r'^cookie', r'^popup', r'^modal',
                r'^advertisement', r'^banner-ad', r'^promo-banner'
            ]

            for pattern in ui_patterns:
                # Remove by class (exact match or starts with pattern)
                for element in soup.find_all(class_=re.compile(pattern, re.IGNORECASE)):
                    element.decompose()
                # Remove by id (exact match or starts with pattern)
                for element in soup.find_all(id=re.compile(pattern, re.IGNORECASE)):
                    element.decompose()

            # Step 3: Preserve semantic structure (HtmlRAG key principle)
            # Keep headers, paragraphs, lists, tables with their semantic meaning

            # Enhance headers with clear separation
            for i, header in enumerate(soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])):
                level = int(header.name[1])
                header_text = header.get_text().strip()
                if header_text:
                    # Add semantic markers for different header levels
                    prefix = "=" * level
                    header.string = f"\n\n{prefix} {header_text} {prefix}\n"

            # Preserve list structure with clear formatting
            for ul in soup.find_all(['ul', 'ol']):
                items = []
                for li in ul.find_all('li'):
                    item_text = li.get_text().strip()
                    if item_text and len(item_text) > 3:  # Filter out empty/meaningless items
                        items.append(f"• {item_text}")
                if items:
                    ul.string = "\n" + "\n".join(items) + "\n"

            # Preserve table structure (critical for EU funding data)
            for table in soup.find_all('table'):
                table_text = self._extract_table_content_enhanced(table)
                if table_text and len(table_text) > 20:  # Only keep substantial tables
                    table.string = f"\n\n[TABLE]\n{table_text}\n[/TABLE]\n"

            # Step 4: Clean up remaining structure while preserving content
            # Remove empty elements that add no value
            for element in soup.find_all():
                if not element.get_text().strip():
                    element.decompose()

            # Step 5: Extract text with preserved structure
            text = soup.get_text(separator=' ', strip=True)

            # Step 6: Post-process for Bulgarian content optimization
            return self._clean_plain_text_enhanced(text)

        except Exception as e:
            logger.error(f"❌ BeautifulSoup HTML cleaning failed: {e}")
            # Fallback to basic regex cleaning
            text = re.sub(r'<[^>]+>', ' ', html_content)
            return self._clean_plain_text(text)

    def _extract_table_content(self, table) -> str:
        """Extract readable content from HTML tables."""
        try:
            rows = []
            for tr in table.find_all('tr'):
                cells = []
                for cell in tr.find_all(['td', 'th']):
                    cell_text = cell.get_text().strip()
                    if cell_text:
                        cells.append(cell_text)
                if cells:
                    rows.append(" | ".join(cells))
            return "\n".join(rows) if rows else ""
        except Exception:
            return ""

    def _clean_plain_text(self, text: str) -> str:
        """Clean plain text content with Bulgarian language optimization."""
        # Remove HTML entities
        text = re.sub(r'&[a-zA-Z0-9#]+;', ' ', text)

        # Normalize excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)  # Max 2 consecutive newlines

        # Standardize Bulgarian punctuation
        text = re.sub(r'[""„"]', '"', text)
        text = re.sub(r'[''`]', "'", text)
        text = re.sub(r'[–—]', '-', text)

        # Remove excessive punctuation
        text = re.sub(r'[.]{3,}', '...', text)
        text = re.sub(r'[!]{2,}', '!', text)
        text = re.sub(r'[?]{2,}', '?', text)

        # Clean up spacing around punctuation
        text = re.sub(r'\s+([,.!?;:])', r'\1', text)
        text = re.sub(r'([,.!?;:])\s+', r'\1 ', text)

        # Remove common web artifacts
        text = re.sub(r'(Cookies?|Privacy Policy|Terms of Service|Copyright|©)', '', text, flags=re.IGNORECASE)
        text = re.sub(r'(Click here|Read more|Learn more|See more)', '', text, flags=re.IGNORECASE)

        # Clean up final spacing
        text = re.sub(r'\s+', ' ', text)

        return text.strip()

    def _extract_table_content_enhanced(self, table) -> str:
        """
        Enhanced table extraction for EU funding data.
        Preserves table structure with better formatting.
        """
        try:
            rows = []
            headers = []

            # Extract headers first
            header_row = table.find('tr')
            if header_row:
                for th in header_row.find_all(['th', 'td']):
                    header_text = th.get_text().strip()
                    if header_text:
                        headers.append(header_text)

            # Extract data rows
            for tr in table.find_all('tr')[1:]:  # Skip header row
                cells = []
                for cell in tr.find_all(['td', 'th']):
                    cell_text = cell.get_text().strip()
                    # Keep cell even if empty to maintain table structure
                    cells.append(cell_text if cell_text else "-")

                if cells and any(cell != "-" for cell in cells):  # Only keep rows with some content
                    rows.append(" | ".join(cells))

            # Format table with headers
            result = []
            if headers:
                result.append(" | ".join(headers))
                result.append("-" * len(" | ".join(headers)))  # Separator line

            result.extend(rows)
            return "\n".join(result) if result else ""

        except Exception as e:
            logger.warning(f"Table extraction failed: {e}")
            return ""

    def _clean_plain_text_enhanced(self, text: str) -> str:
        """
        Enhanced text cleaning optimized for Bulgarian EU funding content.
        Based on HtmlRAG principles for preserving semantic meaning.
        """
        # Remove HTML entities
        text = re.sub(r'&[a-zA-Z0-9#]+;', ' ', text)

        # Preserve structured content markers
        text = re.sub(r'\[TABLE\]\s*\n', '\n[ТАБЛИЦА]\n', text)
        text = re.sub(r'\n\s*\[/TABLE\]', '\n[/ТАБЛИЦА]\n', text)

        # Normalize whitespace but preserve structure
        text = re.sub(r'[ \t]+', ' ', text)  # Multiple spaces to single space
        text = re.sub(r'\n[ \t]+', '\n', text)  # Remove leading spaces on lines
        text = re.sub(r'[ \t]+\n', '\n', text)  # Remove trailing spaces on lines

        # Preserve paragraph structure (max 2 consecutive newlines)
        text = re.sub(r'\n{3,}', '\n\n', text)

        # Standardize Bulgarian punctuation
        text = re.sub(r'[""„"]', '"', text)
        text = re.sub(r'[''`]', "'", text)
        text = re.sub(r'[–—]', '-', text)

        # Clean up punctuation spacing
        text = re.sub(r'\s+([,.!?;:])', r'\1', text)
        text = re.sub(r'([,.!?;:])\s+', r'\1 ', text)

        # Remove web artifacts but be more conservative
        web_artifacts = [
            r'Премини към основното съдържание',
            r'Skip to main content',
            r'Cookies?',
            r'Privacy Policy',
            r'Политика за поверителност',
            r'Copyright|©',
            r'Всички права запазени'
        ]

        for artifact in web_artifacts:
            text = re.sub(artifact, '', text, flags=re.IGNORECASE)

        # Remove excessive punctuation
        text = re.sub(r'[.]{3,}', '...', text)
        text = re.sub(r'[!]{2,}', '!', text)
        text = re.sub(r'[?]{2,}', '?', text)

        # Final cleanup
        text = re.sub(r'\s+', ' ', text)
        text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)

        return text.strip()

    def _analyze_language(self, text: str) -> LanguageMetrics:
        """Analyze language characteristics of the text."""
        # Character analysis
        total_chars = len(text)
        cyrillic_chars = len(re.findall(r'[а-яё]', text, re.IGNORECASE))
        latin_chars = len(re.findall(r'[a-z]', text, re.IGNORECASE))
        
        # Word analysis
        words = text.split()
        total_words = len(words)
        bulgarian_words = sum(1 for word in words if re.search(r'[а-яё]', word))
        
        # Calculate ratios
        cyrillic_ratio = cyrillic_chars / max(total_chars, 1)
        bulgarian_word_ratio = bulgarian_words / max(total_words, 1)
        
        # Detect primary language
        if cyrillic_ratio > 0.3 or bulgarian_word_ratio > 0.4:
            primary_language = "bg"
            confidence = max(cyrillic_ratio, bulgarian_word_ratio)
        else:
            primary_language = "en"
            confidence = 1.0 - max(cyrillic_ratio, bulgarian_word_ratio)
        
        # Calculate bulgarian ratio
        bulgarian_ratio = bulgarian_words / max(total_words, 1)

        # Extract funding terms
        funding_terms = []
        for pattern in self.funding_patterns["program_indicators"]:
            matches = re.findall(pattern, text, re.IGNORECASE)
            funding_terms.extend(matches)

        return LanguageMetrics(
            primary_language=primary_language,
            confidence=confidence,
            cyrillic_ratio=cyrillic_ratio,
            bulgarian_ratio=bulgarian_ratio,
            total_words=total_words,
            bulgarian_words=bulgarian_words,
            avg_word_length=sum(len(word) for word in words) / max(total_words, 1),
            funding_terms=funding_terms
        )
    
    def _extract_funding_info(self, text: str) -> Dict[str, Any]:
        """Extract structured funding information from text."""
        extracted = {
            "programs": [],
            "deadlines": [],
            "budgets": [],
            "eligibility": [],
            "contacts": []
        }
        
        # Extract funding programs
        for pattern in self.funding_patterns["program_indicators"]:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                extracted["programs"].append(match.group().strip())
        
        # Extract deadlines
        for pattern in self.funding_patterns["deadline_patterns"]:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                if match.groups():
                    extracted["deadlines"].append(match.group(1))
                else:
                    extracted["deadlines"].append(match.group().strip())
        
        # Extract budget information
        for pattern in self.funding_patterns["budget_patterns"]:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                extracted["budgets"].append(match.group().strip())
        
        # Extract eligibility criteria
        for pattern in self.funding_patterns["eligibility_patterns"]:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                # Extract surrounding context
                start = max(0, match.start() - 100)
                end = min(len(text), match.end() + 200)
                context = text[start:end].strip()
                extracted["eligibility"].append(context)
        
        # Extract contact information
        email_pattern = r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}'
        phone_pattern = r'(?:\+359|0)\s*[0-9\s\-\(\)]{8,15}'
        
        emails = re.findall(email_pattern, text)
        phones = re.findall(phone_pattern, text)
        
        extracted["contacts"] = {
            "emails": emails,
            "phones": phones
        }
        
        return extracted
    
    async def _create_semantic_chunks(
        self,
        text: str,
        preserve_structure: bool = True
    ) -> List[TextChunk]:
        """
        Create semantic chunks using advanced LangChain text splitter.

        Optimized for Bulgarian EU funding content with larger chunk sizes
        for better context preservation and improved RAG accuracy.
        """
        try:
            # Initialize token counter for Bulgarian content
            encoding = tiktoken.get_encoding("cl100k_base")

            # Create advanced text splitter with semantic awareness
            text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=1024,  # Increased from 800 for better context
                chunk_overlap=256,  # 25% overlap for context preservation
                length_function=lambda x: len(encoding.encode(x)),
                separators=[
                    "\n\n\n",  # Major section breaks
                    "\n\n",    # Paragraph breaks
                    "\n",      # Line breaks
                    ". ",      # Sentence endings
                    "! ",      # Exclamation sentences
                    "? ",      # Question sentences
                    "; ",      # Semicolon breaks
                    ", ",      # Comma breaks (last resort)
                    " ",       # Word breaks (last resort)
                    "",        # Character breaks (absolute last resort)
                ],
                keep_separator=True,
                is_separator_regex=False,
            )

            # Split text into semantic chunks
            text_chunks = text_splitter.split_text(text)

            # Convert to TextChunk objects with enhanced metadata
            chunks = []
            for chunk_id, chunk_text in enumerate(text_chunks):
                chunk_text = chunk_text.strip()
                if len(chunk_text) < 50:  # Skip very short chunks
                    continue

                chunk = self._create_enhanced_chunk(chunk_text, chunk_id)
                chunks.append(chunk)

            logger.info(f"✅ Created {len(chunks)} semantic chunks (avg size: {sum(len(c.content) for c in chunks) // len(chunks) if chunks else 0} chars)")
            return chunks

        except Exception as e:
            logger.error(f"❌ Semantic chunking failed, falling back to basic chunking: {e}")
            return await self._create_basic_chunks(text)
    
    async def _create_basic_chunks(self, text: str) -> List[TextChunk]:
        """Fallback basic chunking method."""
        chunks = []

        # Split by paragraphs first
        sections = re.split(r'\n\s*\n', text)

        chunk_id = 0
        for section in sections:
            section = section.strip()
            if len(section) < 50:
                continue

            # Further split long sections
            if len(section) > 1000:
                subsections = self._split_long_section(section)
                for subsection in subsections:
                    if len(subsection.strip()) >= 50:
                        chunk = self._create_chunk(subsection, chunk_id)
                        chunks.append(chunk)
                        chunk_id += 1
            else:
                chunk = self._create_chunk(section, chunk_id)
                chunks.append(chunk)
                chunk_id += 1

        return chunks

    def _split_long_section(self, text: str, max_length: int = 800) -> List[str]:
        """Split long sections while preserving sentence boundaries."""
        sentences = re.split(r'(?<=[.!?])\s+', text)
        chunks = []
        current_chunk = ""

        for sentence in sentences:
            if len(current_chunk) + len(sentence) <= max_length:
                current_chunk += sentence + " "
            else:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                current_chunk = sentence + " "

        if current_chunk:
            chunks.append(current_chunk.strip())

        return chunks
    
    def _create_enhanced_chunk(self, text: str, chunk_id: int) -> TextChunk:
        """
        Create enhanced text chunk with improved metadata for better RAG performance.

        Includes semantic analysis, funding-specific keywords, and context indicators.
        """
        # Basic word analysis
        words = text.lower().split()

        # Enhanced keyword extraction with funding domain focus
        keywords = []
        funding_keywords = []

        for word in words:
            # Clean word
            clean_word = re.sub(r'[^\w]', '', word)
            if len(clean_word) < 3 or clean_word in self.stopwords:
                continue

            keywords.append(clean_word)

            # Identify funding-specific keywords
            if any(funding_term in clean_word for funding_term in [
                "програма", "финансиране", "фонд", "проект", "грант", "субсидия",
                "бюджет", "евро", "лева", "срок", "условия", "критерии", "мсп"
            ]):
                funding_keywords.append(clean_word)

        # Extract enhanced key phrases with context
        key_phrases = self._extract_contextual_phrases(text)

        # Analyze chunk type and importance
        chunk_type = self._analyze_chunk_type(text)
        importance_score = self._calculate_chunk_importance(text, funding_keywords)

        return TextChunk(
            chunk_id=chunk_id,
            content=text,
            text=text,
            word_count=len(words),
            keywords=keywords[:15],  # Increased from 10
            key_phrases=key_phrases[:8],  # Increased from 5
            metadata={
                "funding_keywords": funding_keywords,
                "chunk_type": chunk_type,
                "importance_score": importance_score,
                "has_financial_info": any(term in text.lower() for term in ["евро", "лева", "бюджет", "сума"]),
                "has_deadline_info": any(term in text.lower() for term in ["срок", "дата", "до", "преди"]),
                "has_criteria_info": any(term in text.lower() for term in ["условия", "критерии", "изисквания"]),
            }
        )

    def _create_chunk(self, text: str, chunk_id: int) -> TextChunk:
        """Fallback basic chunk creation method."""
        # Remove stopwords for keyword extraction
        words = text.lower().split()
        keywords = [word for word in words if word not in self.stopwords and len(word) > 2]

        # Extract key phrases (2-3 word combinations)
        key_phrases = []
        for i in range(len(keywords) - 1):
            phrase = f"{keywords[i]} {keywords[i+1]}"
            if any(funding_word in phrase for funding_word in ["програма", "финансиране", "фонд", "проект"]):
                key_phrases.append(phrase)

        return TextChunk(
            chunk_id=chunk_id,
            content=text,
            text=text,
            word_count=len(words),
            keywords=keywords[:10],  # Top 10 keywords
            key_phrases=key_phrases[:5],  # Top 5 key phrases
            has_funding_info=any(
                keyword in text.lower()
                for keyword in ["програма", "финансиране", "фонд", "срок", "критерии"]
            ),
            language="bg" if re.search(r'[а-яё]', text) else "en"
        )
    
    def _calculate_quality_score(
        self,
        text: str,
        extracted_info: Dict[str, Any],
        language_metrics: LanguageMetrics
    ) -> float:
        """Calculate content quality score for Bulgarian funding content."""
        score = 0.0
        
        # Base score for text length
        if len(text) > 500:
            score += 0.2
        elif len(text) > 200:
            score += 0.1
        
        # Language quality
        if language_metrics.primary_language == "bg":
            score += 0.2 * language_metrics.confidence
        
        # Structured information bonus
        if extracted_info["programs"]:
            score += 0.15
        if extracted_info["deadlines"]:
            score += 0.15
        if extracted_info["budgets"]:
            score += 0.1
        if extracted_info["eligibility"]:
            score += 0.1
        
        # Positive keywords bonus
        positive_count = sum(
            1 for keyword in self.quality_indicators["positive"]
            if keyword in text.lower()
        )
        score += min(0.1, positive_count * 0.02)
        
        # Negative keywords penalty
        negative_count = sum(
            1 for keyword in self.quality_indicators["negative"]
            if keyword in text.lower()
        )
        score -= min(0.1, negative_count * 0.03)
        
        return max(0.0, min(1.0, score))

    def _extract_contextual_phrases(self, text: str) -> List[str]:
        """Extract contextual phrases relevant to EU funding."""
        phrases = []

        # Multi-word funding terms
        funding_phrases = [
            r"европейски фондове?", r"оперативна програма", r"структурни фондове?",
            r"кохезионен фонд", r"регионално развитие", r"иновации и конкурентоспособност",
            r"човешки ресурси", r"околна среда", r"транспортна инфраструктура",
            r"малки и средни предприятия", r"научни изследвания", r"образование и обучение",
            r"финансова помощ", r"безвъзмездна помощ", r"проектно финансиране"
        ]

        for pattern in funding_phrases:
            matches = re.findall(pattern, text, re.IGNORECASE)
            phrases.extend(matches)

        # Extract numerical phrases (budgets, deadlines)
        numerical_phrases = re.findall(r'\d+[.,]?\d*\s*(?:млн\.?|милиона?|хиляди?|евро|лева?)', text, re.IGNORECASE)
        phrases.extend(numerical_phrases)

        # Extract date phrases
        date_phrases = re.findall(r'\d{1,2}[./]\d{1,2}[./]\d{4}|\d{4}\s*г\.?', text)
        phrases.extend(date_phrases)

        return list(set(phrases))  # Remove duplicates

    def _analyze_chunk_type(self, text: str) -> str:
        """Analyze the type of content in the chunk."""
        text_lower = text.lower()

        if any(term in text_lower for term in ["програма", "схема", "мярка"]):
            return "program_info"
        elif any(term in text_lower for term in ["бюджет", "финансиране", "сума", "евро", "лева"]):
            return "financial_info"
        elif any(term in text_lower for term in ["условия", "критерии", "изисквания", "право"]):
            return "eligibility_criteria"
        elif any(term in text_lower for term in ["срок", "дата", "до", "преди", "кандидатстване"]):
            return "deadline_info"
        elif any(term in text_lower for term in ["контакт", "информация", "телефон", "email", "@"]):
            return "contact_info"
        else:
            return "general_info"

    def _calculate_chunk_importance(self, text: str, funding_keywords: List[str]) -> float:
        """Calculate importance score based on funding relevance."""
        score = 0.0
        text_lower = text.lower()

        # Base score from funding keywords density
        if len(text.split()) > 0:
            keyword_density = len(funding_keywords) / len(text.split())
            score += keyword_density * 10

        # Bonus for specific high-value terms
        high_value_terms = [
            "безвъзмездна помощ", "финансиране", "бюджет", "евро",
            "условия", "критерии", "срок", "кандидатстване"
        ]

        for term in high_value_terms:
            if term in text_lower:
                score += 1.0

        # Bonus for numerical information (budgets, dates)
        if re.search(r'\d+[.,]?\d*\s*(?:млн|милион|хиляд|евро|лева)', text_lower):
            score += 2.0

        if re.search(r'\d{1,2}[./]\d{1,2}[./]\d{4}', text):
            score += 1.5

        # Normalize score to 0-10 range
        return min(score, 10.0)

    def _create_document_context(self, text: str, source_url: str, extracted_info: Dict[str, Any]) -> str:
        """Create document context for contextual embeddings."""
        context_parts = []

        # Add source information
        if source_url:
            context_parts.append(f"Източник: {source_url}")

        # Add document type context
        if "programs" in extracted_info and extracted_info["programs"]:
            context_parts.append(f"Документ съдържа {len(extracted_info['programs'])} програми за финансиране")

        if "financial_amounts" in extracted_info and extracted_info["financial_amounts"]:
            context_parts.append("Включва финансова информация и бюджети")

        if "deadlines" in extracted_info and extracted_info["deadlines"]:
            context_parts.append("Съдържа важни срокове и дати")

        # Add content type analysis
        text_lower = text.lower()
        if "оперативна програма" in text_lower:
            context_parts.append("Част от оперативна програма на ЕС")

        if any(term in text_lower for term in ["мсп", "малки и средни предприятия"]):
            context_parts.append("Фокусиран върху малки и средни предприятия")

        if any(term in text_lower for term in ["иновации", "изследвания", "технологии"]):
            context_parts.append("Свързан с иновации и научни изследвания")

        return ". ".join(context_parts) if context_parts else "Документ за европейско финансиране в България"

    async def _enhance_chunk_with_context(self, chunk: TextChunk, document_context: str) -> TextChunk:
        """Enhance chunk with contextual embedding using Anthropic's technique."""
        try:
            from src.core.embeddings import embedding_processor

            # Generate contextual embedding
            contextual_result = await embedding_processor.embed_text_with_context(
                text=chunk.content,
                document_context=document_context,
                is_query=False,
                use_bulgarian_prefix=True,
                cache_key=f"chunk_{chunk.chunk_id}"
            )

            if contextual_result and contextual_result.success:
                # Update chunk with contextual embedding
                chunk.metadata["contextual_embedding"] = contextual_result.vector
                chunk.metadata["contextual_text"] = contextual_result.contextual_text
                chunk.metadata["embedding_model"] = contextual_result.model_name
                chunk.metadata["embedding_dimensions"] = contextual_result.dimensions

                logger.debug(f"✅ Enhanced chunk {chunk.chunk_id} with contextual embedding")
            else:
                logger.warning(f"⚠️ Failed to create contextual embedding for chunk {chunk.chunk_id}")

            return chunk

        except Exception as e:
            logger.error(f"❌ Failed to enhance chunk {chunk.chunk_id} with context: {e}")
            return chunk

# Global text processor instance
text_processor = BulgarianTextProcessor()
