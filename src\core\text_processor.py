"""
Bulgarian Language Processing for EU Funds MCP Server

This module provides specialized text processing for Bulgarian language content,
including Cyrillic handling, morphological analysis, and semantic chunking.
"""

import re
import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import unicodedata

from src.core.config import settings
from src.core.models import ProcessedText, TextChunk, LanguageMetrics

logger = logging.getLogger(__name__)

class BulgarianTextProcessor:
    """
    Advanced text processor optimized for Bulgarian EU funding content.
    
    Features:
    - Cyrillic text normalization and cleaning
    - Bulgarian-specific stopword removal
    - Morphological analysis for better search
    - Semantic chunking by content structure
    - Quality validation for Bulgarian content
    """
    
    def __init__(self):
        """Initialize processor with Bulgarian language configuration."""
        
        # Extended Bulgarian stopwords (80 words from config + additional)
        self.stopwords = set(settings.bulgarian_stopwords + [
            # Additional domain-specific stopwords
            "може", "трябва", "следва", "според", "относно", "въпреки",
            "освен", "включително", "например", "тоест", "както", "така",
            "също", "още", "вече", "само", "дори", "почти", "много",
            "малко", "повече", "по-малко", "най-много", "най-малко"
        ])
        
        # Bulgarian morphological patterns
        self.morphological_patterns = {
            # Common Bulgarian suffixes for normalization
            "noun_suffixes": [
                "ите", "ата", "ето", "то", "та", "ът", "ят", "ия", "ия"
            ],
            "verb_suffixes": [
                "ва", "ме", "те", "ат", "ят", "еш", "иш", "ем", "им"
            ],
            "adjective_suffixes": [
                "ни", "на", "но", "ен", "ин", "ов", "ев", "ски", "цки"
            ]
        }
        
        # Funding-specific terminology patterns
        self.funding_patterns = {
            "program_indicators": [
                r"програма\s+\d{4}-\d{4}",
                r"оперативна\s+програма",
                r"хоризонт\s+европа",
                r"структурни\s+фондове",
                r"кохезионен\s+фонд"
            ],
            "deadline_patterns": [
                r"срок\s+за\s+кандидатстване[:\s]*(\d{1,2}[\.\/]\d{1,2}[\.\/]\d{4})",
                r"крайна\s+дата[:\s]*(\d{1,2}[\.\/]\d{1,2}[\.\/]\d{4})",
                r"до\s+(\d{1,2}[\.\/]\d{1,2}[\.\/]\d{4})",
                r"(\d{1,2}[\.\/]\d{1,2}[\.\/]\d{4})\s+г\."
            ],
            "budget_patterns": [
                r"бюджет[:\s]*(\d+(?:\.\d+)?)\s*(млн\.?|милиона?)\s*(лв\.?|евро?)",
                r"размер\s+на\s+финансирането[:\s]*(\d+(?:\.\d+)?)",
                r"максимален\s+размер[:\s]*(\d+(?:\.\d+)?)"
            ],
            "eligibility_patterns": [
                r"критерии\s+за\s+допустимост",
                r"условия\s+за\s+участие",
                r"изисквания\s+към\s+кандидатите",
                r"целеви\s+групи"
            ]
        }
        
        # Text quality indicators
        self.quality_indicators = {
            "positive": [
                "програма", "финансиране", "кандидатстване", "проект",
                "безвъзмездна помощ", "европейски фондове", "критерии",
                "допустимост", "срок", "бюджет", "дейности"
            ],
            "negative": [
                "реклама", "продажба", "купуване", "търговия",
                "лична информация", "cookies", "политика"
            ]
        }
    
    async def process_text(
        self,
        text: str,
        source_url: str = "",
        preserve_structure: bool = True
    ) -> ProcessedText:
        """
        Process Bulgarian text with comprehensive analysis.
        
        Args:
            text: Raw text content
            source_url: Source URL for context
            preserve_structure: Whether to preserve document structure
            
        Returns:
            ProcessedText with analysis and chunks
        """
        try:
            logger.info(f"📝 Processing Bulgarian text ({len(text)} chars)...")
            
            # Step 1: Text normalization and cleaning
            normalized_text = self._normalize_cyrillic(text)
            cleaned_text = self._clean_text(normalized_text)
            
            # Step 2: Language detection and validation
            language_metrics = self._analyze_language(cleaned_text)
            
            # Step 3: Extract structured information
            extracted_info = self._extract_funding_info(cleaned_text)
            
            # Step 4: Semantic chunking
            chunks = await self._create_semantic_chunks(
                cleaned_text, preserve_structure
            )
            
            # Step 5: Quality assessment
            quality_score = self._calculate_quality_score(
                cleaned_text, extracted_info, language_metrics
            )
            
            # Create processed text result
            processed = ProcessedText(
                original_text=text,
                cleaned_text=cleaned_text,
                language_metrics=language_metrics,
                extracted_info=extracted_info,
                chunks=chunks,
                quality_score=quality_score,
                processing_timestamp=datetime.now().isoformat(),
                source_url=source_url
            )
            
            logger.info(f"✅ Text processed: {len(chunks)} chunks, quality: {quality_score:.2f}")
            return processed
            
        except Exception as e:
            logger.error(f"❌ Text processing failed: {e}")
            raise
    
    def _normalize_cyrillic(self, text: str) -> str:
        """Normalize Cyrillic text and handle encoding issues."""
        # Unicode normalization
        text = unicodedata.normalize('NFC', text)
        
        # Fix common encoding issues
        replacements = {
            'Ð°': 'а', 'Ð±': 'б', 'Ð²': 'в', 'Ð³': 'г', 'Ð´': 'д',
            'Ðµ': 'е', 'Ð¶': 'ж', 'Ð·': 'з', 'Ð¸': 'и', 'Ð¹': 'й',
            'Ðº': 'к', 'Ð»': 'л', 'Ð¼': 'м', 'Ð½': 'н', 'Ð¾': 'о',
            'Ð¿': 'п', 'Ñ€': 'р', 'Ñ': 'с', 'Ñ‚': 'т', 'Ñƒ': 'у',
            'Ñ„': 'ф', 'Ñ…': 'х', 'Ñ†': 'ц', 'Ñ‡': 'ч', 'Ñˆ': 'ш',
            'Ñ‰': 'щ', 'ÑŠ': 'ъ', 'ÑŒ': 'ь', 'ÑŽ': 'ю', 'Ñ': 'я'
        }
        
        for wrong, correct in replacements.items():
            text = text.replace(wrong, correct)
        
        return text
    
    def _clean_text(self, text: str) -> str:
        """Clean and standardize text content."""
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove HTML entities
        text = re.sub(r'&[a-zA-Z0-9#]+;', ' ', text)
        
        # Standardize punctuation
        text = re.sub(r'[""„"]', '"', text)
        text = re.sub(r'[''`]', "'", text)
        text = re.sub(r'[–—]', '-', text)
        
        # Remove excessive punctuation
        text = re.sub(r'[.]{3,}', '...', text)
        text = re.sub(r'[!]{2,}', '!', text)
        text = re.sub(r'[?]{2,}', '?', text)
        
        # Clean up spacing around punctuation
        text = re.sub(r'\s+([,.!?;:])', r'\1', text)
        text = re.sub(r'([,.!?;:])\s+', r'\1 ', text)
        
        return text.strip()
    
    def _analyze_language(self, text: str) -> LanguageMetrics:
        """Analyze language characteristics of the text."""
        # Character analysis
        total_chars = len(text)
        cyrillic_chars = len(re.findall(r'[а-яё]', text, re.IGNORECASE))
        latin_chars = len(re.findall(r'[a-z]', text, re.IGNORECASE))
        
        # Word analysis
        words = text.split()
        total_words = len(words)
        bulgarian_words = sum(1 for word in words if re.search(r'[а-яё]', word))
        
        # Calculate ratios
        cyrillic_ratio = cyrillic_chars / max(total_chars, 1)
        bulgarian_word_ratio = bulgarian_words / max(total_words, 1)
        
        # Detect primary language
        if cyrillic_ratio > 0.3 or bulgarian_word_ratio > 0.4:
            primary_language = "bg"
            confidence = max(cyrillic_ratio, bulgarian_word_ratio)
        else:
            primary_language = "en"
            confidence = 1.0 - max(cyrillic_ratio, bulgarian_word_ratio)
        
        # Calculate bulgarian ratio
        bulgarian_ratio = bulgarian_words / max(total_words, 1)

        # Extract funding terms
        funding_terms = []
        for pattern in self.funding_patterns["program_indicators"]:
            matches = re.findall(pattern, text, re.IGNORECASE)
            funding_terms.extend(matches)

        return LanguageMetrics(
            primary_language=primary_language,
            confidence=confidence,
            cyrillic_ratio=cyrillic_ratio,
            bulgarian_ratio=bulgarian_ratio,
            total_words=total_words,
            bulgarian_words=bulgarian_words,
            avg_word_length=sum(len(word) for word in words) / max(total_words, 1),
            funding_terms=funding_terms
        )
    
    def _extract_funding_info(self, text: str) -> Dict[str, Any]:
        """Extract structured funding information from text."""
        extracted = {
            "programs": [],
            "deadlines": [],
            "budgets": [],
            "eligibility": [],
            "contacts": []
        }
        
        # Extract funding programs
        for pattern in self.funding_patterns["program_indicators"]:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                extracted["programs"].append(match.group().strip())
        
        # Extract deadlines
        for pattern in self.funding_patterns["deadline_patterns"]:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                if match.groups():
                    extracted["deadlines"].append(match.group(1))
                else:
                    extracted["deadlines"].append(match.group().strip())
        
        # Extract budget information
        for pattern in self.funding_patterns["budget_patterns"]:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                extracted["budgets"].append(match.group().strip())
        
        # Extract eligibility criteria
        for pattern in self.funding_patterns["eligibility_patterns"]:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                # Extract surrounding context
                start = max(0, match.start() - 100)
                end = min(len(text), match.end() + 200)
                context = text[start:end].strip()
                extracted["eligibility"].append(context)
        
        # Extract contact information
        email_pattern = r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}'
        phone_pattern = r'(?:\+359|0)\s*[0-9\s\-\(\)]{8,15}'
        
        emails = re.findall(email_pattern, text)
        phones = re.findall(phone_pattern, text)
        
        extracted["contacts"] = {
            "emails": emails,
            "phones": phones
        }
        
        return extracted
    
    async def _create_semantic_chunks(
        self,
        text: str,
        preserve_structure: bool = True
    ) -> List[TextChunk]:
        """Create semantic chunks optimized for Bulgarian content."""
        chunks = []
        
        if preserve_structure:
            # Split by headers and major sections
            sections = re.split(r'\n\s*(?=[А-ЯA-Z][^.]*:|\d+\.\s+[А-ЯA-Z])', text)
        else:
            # Split by paragraphs
            sections = re.split(r'\n\s*\n', text)
        
        chunk_id = 0
        for section in sections:
            section = section.strip()
            if len(section) < 50:  # Skip very short sections
                continue
            
            # Further split long sections
            if len(section) > 1000:
                subsections = self._split_long_section(section)
                for subsection in subsections:
                    if len(subsection.strip()) >= 50:
                        chunk = self._create_chunk(subsection, chunk_id)
                        chunks.append(chunk)
                        chunk_id += 1
            else:
                chunk = self._create_chunk(section, chunk_id)
                chunks.append(chunk)
                chunk_id += 1
        
        return chunks
    
    def _split_long_section(self, text: str, max_length: int = 800) -> List[str]:
        """Split long sections while preserving sentence boundaries."""
        sentences = re.split(r'(?<=[.!?])\s+', text)
        chunks = []
        current_chunk = ""
        
        for sentence in sentences:
            if len(current_chunk) + len(sentence) <= max_length:
                current_chunk += sentence + " "
            else:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                current_chunk = sentence + " "
        
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        return chunks
    
    def _create_chunk(self, text: str, chunk_id: int) -> TextChunk:
        """Create a text chunk with metadata."""
        # Remove stopwords for keyword extraction
        words = text.lower().split()
        keywords = [word for word in words if word not in self.stopwords and len(word) > 2]
        
        # Extract key phrases (2-3 word combinations)
        key_phrases = []
        for i in range(len(keywords) - 1):
            phrase = f"{keywords[i]} {keywords[i+1]}"
            if any(funding_word in phrase for funding_word in ["програма", "финансиране", "фонд", "проект"]):
                key_phrases.append(phrase)
        
        return TextChunk(
            id=chunk_id,
            text=text,
            word_count=len(words),
            keywords=keywords[:10],  # Top 10 keywords
            key_phrases=key_phrases[:5],  # Top 5 key phrases
            has_funding_info=any(
                keyword in text.lower() 
                for keyword in ["програма", "финансиране", "фонд", "срок", "критерии"]
            ),
            language="bg" if re.search(r'[а-яё]', text) else "en"
        )
    
    def _calculate_quality_score(
        self,
        text: str,
        extracted_info: Dict[str, Any],
        language_metrics: LanguageMetrics
    ) -> float:
        """Calculate content quality score for Bulgarian funding content."""
        score = 0.0
        
        # Base score for text length
        if len(text) > 500:
            score += 0.2
        elif len(text) > 200:
            score += 0.1
        
        # Language quality
        if language_metrics.primary_language == "bg":
            score += 0.2 * language_metrics.confidence
        
        # Structured information bonus
        if extracted_info["programs"]:
            score += 0.15
        if extracted_info["deadlines"]:
            score += 0.15
        if extracted_info["budgets"]:
            score += 0.1
        if extracted_info["eligibility"]:
            score += 0.1
        
        # Positive keywords bonus
        positive_count = sum(
            1 for keyword in self.quality_indicators["positive"]
            if keyword in text.lower()
        )
        score += min(0.1, positive_count * 0.02)
        
        # Negative keywords penalty
        negative_count = sum(
            1 for keyword in self.quality_indicators["negative"]
            if keyword in text.lower()
        )
        score -= min(0.1, negative_count * 0.03)
        
        return max(0.0, min(1.0, score))

# Global text processor instance
text_processor = BulgarianTextProcessor()
