"""
Performance monitoring and metrics collection for EU Funds MCP Server.
Tracks response times, throughput, and system health metrics.
"""

import asyncio
import time
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from collections import defaultdict, deque
import psutil
import threading

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetrics:
    """Performance metrics data structure."""
    
    # Response time metrics
    avg_response_time: float = 0.0
    min_response_time: float = float('inf')
    max_response_time: float = 0.0
    p95_response_time: float = 0.0
    p99_response_time: float = 0.0
    
    # Throughput metrics
    requests_per_second: float = 0.0
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    
    # System metrics
    cpu_usage: float = 0.0
    memory_usage: float = 0.0
    memory_usage_mb: float = 0.0
    
    # Cache metrics
    cache_hit_rate: float = 0.0
    cache_size: int = 0
    
    # Database metrics
    db_connection_count: int = 0
    avg_db_query_time: float = 0.0
    
    # Timestamp
    timestamp: datetime = field(default_factory=datetime.now)


class PerformanceMonitor:
    """
    Performance monitoring system for EU Funds MCP Server.
    Collects and analyzes performance metrics in real-time.
    """
    
    def __init__(self, window_size: int = 1000):
        """
        Initialize performance monitor.
        
        Args:
            window_size: Number of recent measurements to keep for calculations
        """
        self.window_size = window_size
        self.response_times = deque(maxlen=window_size)
        self.request_timestamps = deque(maxlen=window_size)
        
        # Counters
        self.total_requests = 0
        self.successful_requests = 0
        self.failed_requests = 0
        
        # Tool-specific metrics
        self.tool_metrics = defaultdict(lambda: {
            'response_times': deque(maxlen=window_size),
            'requests': 0,
            'successes': 0,
            'failures': 0
        })
        
        # Database query metrics
        self.db_query_times = deque(maxlen=window_size)
        
        # System monitoring
        self.system_monitor_active = False
        self.system_metrics_history = deque(maxlen=100)  # Last 100 system snapshots
        
        # Lock for thread safety
        self._lock = threading.Lock()
        
        logger.info("📊 Performance monitor initialized")
    
    def start_system_monitoring(self, interval: float = 5.0):
        """Start background system monitoring."""
        if self.system_monitor_active:
            return
        
        self.system_monitor_active = True
        
        def monitor_system():
            while self.system_monitor_active:
                try:
                    # Collect system metrics
                    cpu_percent = psutil.cpu_percent(interval=1)
                    memory = psutil.virtual_memory()
                    
                    with self._lock:
                        self.system_metrics_history.append({
                            'timestamp': datetime.now(),
                            'cpu_usage': cpu_percent,
                            'memory_usage': memory.percent,
                            'memory_usage_mb': memory.used / (1024 * 1024)
                        })
                    
                    time.sleep(interval)
                    
                except Exception as e:
                    logger.error(f"❌ System monitoring error: {e}")
                    time.sleep(interval)
        
        # Start monitoring in background thread
        monitor_thread = threading.Thread(target=monitor_system, daemon=True)
        monitor_thread.start()
        
        logger.info("🔍 System monitoring started")
    
    def stop_system_monitoring(self):
        """Stop background system monitoring."""
        self.system_monitor_active = False
        logger.info("⏹️ System monitoring stopped")
    
    def record_request(self, response_time: float, success: bool = True, tool_name: Optional[str] = None):
        """
        Record a request with its response time and success status.
        
        Args:
            response_time: Response time in seconds
            success: Whether the request was successful
            tool_name: Name of the MCP tool (optional)
        """
        with self._lock:
            current_time = time.time()
            
            # Record overall metrics
            self.response_times.append(response_time)
            self.request_timestamps.append(current_time)
            self.total_requests += 1
            
            if success:
                self.successful_requests += 1
            else:
                self.failed_requests += 1
            
            # Record tool-specific metrics
            if tool_name:
                tool_data = self.tool_metrics[tool_name]
                tool_data['response_times'].append(response_time)
                tool_data['requests'] += 1
                
                if success:
                    tool_data['successes'] += 1
                else:
                    tool_data['failures'] += 1
    
    def record_db_query(self, query_time: float):
        """Record database query time."""
        with self._lock:
            self.db_query_times.append(query_time)
    
    def get_metrics(self) -> PerformanceMetrics:
        """Get current performance metrics."""
        with self._lock:
            metrics = PerformanceMetrics()
            
            # Response time metrics
            if self.response_times:
                sorted_times = sorted(self.response_times)
                metrics.avg_response_time = sum(sorted_times) / len(sorted_times)
                metrics.min_response_time = sorted_times[0]
                metrics.max_response_time = sorted_times[-1]
                
                # Calculate percentiles
                if len(sorted_times) >= 20:  # Only calculate if we have enough data
                    p95_idx = int(len(sorted_times) * 0.95)
                    p99_idx = int(len(sorted_times) * 0.99)
                    metrics.p95_response_time = sorted_times[p95_idx]
                    metrics.p99_response_time = sorted_times[p99_idx]
            
            # Throughput metrics
            if self.request_timestamps:
                # Calculate requests per second over last minute
                current_time = time.time()
                recent_requests = [
                    ts for ts in self.request_timestamps 
                    if current_time - ts <= 60
                ]
                metrics.requests_per_second = len(recent_requests) / 60.0
            
            metrics.total_requests = self.total_requests
            metrics.successful_requests = self.successful_requests
            metrics.failed_requests = self.failed_requests
            
            # System metrics (latest values)
            if self.system_metrics_history:
                latest_system = self.system_metrics_history[-1]
                metrics.cpu_usage = latest_system['cpu_usage']
                metrics.memory_usage = latest_system['memory_usage']
                metrics.memory_usage_mb = latest_system['memory_usage_mb']
            
            # Database metrics
            if self.db_query_times:
                metrics.avg_db_query_time = sum(self.db_query_times) / len(self.db_query_times)
            
            return metrics
    
    def get_tool_metrics(self) -> Dict[str, Dict[str, Any]]:
        """Get tool-specific performance metrics."""
        with self._lock:
            result = {}
            
            for tool_name, data in self.tool_metrics.items():
                response_times = list(data['response_times'])
                
                tool_stats = {
                    'requests': data['requests'],
                    'successes': data['successes'],
                    'failures': data['failures'],
                    'success_rate': data['successes'] / max(data['requests'], 1),
                    'avg_response_time': sum(response_times) / len(response_times) if response_times else 0.0,
                    'min_response_time': min(response_times) if response_times else 0.0,
                    'max_response_time': max(response_times) if response_times else 0.0
                }
                
                result[tool_name] = tool_stats
            
            return result
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get overall system health status."""
        metrics = self.get_metrics()
        
        # Define health thresholds
        health_status = "healthy"
        issues = []
        
        # Check response time
        if metrics.avg_response_time > 2.0:  # 2 seconds threshold
            health_status = "degraded"
            issues.append(f"High average response time: {metrics.avg_response_time:.2f}s")
        
        # Check error rate
        error_rate = metrics.failed_requests / max(metrics.total_requests, 1)
        if error_rate > 0.05:  # 5% error rate threshold
            health_status = "unhealthy"
            issues.append(f"High error rate: {error_rate:.1%}")
        
        # Check system resources
        if metrics.cpu_usage > 80:
            health_status = "degraded"
            issues.append(f"High CPU usage: {metrics.cpu_usage:.1f}%")
        
        if metrics.memory_usage > 85:
            health_status = "degraded"
            issues.append(f"High memory usage: {metrics.memory_usage:.1f}%")
        
        return {
            "status": health_status,
            "issues": issues,
            "metrics": metrics,
            "timestamp": datetime.now().isoformat()
        }
    
    def reset_metrics(self):
        """Reset all metrics."""
        with self._lock:
            self.response_times.clear()
            self.request_timestamps.clear()
            self.db_query_times.clear()
            self.system_metrics_history.clear()
            
            self.total_requests = 0
            self.successful_requests = 0
            self.failed_requests = 0
            
            self.tool_metrics.clear()
            
        logger.info("🔄 Performance metrics reset")


# Global performance monitor instance
_global_monitor: Optional[PerformanceMonitor] = None


def get_performance_monitor() -> PerformanceMonitor:
    """Get global performance monitor instance."""
    global _global_monitor
    if _global_monitor is None:
        _global_monitor = PerformanceMonitor()
        _global_monitor.start_system_monitoring()
    return _global_monitor


def cleanup_performance_monitor():
    """Cleanup performance monitor resources."""
    global _global_monitor
    if _global_monitor:
        _global_monitor.stop_system_monitoring()
        _global_monitor = None
    logger.info("✅ Performance monitor cleanup complete")
