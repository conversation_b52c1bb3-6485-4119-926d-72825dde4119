#!/usr/bin/env python3
"""
Clear all old data from Supabase tables and start fresh.
"""

import asyncio
import sys
import os
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def clear_all_data():
    """Clear all data from Supabase tables."""
    print("🧹 ИЗЧИСТВАНЕ НА ВСИЧКИ СТАРИ ДАННИ")
    print("=" * 50)
    
    # Initialize Supabase client
    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_SERVICE_KEY") or os.getenv("SUPABASE_KEY")
    
    if not url or not key:
        print("❌ Няма SUPABASE credentials")
        return False
    
    supabase: Client = create_client(url, key)
    
    try:
        # List of tables to clear
        tables_to_clear = [
            'eu_funds_content',
            'documents', 
            'content', 
            'chunks', 
            'embeddings',
            'crawled_content',
            'vector_store',
            'crawl_results',
            'processed_content'
        ]
        
        cleared_tables = []
        
        for table_name in tables_to_clear:
            try:
                # Check if table exists and has data
                result = supabase.table(table_name).select("id", count="exact").limit(1).execute()
                count = result.count if hasattr(result, 'count') else len(result.data)
                
                if count > 0:
                    print(f"🗑️ Изчистване на {table_name} ({count} записа)...")
                    
                    # Delete all records
                    delete_result = supabase.table(table_name).delete().neq('id', '00000000-0000-0000-0000-000000000000').execute()
                    
                    # Verify deletion
                    verify_result = supabase.table(table_name).select("id", count="exact").limit(1).execute()
                    remaining = verify_result.count if hasattr(verify_result, 'count') else len(verify_result.data)
                    
                    if remaining == 0:
                        print(f"   ✅ {table_name} изчистена успешно")
                        cleared_tables.append(table_name)
                    else:
                        print(f"   ⚠️ {table_name} частично изчистена ({remaining} остават)")
                else:
                    print(f"   ℹ️ {table_name} вече е празна")
                    
            except Exception as e:
                if "does not exist" in str(e) or "relation" in str(e):
                    print(f"   ℹ️ {table_name} не съществува")
                else:
                    print(f"   ❌ Грешка при {table_name}: {e}")
        
        print(f"\n📊 РЕЗУЛТАТ:")
        print(f"   ✅ Изчистени таблици: {len(cleared_tables)}")
        if cleared_tables:
            for table in cleared_tables:
                print(f"      • {table}")
        
        print(f"\n🎯 ГОТОВО ЗА НОВО CRAWLING!")
        return True
        
    except Exception as e:
        print(f"❌ Обща грешка: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(clear_all_data())
    if success:
        print("\n✅ Базата данни е изчистена и готова за ново crawling!")
    else:
        print("\n❌ Има проблеми с изчистването!")
        exit(1)
