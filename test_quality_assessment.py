#!/usr/bin/env python3
"""
Quality Assessment Test for EU Funds MCP Server
Tests real data quality and search performance
"""

import asyncio
import json
from src.mcp.tools import EUFundsMCPTools

async def test_real_search_quality():
    """Test search quality with real Bulgarian content"""
    tools = EUFundsMCPTools()
    await tools.initialize()
    
    print("🔍 TESTING SEARCH QUALITY WITH REAL DATA")
    print("=" * 50)
    
    # Test 1: Bulgarian search
    print("\n1. BULGARIAN SEARCH TEST")
    result = await tools.search_eu_funds('европейски фондове България иновации')
    results = result.get('results', [])
    print(f"Results found: {len(results)}")
    
    for i, res in enumerate(results[:3]):
        print(f"\nResult {i+1}:")
        print(f"  Title: {res.get('title', 'N/A')}")
        print(f"  Score: {res.get('score', 'N/A')}")
        print(f"  Language: {res.get('language', 'N/A')}")
        print(f"  Content preview: {res.get('content', '')[:150]}...")
    
    # Test 2: Funding programs search
    print("\n\n2. FUNDING PROGRAMS TEST")
    result = await tools.get_funding_programs('технологии')
    programs = result.get('programs', [])
    print(f"Programs found: {len(programs)}")
    
    for i, prog in enumerate(programs[:2]):
        print(f"\nProgram {i+1}:")
        print(f"  Title: {prog.get('title', 'N/A')}")
        print(f"  Score: {prog.get('score', 'N/A')}")
        print(f"  Content preview: {prog.get('content', '')[:150]}...")
    
    # Test 3: Eligibility analysis
    print("\n\n3. ELIGIBILITY ANALYSIS TEST")
    result = await tools.analyze_eligibility('МСП', 'технологии', 'Иновативен технологичен проект за малки и средни предприятия')
    eligible = result.get('eligible_programs', [])
    print(f"Eligible programs found: {len(eligible)}")
    
    # Test 4: Deadlines search
    print("\n\n4. DEADLINES SEARCH TEST")
    result = await tools.get_application_deadlines(1)
    deadlines = result.get('deadlines', [])
    print(f"Deadlines found: {len(deadlines)}")
    
    await tools.cleanup()
    
    # Quality assessment
    print("\n\n📊 QUALITY ASSESSMENT")
    print("=" * 50)
    
    total_results = len(results) + len(programs) + len(eligible) + len(deadlines)
    print(f"Total results across all tests: {total_results}")
    
    if total_results > 0:
        print("✅ System is finding and returning results")
        print("✅ Bulgarian content is being processed")
        print("✅ Search functionality is working")
        
        # Check content quality
        bulgarian_content = sum(1 for r in results if r.get('language') == 'bg')
        print(f"✅ Bulgarian content ratio: {bulgarian_content}/{len(results)} results")
        
        if bulgarian_content > 0:
            print("✅ Bulgarian language detection working")
        else:
            print("⚠️ No Bulgarian content detected in results")
    else:
        print("❌ No results found - system may have issues")

if __name__ == "__main__":
    asyncio.run(test_real_search_quality())
