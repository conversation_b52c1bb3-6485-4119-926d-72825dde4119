#!/usr/bin/env python3
"""
Analyze current data in Supabase to understand why we have so few records.
"""

import asyncio
import sys
import os
from supabase import create_client, Client
from dotenv import load_dotenv
from collections import Counter

# Load environment variables
load_dotenv()

async def analyze_data():
    """Analyze current data in detail."""
    print("🔍 ДЕТАЙЛЕН АНАЛИЗ НА ТЕКУЩИТЕ ДАННИ")
    print("=" * 50)
    
    # Initialize Supabase client
    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_SERVICE_KEY") or os.getenv("SUPABASE_KEY")
    
    if not url or not key:
        print("❌ Няма SUPABASE credentials")
        return
    
    supabase: Client = create_client(url, key)
    
    try:
        # Get all records
        result = supabase.table('eu_funds_content').select("*").execute()
        records = result.data
        
        print(f"📊 ОБЩО ЗАПИСИ: {len(records)}")
        print()
        
        # Analyze URLs
        urls = [record.get('source_url', 'N/A') for record in records]
        url_counter = Counter(urls)
        
        print("🌐 АНАЛИЗ НА URL-И:")
        for url, count in url_counter.most_common():
            print(f"   {count}x: {url}")
        print()
        
        # Analyze content lengths
        content_lengths = [len(record.get('content', '')) for record in records]
        avg_length = sum(content_lengths) / len(content_lengths) if content_lengths else 0
        
        print("📝 АНАЛИЗ НА СЪДЪРЖАНИЕТО:")
        print(f"   Средна дължина: {avg_length:.0f} символа")
        print(f"   Най-кратко: {min(content_lengths)} символа")
        print(f"   Най-дълго: {max(content_lengths)} символа")
        print()
        
        # Analyze content types
        content_types = [record.get('content_type', 'N/A') for record in records]
        type_counter = Counter(content_types)
        
        print("📋 ТИПОВЕ СЪДЪРЖАНИЕ:")
        for content_type, count in type_counter.items():
            print(f"   {count}x: {content_type}")
        print()
        
        # Show sample content
        print("📄 ПРИМЕРНО СЪДЪРЖАНИЕ:")
        for i, record in enumerate(records[:3]):
            print(f"\n--- ЗАПИС {i+1} ---")
            print(f"Title: {record.get('title', 'N/A')}")
            print(f"URL: {record.get('source_url', 'N/A')}")
            print(f"Type: {record.get('content_type', 'N/A')}")
            print(f"Length: {len(record.get('content', ''))} символа")
            
            content = record.get('content', '')
            if content:
                # Show first 200 chars
                preview = content[:200] + "..." if len(content) > 200 else content
                print(f"Content: {preview}")
        
        # Check for duplicates
        content_hashes = {}
        duplicates = 0
        for record in records:
            content = record.get('content', '')
            content_hash = hash(content)
            if content_hash in content_hashes:
                duplicates += 1
            else:
                content_hashes[content_hash] = record.get('id')
        
        print(f"\n🔄 ДУБЛИРАНИ ЗАПИСИ: {duplicates}")
        
        # Analyze quality scores
        quality_scores = [record.get('quality_score', 0) for record in records if record.get('quality_score')]
        if quality_scores:
            avg_quality = sum(quality_scores) / len(quality_scores)
            print(f"⭐ СРЕДНО КАЧЕСТВО: {avg_quality:.2f}")
        
        # Check metadata
        print(f"\n🏷️ МЕТАДАННИ:")
        metadata_keys = set()
        for record in records:
            metadata = record.get('metadata', {})
            if isinstance(metadata, dict):
                metadata_keys.update(metadata.keys())
        
        print(f"   Ключове в metadata: {list(metadata_keys)}")
        
        # Recommendations
        print(f"\n💡 ПРЕПОРЪКИ:")
        if len(records) < 50:
            print("   ⚠️ Малко записи - нужно е повече crawling")
        if duplicates > 0:
            print(f"   🔄 {duplicates} дублирани записи - нужно почистване")
        if avg_length < 500:
            print("   📝 Кратко съдържание - може да се подобри chunking")
        if len(set(urls)) == 1:
            print("   🌐 Само един URL - нужно crawling на повече страници")
            
    except Exception as e:
        print(f"❌ Грешка: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(analyze_data())
