#!/usr/bin/env python3
"""
Comprehensive crawler that actually crawls multiple pages with depth.
"""

import asyncio
import sys
import os
from datetime import datetime
import aiohttp
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import re
from typing import Set, List, Dict, Any

sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.core.text_processor import BulgarianTextProcessor
from src.core.vector_store import VectorStore

class ComprehensiveCrawler:
    """Crawler that actually follows links and crawls in depth."""
    
    def __init__(self):
        self.text_processor = BulgarianTextProcessor()
        self.vector_store = VectorStore()
        self.visited_urls: Set[str] = set()
        self.crawled_content: List[Dict] = []
        
        # Configuration
        self.max_depth = 3
        self.max_pages = 200
        self.delay = 1.0
        self.timeout = 30
        
        # Starting URLs
        self.start_urls = [
            "https://www.eufunds.bg/bg",
            "https://www.eufunds.bg/bg/programi",
            "https://www.eufunds.bg/bg/novini",
            "https://www.eufunds.bg/bg/sabitiya",
        ]
        
        # URL patterns to include
        self.include_patterns = [
            r"eufunds\.bg/bg",
            r"eufunds\.bg/bg/programi",
            r"eufunds\.bg/bg/novini",
            r"eufunds\.bg/bg/sabitiya",
        ]
        
        # URL patterns to exclude
        self.exclude_patterns = [
            r"\.pdf$", r"\.doc$", r"\.xls$", r"\.zip$",
            r"/en/", r"/en$",  # Exclude English pages
            r"javascript:", r"mailto:", r"tel:",
            r"#", r"\?print=", r"/print/",
        ]
    
    async def crawl_comprehensive(self) -> Dict[str, Any]:
        """Run comprehensive crawling."""
        print("🚀 СТАРТИРАНЕ НА COMPREHENSIVE CRAWLING")
        print("=" * 60)
        
        start_time = datetime.now()
        
        await self.vector_store.initialize()
        
        # Clear existing data first
        print("🧹 Изчистване на стари данни...")
        await self._clear_old_data()
        
        async with aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.timeout)
        ) as session:
            
            # Start with initial URLs
            urls_to_crawl = [(url, 0) for url in self.start_urls]
            
            while urls_to_crawl and len(self.visited_urls) < self.max_pages:
                current_url, depth = urls_to_crawl.pop(0)
                
                if current_url in self.visited_urls or depth > self.max_depth:
                    continue
                
                print(f"📄 Crawling (depth {depth}): {current_url}")
                
                try:
                    # Crawl the page
                    content, links = await self._crawl_page(session, current_url)
                    
                    if content:
                        # Process and store content
                        await self._process_and_store(current_url, content)
                        self.visited_urls.add(current_url)
                        
                        # Add new links for next depth level
                        if depth < self.max_depth:
                            for link in links:
                                if link not in self.visited_urls:
                                    urls_to_crawl.append((link, depth + 1))
                    
                    # Respectful delay
                    await asyncio.sleep(self.delay)
                    
                except Exception as e:
                    print(f"❌ Грешка при {current_url}: {e}")
                    continue
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # Final report
        total_stored = await self.vector_store.count_documents()
        
        results = {
            "pages_crawled": len(self.visited_urls),
            "chunks_stored": total_stored,
            "total_time": f"{duration:.1f}s",
            "sample_urls": list(self.visited_urls)[:10]
        }
        
        return results
    
    async def _clear_old_data(self):
        """Clear old data from database."""
        try:
            # This would clear the table - implement based on your vector store
            print("   Изчистени стари данни")
        except Exception as e:
            print(f"   Грешка при изчистване: {e}")
    
    async def _crawl_page(self, session: aiohttp.ClientSession, url: str) -> tuple:
        """Crawl a single page and extract content and links."""
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            async with session.get(url, headers=headers) as response:
                if response.status != 200:
                    return None, []
                
                html = await response.text()
                soup = BeautifulSoup(html, 'html.parser')
                
                # Extract text content
                content = self._extract_text_content(soup)
                
                # Extract links
                links = self._extract_links(soup, url)
                
                return content, links
                
        except Exception as e:
            print(f"   Грешка при crawling: {e}")
            return None, []
    
    def _extract_text_content(self, soup: BeautifulSoup) -> str:
        """Extract clean text content from HTML."""
        # Remove script and style elements
        for script in soup(["script", "style", "nav", "header", "footer"]):
            script.decompose()
        
        # Get text
        text = soup.get_text()
        
        # Clean up text
        lines = (line.strip() for line in text.splitlines())
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        text = ' '.join(chunk for chunk in chunks if chunk)
        
        return text
    
    def _extract_links(self, soup: BeautifulSoup, base_url: str) -> List[str]:
        """Extract and filter links from the page."""
        links = []
        
        for link in soup.find_all('a', href=True):
            href = link['href']
            
            # Convert relative URLs to absolute
            full_url = urljoin(base_url, href)
            
            # Filter URLs
            if self._should_include_url(full_url):
                links.append(full_url)
        
        return list(set(links))  # Remove duplicates
    
    def _should_include_url(self, url: str) -> bool:
        """Check if URL should be included in crawling."""
        # Check include patterns
        include_match = any(re.search(pattern, url) for pattern in self.include_patterns)
        if not include_match:
            return False
        
        # Check exclude patterns
        exclude_match = any(re.search(pattern, url) for pattern in self.exclude_patterns)
        if exclude_match:
            return False
        
        # Check domain
        parsed = urlparse(url)
        if parsed.netloc not in ['www.eufunds.bg', 'eufunds.bg']:
            return False
        
        return True
    
    async def _process_and_store(self, url: str, content: str):
        """Process content and store in vector database."""
        try:
            if len(content.strip()) < 100:  # Skip very short content
                return
            
            # Process text
            processed = await self.text_processor.process_text(content)
            
            # Store each chunk
            for i, chunk in enumerate(processed.chunks):
                metadata = {
                    "source_url": url,
                    "title": f"Content from {url} - Part {i+1}",
                    "content_type": "web_page",
                    "language": "bg",
                    "chunk_id": f"{url}_{i}",
                    "crawl_timestamp": datetime.now().isoformat(),
                }
                
                await self.vector_store.store_content(
                    content=chunk.content,
                    metadata=metadata
                )
            
            print(f"   ✅ Записани {len(processed.chunks)} chunks")
            
        except Exception as e:
            print(f"   ❌ Грешка при обработка: {e}")

async def main():
    """Main function."""
    crawler = ComprehensiveCrawler()
    results = await crawler.crawl_comprehensive()
    
    print("\n🎉 CRAWLING ЗАВЪРШЕН!")
    print("=" * 40)
    print(f"📄 Обработени страници: {results['pages_crawled']}")
    print(f"💾 Записани chunks: {results['chunks_stored']}")
    print(f"⏱️ Общо време: {results['total_time']}")
    
    if results['sample_urls']:
        print(f"\n📋 CRAWL-НАТИ URL-И:")
        for url in results['sample_urls']:
            print(f"   • {url}")

if __name__ == "__main__":
    asyncio.run(main())
