#!/usr/bin/env python3
"""
Test different vector formats for Supabase.
"""

import asyncio
import sys
import os
from supabase import create_client, Client
from dotenv import load_dotenv
import openai

# Load environment variables
load_dotenv()

async def test_vector_formats():
    """Test different vector formats."""
    print("🧪 ТЕСТВАНЕ НА РАЗЛИЧНИ VECTOR ФОРМАТИ")
    print("=" * 50)
    
    # Initialize Supabase client
    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_SERVICE_KEY") or os.getenv("SUPABASE_KEY")
    supabase: Client = create_client(url, key)
    
    # Initialize OpenAI
    openai.api_key = os.getenv("OPENAI_API_KEY")
    
    # Create a small test embedding
    print("🔧 Създаване на тестов embedding...")
    try:
        response = openai.embeddings.create(
            model="text-embedding-3-small",
            input="тест"
        )
        
        original_embedding = response.data[0].embedding
        print(f"   ✅ Оригинален embedding: {len(original_embedding)} dimensions")
        
        # Test different formats
        formats_to_test = [
            ("list", original_embedding),
            ("string_brackets", "[" + ",".join(map(str, original_embedding)) + "]"),
            ("string_no_brackets", ",".join(map(str, original_embedding))),
            ("string_spaces", " ".join(map(str, original_embedding))),
        ]
        
        for format_name, embedding_data in formats_to_test:
            print(f"\n🧪 Тестване на формат: {format_name}")
            
            try:
                test_data = {
                    "title": f"Тест {format_name}",
                    "content": "Тестово съдържание",
                    "source_url": "https://test.com",
                    "content_type": "test",
                    "language": "bg",
                    "metadata": {"format": format_name},
                    "quality_score": 0.8,
                    "embedding": embedding_data
                }
                
                result = supabase.table('eu_funds_content').insert(test_data).execute()
                
                if result.data:
                    inserted_id = result.data[0]['id']
                    print(f"   ✅ Записан с ID: {inserted_id}")
                    
                    # Retrieve and check
                    retrieved = supabase.table('eu_funds_content').select("*").eq('id', inserted_id).execute()
                    
                    if retrieved.data:
                        stored_embedding = retrieved.data[0]['embedding']
                        print(f"   📊 Записан размер: {len(stored_embedding) if stored_embedding else 'None'}")
                        print(f"   🔍 Тип: {type(stored_embedding)}")
                        
                        if isinstance(stored_embedding, list) and len(stored_embedding) == 1536:
                            print(f"   🎉 УСПЕХ! Правилен формат!")
                        else:
                            print(f"   ❌ Неправилен формат")
                    
                    # Clean up
                    supabase.table('eu_funds_content').delete().eq('id', inserted_id).execute()
                    
                else:
                    print(f"   ❌ Неуспешно записване")
                    
            except Exception as e:
                print(f"   ❌ Грешка: {e}")
        
        # Test using SQL function for vector conversion
        print(f"\n🧪 Тестване с SQL vector() функция...")
        try:
            # Try using raw SQL to insert with vector() function
            embedding_str = "[" + ",".join(map(str, original_embedding[:5])) + "]"  # Small test
            
            sql_query = f"""
            INSERT INTO eu_funds_content (title, content, source_url, content_type, language, metadata, quality_score, embedding)
            VALUES ('SQL Test', 'Test content', 'https://test.com', 'test', 'bg', '{{"test": true}}', 0.8, '{embedding_str}'::vector);
            """
            
            print(f"   SQL: {sql_query[:100]}...")
            # This might not work directly through supabase client
            print(f"   ⚠️ SQL метод не може да се тества директно")
            
        except Exception as e:
            print(f"   ❌ SQL грешка: {e}")
            
    except Exception as e:
        print(f"❌ Грешка при OpenAI: {e}")

if __name__ == "__main__":
    asyncio.run(test_vector_formats())
