# Environment variables and secrets
.env
.env.local
.env.production
.env.staging
*.key
*.secret
config/secrets.json

# Database files
*.db
*.sqlite
*.sqlite3

# Logs
logs/
*.log

# Cache and temporary files
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
*.so
*.tmp
*.temp
temp/

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Node modules
node_modules/

# Build artifacts
build/
dist/
*.egg-info/

# Test coverage
.coverage
htmlcov/
.pytest_cache/

# Crawl reports (contain timestamps)
crawl_report_*.json

# MCP logs
mcp_*.log
