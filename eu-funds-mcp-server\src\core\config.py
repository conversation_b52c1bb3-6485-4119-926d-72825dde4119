"""
Core configuration management for EU Funds MCP Server.
Optimized for low RAM usage and production deployment.
"""

import os
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

from pydantic import Field, validator
from pydantic_settings import BaseSettings, SettingsConfigDict
from dotenv import load_dotenv

# Load .env file from project root
project_root = Path(__file__).parent.parent.parent
env_path = project_root / ".env"
if env_path.exists():
    load_dotenv(env_path)


class DatabaseSettings(BaseSettings):
    """Database configuration settings."""
    
    # Supabase Configuration
    supabase_url: str = Field(..., description="Supabase project URL")
    supabase_service_key: str = Field(..., description="Supabase service role key")
    supabase_anon_key: Optional[str] = Field(None, description="Supabase anon key")
    
    # PostgreSQL Direct Connection (fallback)
    postgres_host: str = Field("localhost", description="PostgreSQL host")
    postgres_port: int = Field(5432, description="PostgreSQL port")
    postgres_db: str = Field("eu_funds", description="Database name")
    postgres_user: str = Field("postgres", description="Database user")
    postgres_password: str = Field("", description="Database password")
    
    # Connection Pool Settings (optimized for low RAM)
    max_connections: int = Field(5, description="Maximum database connections")
    min_connections: int = Field(1, description="Minimum database connections")
    connection_timeout: int = Field(30, description="Connection timeout in seconds")
    
    model_config = SettingsConfigDict(env_prefix="DB_")


class CrawlerSettings(BaseSettings):
    """Web crawler configuration settings."""
    
    # Crawl4AI Settings (memory optimized)
    max_concurrent_crawls: int = Field(2, description="Max concurrent crawl operations")
    crawl_timeout: int = Field(60, description="Crawl timeout in seconds")
    max_pages_per_site: int = Field(100, description="Maximum pages to crawl per site")
    
    # Browser Settings (lightweight)
    headless: bool = Field(True, description="Run browser in headless mode")
    browser_pool_size: int = Field(1, description="Browser pool size")
    page_load_timeout: int = Field(30, description="Page load timeout")
    
    # Content Processing
    chunk_size: int = Field(1000, description="Text chunk size for processing")
    chunk_overlap: int = Field(200, description="Overlap between chunks")
    
    # Target Sites
    target_sites: List[str] = Field(
        default=[
            "https://eufunds.bg",
            "https://opic.bg", 
            "https://esif.bg",
            "https://strategy.bg"
        ],
        description="List of target sites to crawl"
    )
    
    model_config = SettingsConfigDict(env_prefix="CRAWLER_")


class RAGSettings(BaseSettings):
    """RAG system configuration settings."""
    
    # OpenAI Configuration
    openai_api_key: str = Field(..., description="OpenAI API key")
    openai_model: str = Field("gpt-4o-mini", description="OpenAI model for processing")
    
    # Embedding Model (optimized for Bulgarian)
    embedding_model: str = Field(
        "sentence-transformers/multilingual-e5-large-instruct",
        description="Embedding model name"
    )
    embedding_dimension: int = Field(1024, description="Embedding vector dimension")
    
    # Search Settings
    max_search_results: int = Field(10, description="Maximum search results")
    similarity_threshold: float = Field(0.7, description="Similarity threshold")
    
    # Hybrid Search
    use_hybrid_search: bool = Field(True, description="Enable hybrid search")
    bm25_weight: float = Field(0.3, description="BM25 weight in hybrid search")
    semantic_weight: float = Field(0.7, description="Semantic weight in hybrid search")
    
    # Caching (Redis)
    cache_ttl: int = Field(3600, description="Cache TTL in seconds")
    
    model_config = SettingsConfigDict(env_prefix="RAG_")


class MCPSettings(BaseSettings):
    """MCP server configuration settings."""
    
    # Server Settings
    host: str = Field("0.0.0.0", description="Server host")
    port: int = Field(8051, description="Server port")
    transport: str = Field("sse", description="Transport protocol (sse/stdio)")
    
    # Security
    cors_origins: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:8080"],
        description="CORS allowed origins"
    )
    rate_limit: int = Field(100, description="Rate limit per minute")
    
    # Health Checks
    health_check_interval: int = Field(30, description="Health check interval")
    
    model_config = SettingsConfigDict(env_prefix="MCP_")


class MonitoringSettings(BaseSettings):
    """Monitoring and logging configuration."""
    
    # Logging
    log_level: str = Field("INFO", description="Logging level")
    log_format: str = Field("json", description="Log format (json/text)")
    log_file: Optional[str] = Field(None, description="Log file path")
    
    # Metrics
    enable_metrics: bool = Field(True, description="Enable Prometheus metrics")
    metrics_port: int = Field(9090, description="Metrics server port")
    
    # Health Monitoring
    enable_health_checks: bool = Field(True, description="Enable health checks")
    
    model_config = SettingsConfigDict(env_prefix="MONITORING_")


class RedisSettings(BaseSettings):
    """Redis cache configuration."""
    
    redis_url: str = Field("redis://localhost:6379", description="Redis URL")
    redis_db: int = Field(0, description="Redis database number")
    redis_password: Optional[str] = Field(None, description="Redis password")
    
    # Connection Pool (optimized for low RAM)
    max_connections: int = Field(5, description="Maximum Redis connections")
    
    model_config = SettingsConfigDict(env_prefix="REDIS_")


class Settings(BaseSettings):
    """Main application settings."""
    
    # Environment
    environment: str = Field("development", description="Environment (dev/staging/prod)")
    debug: bool = Field(False, description="Debug mode")
    
    # Application Info
    app_name: str = Field("EU Funds MCP Server", description="Application name")
    app_version: str = Field("0.1.0", description="Application version")
    
    # Sub-configurations
    database: DatabaseSettings = Field(default_factory=DatabaseSettings)
    crawler: CrawlerSettings = Field(default_factory=CrawlerSettings)
    rag: RAGSettings = Field(default_factory=RAGSettings)
    mcp: MCPSettings = Field(default_factory=MCPSettings)
    monitoring: MonitoringSettings = Field(default_factory=MonitoringSettings)
    redis: RedisSettings = Field(default_factory=RedisSettings)
    
    @validator("environment")
    def validate_environment(cls, v):
        """Validate environment setting."""
        allowed = ["development", "staging", "production"]
        if v not in allowed:
            raise ValueError(f"Environment must be one of {allowed}")
        return v
    
    @property
    def is_production(self) -> bool:
        """Check if running in production."""
        return self.environment == "production"
    
    @property
    def is_development(self) -> bool:
        """Check if running in development."""
        return self.environment == "development"
    
    model_config = SettingsConfigDict(
        env_file=[".env", "../.env", "../../.env"],
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"
    )


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings."""
    return settings


def reload_settings() -> Settings:
    """Reload settings from environment."""
    global settings
    settings = Settings()
    return settings
