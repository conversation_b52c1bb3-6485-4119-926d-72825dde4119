"""
Configuration management using Pydantic Settings.
Handles environment variables and validation.
CRITICAL: Uses Context Engineering pattern for comma-separated lists.
"""

import os
from typing import List, Optional, Annotated
from pydantic import Field, field_validator, BeforeValidator
from pydantic_settings import BaseSettings, SettingsConfigDict


def parse_comma_separated_list(v) -> List[str]:
    """
    CRITICAL FUNCTION: Parse comma-separated string into list.
    This is the EXACT solution for environment variable list parsing.
    """
    if isinstance(v, str):
        # Split by comma and strip whitespace
        items = [item.strip() for item in v.split(',') if item.strip()]
        return items
    elif isinstance(v, list):
        return v
    return []


class Settings(BaseSettings):
    """
    Application settings with environment variable support.
    CRITICAL: Uses Context Engineering pattern for robust configuration.
    """

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"  # Removed env_parse_none_str which can cause issues
    )

    # Database Configuration
    supabase_url: str = Field(..., description="Supabase project URL")
    supabase_service_key: str = Field(..., description="Supabase service role key")
    supabase_anon_key: Optional[str] = Field(None, description="Supabase anon key")

    # AI Services
    openai_api_key: str = Field(..., description="OpenAI API key")
    cohere_api_key: str = Field(..., description="Cohere API key")

    # Embedding Configuration
    embedding_model_name: str = Field(
        default="intfloat/multilingual-e5-large-instruct",
        description="Embedding model name"
    )
    embedding_dimension: int = Field(default=1024, description="Embedding vector dimension")
    embedding_batch_size: int = Field(default=32, description="Batch size for embeddings")

    # Search Configuration
    rerank_weight_ce: float = Field(default=0.3, ge=0.0, le=1.0, description="Cross-encoder weight")
    rerank_weight_hybrid: float = Field(default=0.7, ge=0.0, le=1.0, description="Hybrid search weight")
    max_search_results: int = Field(default=100, ge=1, le=1000, description="Maximum search results")
    default_search_limit: int = Field(default=10, ge=1, le=100, description="Default search limit")

    # Crawler Configuration
    crawl_domains: Annotated[List[str], BeforeValidator(parse_comma_separated_list)] = Field(
        default=["eufunds.bg", "opic.bg", "esif.bg"],
        description="Domains to crawl"
    )
    crawl_frequency_hours: int = Field(default=24, ge=1, description="Crawling frequency in hours")
    crawl_max_depth: int = Field(default=3, ge=1, le=10, description="Maximum crawl depth")
    crawl_max_pages: int = Field(default=1000, ge=1, description="Maximum pages to crawl")
    browser_pool_size: int = Field(default=3, ge=1, le=10, description="Browser pool size")
    max_concurrent_requests: int = Field(default=5, ge=1, le=20, description="Max concurrent requests")
    request_timeout_seconds: int = Field(default=30, ge=5, le=300, description="Request timeout")

    # MCP Server Configuration
    mcp_host: str = Field(default="0.0.0.0", description="MCP server host")
    mcp_port: int = Field(default=8051, ge=1024, le=65535, description="MCP server port")
    mcp_transport: str = Field(default="stdio", description="MCP transport type")
    mcp_cors_origins: Annotated[List[str], BeforeValidator(parse_comma_separated_list)] = Field(
        default=["http://localhost:3000", "http://localhost:8080"],
        description="CORS origins"
    )

    # Redis Configuration
    redis_url: str = Field(default="redis://localhost:6379/0", description="Redis connection URL")
    redis_ttl_seconds: int = Field(default=3600, ge=60, description="Redis TTL in seconds")

    # Monitoring & Logging
    log_level: str = Field(default="INFO", description="Logging level")
    log_format: str = Field(default="json", description="Log format")
    enable_metrics: bool = Field(default=True, description="Enable Prometheus metrics")
    metrics_port: int = Field(default=9090, ge=1024, le=65535, description="Metrics port")

    # Environment
    environment: str = Field(default="development", description="Environment name")
    debug: bool = Field(default=True, description="Debug mode")

    # Security
    jwt_secret_key: str = Field(default="dev-secret-key", description="JWT secret key")
    jwt_algorithm: str = Field(default="HS256", description="JWT algorithm")
    jwt_expiration_hours: int = Field(default=24, ge=1, description="JWT expiration hours")
    rate_limit_requests: int = Field(default=100, ge=1, description="Rate limit requests")
    rate_limit_window_minutes: int = Field(default=1, ge=1, description="Rate limit window")

    # Feature Flags
    enable_multimodal: bool = Field(default=True, description="Enable multimodal processing")
    enable_ocr: bool = Field(default=True, description="Enable OCR")
    enable_table_extraction: bool = Field(default=True, description="Enable table extraction")
    enable_bulgarian_fts: bool = Field(default=True, description="Enable Bulgarian FTS")
    enable_real_time_updates: bool = Field(default=False, description="Enable real-time updates")



    @field_validator('rerank_weight_ce', 'rerank_weight_hybrid')
    @classmethod
    def validate_weights(cls, v, info):
        """Validate that weights are between 0 and 1."""
        if not 0.0 <= v <= 1.0:
            raise ValueError(f"Weight must be between 0.0 and 1.0, got {v}")
        return v

    def model_post_init(self, __context) -> None:
        """Post-initialization validation."""
        # Validate that rerank weights sum to 1.0
        total_weight = self.rerank_weight_ce + self.rerank_weight_hybrid
        if abs(total_weight - 1.0) > 0.001:
            raise ValueError(
                f"Rerank weights must sum to 1.0, got {total_weight} "
                f"(CE: {self.rerank_weight_ce}, Hybrid: {self.rerank_weight_hybrid})"
            )


# Global settings instance
_settings: Optional[Settings] = None


def get_settings() -> Settings:
    """Get application settings singleton."""
    global _settings
    if _settings is None:
        _settings = Settings()
    return _settings


def reload_settings() -> Settings:
    """Reload settings from environment."""
    global _settings
    _settings = Settings()
    return _settings
