"""
Configuration management for EU Funds MCP Server.

This module provides Pydantic v2 settings management with environment-based
configuration, secrets management, and comprehensive validation.
"""

import os
from pathlib import Path
from typing import List, Optional
from functools import lru_cache

from pydantic import Field, validator
from pydantic_settings import BaseSettings, SettingsConfigDict
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class DatabaseSettings(BaseSettings):
    """Database configuration settings."""
    
    supabase_url: str = Field(..., description="Supabase project URL")
    supabase_service_key: str = Field(..., description="Supabase service role key")
    supabase_anon_key: Optional[str] = Field(None, description="Supabase anon key")
    
    # Connection pool settings
    db_pool_size: int = Field(10, description="Database connection pool size")
    db_max_overflow: int = Field(20, description="Max overflow connections")
    db_pool_timeout: int = Field(30, description="Pool timeout in seconds")
    
    model_config = SettingsConfigDict(env_prefix="")


class AISettings(BaseSettings):
    """AI and LLM configuration settings."""
    
    # OpenAI Configuration
    openai_api_key: str = Field(..., description="OpenAI API key")
    model_choice: str = Field("gpt-4o-mini", description="OpenAI model for processing")
    
    # Cohere Configuration
    cohere_api_key: str = Field(..., description="Cohere API key")
    
    # Cross-encoder Configuration
    cross_encoder_model_api: str = Field(
        "cross-encoder/ms-marco-MiniLM-L-6-v2", 
        description="Cross encoder model"
    )
    
    # Reranking weights
    rerank_weight_ce: float = Field(0.3, description="Cross encoder weight")
    rerank_weight_hybrid: float = Field(0.7, description="Hybrid score weight")
    
    # Embedding Configuration
    embedding_model_name: str = Field(
        "intfloat/multilingual-e5-large-instruct",
        description="Embedding model name"
    )
    embedding_dimension: int = Field(1024, description="Embedding vector dimension")
    
    model_config = SettingsConfigDict(env_prefix="")


class MCPSettings(BaseSettings):
    """MCP server configuration settings."""
    
    # Server Settings
    host: str = Field("0.0.0.0", description="Server host")
    port: int = Field(8051, description="Server port")
    transport: str = Field("sse", description="Transport protocol (sse/stdio)")
    
    # Security
    cors_origins: str = Field(
        default="http://localhost:3000,http://localhost:8080",
        description="CORS allowed origins (comma-separated)"
    )
    rate_limit_per_minute: int = Field(100, description="Rate limit per minute")
    
    # Authentication
    secret_key: str = Field("eu-funds-mcp-secret-key-2025-production", description="JWT secret key")
    algorithm: str = Field("HS256", description="JWT algorithm")
    access_token_expire_minutes: int = Field(30, description="Token expiration time")
    
    model_config = SettingsConfigDict(env_prefix="")


class CrawlerSettings(BaseSettings):
    """Web crawler configuration settings."""
    
    # Crawling behavior
    crawl_frequency_hours: int = Field(24, description="Crawl frequency in hours")
    max_concurrent_requests: int = Field(5, description="Max concurrent requests")
    crawl_delay_seconds: int = Field(1, description="Delay between requests")
    
    # Browser settings
    browser_pool_size: int = Field(3, description="Browser pool size")
    browser_timeout: int = Field(30, description="Browser timeout in seconds")
    headless_browser: bool = Field(True, description="Run browser in headless mode")
    
    # User agent and domains
    user_agent: str = Field(
        "EU-Funds-MCP-Bot/1.0 (+https://eufunds-mcp.com/bot)",
        description="User agent string"
    )
    crawl_domains: str = Field(
        default="eufunds.bg,opic.bg,esif.bg",
        description="Domains to crawl (comma-separated)"
    )
    
    model_config = SettingsConfigDict(env_prefix="")


class MonitoringSettings(BaseSettings):
    """Monitoring and logging configuration settings."""
    
    # Logging
    log_level: str = Field("INFO", description="Log level")
    log_format: str = Field("json", description="Log format")
    enable_metrics: bool = Field(True, description="Enable Prometheus metrics")
    
    # Health checks
    health_check_interval: int = Field(30, description="Health check interval")
    health_check_timeout: int = Field(10, description="Health check timeout")
    
    model_config = SettingsConfigDict(env_prefix="")


class FeatureSettings(BaseSettings):
    """Feature flags configuration."""
    
    enable_multimodal: bool = Field(True, description="Enable multimodal processing")
    enable_ocr: bool = Field(True, description="Enable OCR processing")
    enable_table_extraction: bool = Field(True, description="Enable table extraction")
    enable_change_detection: bool = Field(True, description="Enable change detection")
    enable_bulgarian_fts: bool = Field(True, description="Enable Bulgarian full-text search")
    bulgarian_stemmer: bool = Field(True, description="Enable Bulgarian stemmer")
    
    model_config = SettingsConfigDict(env_prefix="")


class LocalizationSettings(BaseSettings):
    """Localization and regional settings."""
    
    language: str = Field("bg", description="Primary language")
    timezone: str = Field("Europe/Sofia", description="Timezone")
    currency: str = Field("BGN", description="Currency")
    
    model_config = SettingsConfigDict(env_prefix="")


class Settings(BaseSettings):
    """Main application settings."""
    
    # Environment
    debug: bool = Field(False, description="Debug mode")
    environment: str = Field("production", description="Environment name")
    
    # Sub-settings
    database: DatabaseSettings = DatabaseSettings()
    ai: AISettings = AISettings()
    mcp: MCPSettings = MCPSettings()
    crawler: CrawlerSettings = CrawlerSettings()
    monitoring: MonitoringSettings = MonitoringSettings()
    features: FeatureSettings = FeatureSettings()
    localization: LocalizationSettings = LocalizationSettings()
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"
    )


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance."""
    return Settings()


def reload_settings() -> Settings:
    """Reload settings (clear cache and create new instance)."""
    get_settings.cache_clear()
    return get_settings()


# Global settings instance
settings = Settings()
