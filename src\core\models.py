"""
Pydantic models for EU Funds MCP Server

This module defines all data models used throughout the application
with proper type hints and validation for Bulgarian language support.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field

class SearchRequest(BaseModel):
    """Search request for EU funding information."""
    query: str = Field(..., description="Search query in Bulgarian or English")
    source_filter: Optional[str] = Field(None, description="Filter by specific source (e.g., 'ec.europa.eu')")
    limit: int = Field(10, description="Maximum number of results to return")

class SearchResult(BaseModel):
    """Search result with relevance scoring."""
    title: str = Field(..., description="Document title")
    content: str = Field(..., description="Relevant content excerpt")
    source: str = Field(..., description="Source URL or document identifier")
    relevance_score: float = Field(..., description="Relevance score (0.0 to 1.0)")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")

class FundingProgram(BaseModel):
    """EU funding program information."""
    name: str = Field(..., description="Program name in Bulgarian and English")
    description: str = Field(..., description="Program description")
    eligibility: List[str] = Field(..., description="Eligibility criteria")
    deadline: Optional[str] = Field(None, description="Application deadline")
    budget: Optional[str] = Field(None, description="Available budget")
    contact: Optional[str] = Field(None, description="Contact information")

class EligibilityAnalysis(BaseModel):
    """Eligibility analysis result."""
    program_name: str = Field(..., description="Name of the funding program")
    organization_type: str = Field(..., description="Type of organization")
    location: str = Field(..., description="Location/country")
    eligible: bool = Field(..., description="Whether the organization is eligible")
    confidence: float = Field(..., description="Confidence score (0.0 to 1.0)")
    requirements: List[str] = Field(..., description="List of requirements")
    recommendations: List[str] = Field(..., description="List of recommendations")
    supporting_documents: List[str] = Field(..., description="Supporting document sources")

class DeadlineInfo(BaseModel):
    """Application deadline information."""
    program_name: str = Field(..., description="Name of the funding program")
    deadline: str = Field(..., description="Application deadline")
    days_remaining: int = Field(..., description="Days remaining until deadline")
    status: str = Field(..., description="Status (open, closing_soon, closed)")
    application_url: Optional[str] = Field(None, description="Application URL")

class DataSource(BaseModel):
    """Data source information."""
    name: str = Field(..., description="Source name")
    url: str = Field(..., description="Source URL")
    reliability_score: float = Field(..., description="Reliability score (0.0 to 1.0)")
    last_updated: str = Field(..., description="Last update timestamp")
    content_type: str = Field(..., description="Type of content")
    language: str = Field(..., description="Primary language")

class HealthStatus(BaseModel):
    """Health check status."""
    status: str = Field(..., description="Overall status")
    timestamp: float = Field(..., description="Timestamp of health check")
    components: Dict[str, str] = Field(..., description="Component status")
    version: str = Field(..., description="Application version")

class ErrorResponse(BaseModel):
    """Error response model."""
    error: str = Field(..., description="Error message")

# Crawling Models
class CrawlResult(BaseModel):
    """Result from web crawling operation."""
    url: str = Field(..., description="Crawled URL")
    title: str = Field(..., description="Page title")
    content: str = Field(..., description="Extracted content")
    extracted_data: Dict[str, Any] = Field(default_factory=dict, description="Structured extracted data")
    metadata: 'ContentMetadata' = Field(..., description="Content metadata")
    links: List[str] = Field(default_factory=list, description="Internal links found")
    images: List[str] = Field(default_factory=list, description="Images found")
    success: bool = Field(..., description="Whether crawl was successful")
    error_message: Optional[str] = Field(None, description="Error message if failed")

class ContentMetadata(BaseModel):
    """Metadata for crawled content."""
    source: str = Field(..., description="Source identifier")
    url: str = Field(..., description="Source URL")
    title: str = Field(..., description="Content title")
    language: str = Field(..., description="Detected language")
    crawl_timestamp: str = Field(..., description="When content was crawled")
    content_type: str = Field(..., description="Type of content")
    word_count: int = Field(..., description="Number of words")
    has_deadlines: bool = Field(default=False, description="Contains deadline information")
    has_eligibility: bool = Field(default=False, description="Contains eligibility criteria")
    quality_score: float = Field(..., description="Content quality score (0.0 to 1.0)")

# Text Processing Models
class ProcessedText(BaseModel):
    """Result from text processing."""
    original_text: str = Field(..., description="Original text")
    cleaned_text: str = Field(..., description="Cleaned and normalized text")
    language_metrics: 'LanguageMetrics' = Field(..., description="Language analysis")
    extracted_info: Dict[str, Any] = Field(default_factory=dict, description="Extracted structured info")
    chunks: List['TextChunk'] = Field(default_factory=list, description="Text chunks")
    quality_score: float = Field(..., description="Text quality score")
    processing_timestamp: str = Field(..., description="Processing timestamp")
    source_url: str = Field(default="", description="Source URL")

class LanguageMetrics(BaseModel):
    """Language analysis metrics."""
    primary_language: str = Field(..., description="Detected primary language")
    confidence: float = Field(..., description="Detection confidence")
    cyrillic_ratio: float = Field(..., description="Ratio of Cyrillic characters")
    bulgarian_ratio: float = Field(..., description="Ratio of Bulgarian words")
    total_words: int = Field(..., description="Total word count")
    bulgarian_words: int = Field(..., description="Bulgarian word count")
    avg_word_length: float = Field(..., description="Average word length")
    funding_terms: List[str] = Field(default_factory=list, description="Found funding terms")

class TextChunk(BaseModel):
    """Individual text chunk."""
    chunk_id: int = Field(..., description="Chunk ID")
    text: str = Field(..., description="Chunk text")
    word_count: int = Field(..., description="Word count")
    keywords: List[str] = Field(default_factory=list, description="Extracted keywords")
    key_phrases: List[str] = Field(default_factory=list, description="Key phrases")
    has_funding_info: bool = Field(default=False, description="Contains funding information")
    language: str = Field(..., description="Chunk language")
    chunk_type: str = Field(default="content", description="Type of chunk (content, title, etc.)")

class CrawledContent(BaseModel):
    """Crawled content with processing results."""
    url: str = Field(..., description="Source URL")
    title: str = Field(..., description="Page title")
    content: str = Field(..., description="Raw content")
    processed_content: Optional[ProcessedText] = Field(None, description="Processed text result")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Content metadata")
    crawl_timestamp: datetime = Field(default_factory=datetime.utcnow, description="When content was crawled")
    success: bool = Field(default=True, description="Whether crawl was successful")
    error_message: Optional[str] = Field(None, description="Error message if failed")

# Embedding Models
class EmbeddingResult(BaseModel):
    """Result from embedding generation."""
    text: str = Field(..., description="Original text")
    vector: List[float] = Field(..., description="Embedding vector")
    dimensions: int = Field(..., description="Vector dimensions")
    model: str = Field(..., description="Model used")
    processing_time: float = Field(..., description="Processing time in seconds")
    is_query: bool = Field(default=False, description="Whether this is a query embedding")
    language: str = Field(..., description="Text language")
    timestamp: str = Field(..., description="Generation timestamp")

class BatchEmbeddingResult(BaseModel):
    """Result from batch embedding generation."""
    embeddings: List[EmbeddingResult] = Field(..., description="Generated embeddings")
    total_texts: int = Field(..., description="Total texts processed")
    successful_embeddings: int = Field(..., description="Successful embeddings")
    failed_indices: List[int] = Field(default_factory=list, description="Failed text indices")
    total_processing_time: float = Field(..., description="Total processing time")
    avg_time_per_embedding: float = Field(..., description="Average time per embedding")
    success_rate: float = Field(..., description="Success rate (0.0 to 1.0)")
    batch_size: int = Field(..., description="Batch size used")
    model: str = Field(..., description="Model used")
    timestamp: str = Field(..., description="Processing timestamp")

class EmbeddingMetrics(BaseModel):
    """Embedding processing metrics."""
    total_embeddings: int = Field(..., description="Total embeddings generated")
    cache_hits: int = Field(..., description="Cache hits")
    cache_hit_rate: float = Field(..., description="Cache hit rate")
    batch_operations: int = Field(..., description="Batch operations performed")
    avg_processing_time: float = Field(..., description="Average processing time")
    model_name: str = Field(..., description="Model name")
    dimensions: int = Field(..., description="Vector dimensions")
    cache_size: int = Field(..., description="Current cache size")

# Vector Search Models
class VectorSearchResult(BaseModel):
    """Result from vector similarity search."""
    results: List[Dict[str, Any]] = Field(..., description="Search results")
    total_results: int = Field(..., description="Total number of results")
    processing_time: float = Field(..., description="Search processing time")
    similarity_threshold: float = Field(..., description="Similarity threshold used")
    search_type: str = Field(..., description="Type of search performed")
    query_embedding_dim: int = Field(..., description="Query embedding dimensions")

class HybridSearchResult(BaseModel):
    """Result from hybrid search (vector + text)."""
    results: List[Dict[str, Any]] = Field(..., description="Ranked search results")
    total_results: int = Field(..., description="Total number of results")
    vector_results: int = Field(..., description="Results from vector search")
    text_results: int = Field(..., description="Results from text search")
    processing_time: float = Field(..., description="Total processing time")
    fusion_method: str = Field(..., description="Fusion method used")
    weights: Dict[str, float] = Field(..., description="Search weights used")

class SearchMetrics(BaseModel):
    """Search performance metrics."""
    total_searches: int = Field(..., description="Total searches performed")
    avg_response_time: float = Field(..., description="Average response time")
    cache_hit_rate: float = Field(..., description="Cache hit rate")
    popular_queries: List[str] = Field(default_factory=list, description="Most popular queries")
    timestamp: str = Field(..., description="Metrics timestamp")

# Database Models
class Document(BaseModel):
    """Document model for database storage."""
    id: Optional[str] = Field(None, description="Document ID")
    title: str = Field(..., description="Document title")
    content: str = Field(..., description="Document content")
    source: str = Field(..., description="Source URL or identifier")
    language: str = Field(..., description="Document language")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")

class EmbeddingVector(BaseModel):
    """Embedding vector model."""
    document_id: str = Field(..., description="Associated document ID")
    vector: List[float] = Field(..., description="Embedding vector")
    model: str = Field(..., description="Embedding model used")
    dimensions: int = Field(..., description="Vector dimensions")
    error_code: str = Field(..., description="Error code")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    timestamp: float = Field(..., description="Error timestamp")

class BulgarianTextMetadata(BaseModel):
    """Metadata for Bulgarian text processing."""
    original_text: str = Field(..., description="Original Bulgarian text")
    normalized_text: str = Field(..., description="Normalized text")
    language_detected: str = Field(..., description="Detected language")
    confidence: float = Field(..., description="Language detection confidence")
    stopwords_removed: int = Field(..., description="Number of stopwords removed")
    tokens_count: int = Field(..., description="Number of tokens")

class EmbeddingMetadata(BaseModel):
    """Metadata for embedding processing."""
    model_name: str = Field(..., description="Embedding model name")
    dimensions: int = Field(..., description="Embedding dimensions")
    processing_time: float = Field(..., description="Processing time in seconds")
    chunk_size: int = Field(..., description="Text chunk size")
    quality_score: float = Field(..., description="Embedding quality score")

class SearchMetrics(BaseModel):
    """Search performance metrics."""
    query: str = Field(..., description="Search query")
    total_results: int = Field(..., description="Total number of results")
    search_time: float = Field(..., description="Search time in seconds")
    reranking_time: float = Field(..., description="Reranking time in seconds")
    cache_hit: bool = Field(..., description="Whether result was cached")
    relevance_scores: List[float] = Field(..., description="Relevance scores of results")

class CrawlMetadata(BaseModel):
    """Metadata for web crawling operations."""
    url: str = Field(..., description="Crawled URL")
    status_code: int = Field(..., description="HTTP status code")
    content_length: int = Field(..., description="Content length in bytes")
    crawl_time: float = Field(..., description="Crawl time in seconds")
    content_type: str = Field(..., description="Content type")
    language_detected: str = Field(..., description="Detected language")
    last_modified: Optional[str] = Field(None, description="Last modified timestamp")

class DatabaseMetrics(BaseModel):
    """Database performance metrics."""
    operation: str = Field(..., description="Database operation")
    execution_time: float = Field(..., description="Execution time in seconds")
    rows_affected: int = Field(..., description="Number of rows affected")
    connection_pool_size: int = Field(..., description="Current connection pool size")
    active_connections: int = Field(..., description="Number of active connections")
