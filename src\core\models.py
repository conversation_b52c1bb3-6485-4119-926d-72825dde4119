"""
Pydantic models for EU Funds MCP Server

This module defines all data models used throughout the application
with proper type hints and validation for Bulgarian language support.
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field

class SearchRequest(BaseModel):
    """Search request for EU funding information."""
    query: str = Field(..., description="Search query in Bulgarian or English")
    source_filter: Optional[str] = Field(None, description="Filter by specific source (e.g., 'ec.europa.eu')")
    limit: int = Field(10, description="Maximum number of results to return")

class SearchResult(BaseModel):
    """Search result with relevance scoring."""
    title: str = Field(..., description="Document title")
    content: str = Field(..., description="Relevant content excerpt")
    source: str = Field(..., description="Source URL or document identifier")
    relevance_score: float = Field(..., description="Relevance score (0.0 to 1.0)")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")

class FundingProgram(BaseModel):
    """EU funding program information."""
    name: str = Field(..., description="Program name in Bulgarian and English")
    description: str = Field(..., description="Program description")
    eligibility: List[str] = Field(..., description="Eligibility criteria")
    deadline: Optional[str] = Field(None, description="Application deadline")
    budget: Optional[str] = Field(None, description="Available budget")
    contact: Optional[str] = Field(None, description="Contact information")

class EligibilityAnalysis(BaseModel):
    """Eligibility analysis result."""
    program_name: str = Field(..., description="Name of the funding program")
    organization_type: str = Field(..., description="Type of organization")
    location: str = Field(..., description="Location/country")
    eligible: bool = Field(..., description="Whether the organization is eligible")
    confidence: float = Field(..., description="Confidence score (0.0 to 1.0)")
    requirements: List[str] = Field(..., description="List of requirements")
    recommendations: List[str] = Field(..., description="List of recommendations")
    supporting_documents: List[str] = Field(..., description="Supporting document sources")

class DeadlineInfo(BaseModel):
    """Application deadline information."""
    program_name: str = Field(..., description="Name of the funding program")
    deadline: str = Field(..., description="Application deadline")
    days_remaining: int = Field(..., description="Days remaining until deadline")
    status: str = Field(..., description="Status (open, closing_soon, closed)")
    application_url: Optional[str] = Field(None, description="Application URL")

class DataSource(BaseModel):
    """Data source information."""
    name: str = Field(..., description="Source name")
    url: str = Field(..., description="Source URL")
    reliability_score: float = Field(..., description="Reliability score (0.0 to 1.0)")
    last_updated: str = Field(..., description="Last update timestamp")
    content_type: str = Field(..., description="Type of content")
    language: str = Field(..., description="Primary language")

class HealthStatus(BaseModel):
    """Health check status."""
    status: str = Field(..., description="Overall status")
    timestamp: float = Field(..., description="Timestamp of health check")
    components: Dict[str, str] = Field(..., description="Component status")
    version: str = Field(..., description="Application version")

class ErrorResponse(BaseModel):
    """Error response model."""
    error: str = Field(..., description="Error message")
    error_code: str = Field(..., description="Error code")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    timestamp: float = Field(..., description="Error timestamp")

class BulgarianTextMetadata(BaseModel):
    """Metadata for Bulgarian text processing."""
    original_text: str = Field(..., description="Original Bulgarian text")
    normalized_text: str = Field(..., description="Normalized text")
    language_detected: str = Field(..., description="Detected language")
    confidence: float = Field(..., description="Language detection confidence")
    stopwords_removed: int = Field(..., description="Number of stopwords removed")
    tokens_count: int = Field(..., description="Number of tokens")

class EmbeddingMetadata(BaseModel):
    """Metadata for embedding processing."""
    model_name: str = Field(..., description="Embedding model name")
    dimensions: int = Field(..., description="Embedding dimensions")
    processing_time: float = Field(..., description="Processing time in seconds")
    chunk_size: int = Field(..., description="Text chunk size")
    quality_score: float = Field(..., description="Embedding quality score")

class SearchMetrics(BaseModel):
    """Search performance metrics."""
    query: str = Field(..., description="Search query")
    total_results: int = Field(..., description="Total number of results")
    search_time: float = Field(..., description="Search time in seconds")
    reranking_time: float = Field(..., description="Reranking time in seconds")
    cache_hit: bool = Field(..., description="Whether result was cached")
    relevance_scores: List[float] = Field(..., description="Relevance scores of results")

class CrawlMetadata(BaseModel):
    """Metadata for web crawling operations."""
    url: str = Field(..., description="Crawled URL")
    status_code: int = Field(..., description="HTTP status code")
    content_length: int = Field(..., description="Content length in bytes")
    crawl_time: float = Field(..., description="Crawl time in seconds")
    content_type: str = Field(..., description="Content type")
    language_detected: str = Field(..., description="Detected language")
    last_modified: Optional[str] = Field(None, description="Last modified timestamp")

class DatabaseMetrics(BaseModel):
    """Database performance metrics."""
    operation: str = Field(..., description="Database operation")
    execution_time: float = Field(..., description="Execution time in seconds")
    rows_affected: int = Field(..., description="Number of rows affected")
    connection_pool_size: int = Field(..., description="Current connection pool size")
    active_connections: int = Field(..., description="Number of active connections")
