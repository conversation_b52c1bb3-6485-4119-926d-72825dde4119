"""
Cross-encoder Reranking for EU Funds MCP Server
Implements cross-encoder/ms-marco-MiniLM-L-6-v2 for result reranking
"""

import logging
import asyncio
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import numpy as np
import torch
from sentence_transformers import CrossEncoder

from src.core.config import settings
from src.core.models import HybridSearchResult, SearchMetrics

logger = logging.getLogger(__name__)

class CrossEncoderReranker:
    """
    Advanced cross-encoder reranking for search results.
    Uses cross-encoder/ms-marco-MiniLM-L-6-v2 for relevance scoring.
    Optimized for Bulgarian EU funding content.
    """
    
    def __init__(self):
        """Initialize cross-encoder reranker."""
        self.model_name = "cross-encoder/ms-marco-MiniLM-L-6-v2"
        self.model: Optional[CrossEncoder] = None
        self.initialized = False
        
        # Reranking configuration
        self.max_rerank_candidates = 100  # Maximum candidates to rerank
        self.rerank_threshold = 0.1       # Minimum score threshold
        self.bulgarian_boost = 1.15       # Additional boost for Bulgarian content
        
        # Performance tracking
        self.rerank_stats = {
            "total_reranks": 0,
            "avg_processing_time": 0.0,
            "score_improvements": 0,
            "bulgarian_content_boosted": 0
        }
        
    async def initialize(self) -> None:
        """Initialize cross-encoder model."""
        try:
            logger.info("🔄 Initializing cross-encoder reranker...")
            
            # Check device availability
            device = "cuda" if torch.cuda.is_available() else "cpu"
            logger.info(f"🔧 Using device: {device}")
            
            # Load cross-encoder model
            logger.info(f"📥 Loading {self.model_name}...")
            self.model = CrossEncoder(
                self.model_name,
                device=device,
                trust_remote_code=True
            )
            
            # Test model with sample query-document pair
            test_query = "Как да кандидатствам за европейско финансиране?"
            test_doc = "Програма за развитие на селските райони предоставя финансиране"
            
            test_score = self.model.predict([(test_query, test_doc)])[0]
            
            if test_score is not None:
                logger.info(f"✅ Cross-encoder initialized: test score {test_score:.3f}")
                self.initialized = True
            else:
                raise ValueError("Cross-encoder test failed")
                
        except Exception as e:
            logger.error(f"❌ Failed to initialize cross-encoder: {e}")
            raise
    
    async def rerank_results(
        self,
        query: str,
        search_results: List[Dict[str, Any]],
        top_k: int = 10,
        use_bulgarian_boost: bool = True
    ) -> List[Dict[str, Any]]:
        """
        Rerank search results using cross-encoder.
        
        Args:
            query: Original search query
            search_results: List of search results to rerank
            top_k: Number of top results to return
            use_bulgarian_boost: Apply additional boost for Bulgarian content
        """
        try:
            if not self.initialized:
                await self.initialize()
            
            start_time = datetime.utcnow()
            
            if not search_results:
                return []
            
            # Limit candidates for performance
            candidates = search_results[:self.max_rerank_candidates]
            
            logger.info(f"🔄 Reranking {len(candidates)} results for query: '{query[:50]}...'")
            
            # Prepare query-document pairs
            query_doc_pairs = []
            for result in candidates:
                content = result.get('content', '')
                # Truncate content for cross-encoder (max 512 tokens)
                truncated_content = content[:2000]  # Approximate token limit
                query_doc_pairs.append((query, truncated_content))
            
            # Get cross-encoder scores
            ce_scores = self.model.predict(query_doc_pairs)
            
            # Apply scores and additional boosts
            reranked_results = []
            for i, (result, ce_score) in enumerate(zip(candidates, ce_scores)):
                enhanced_result = result.copy()
                
                # Apply cross-encoder score
                enhanced_result['ce_score'] = float(ce_score)
                
                # Calculate final score with weights
                original_score = result.get('similarity_score', result.get('rrf_score', 0.5))
                
                # Apply reranking weights from user specification: CE: 0.3, Hybrid: 0.7
                final_score = (0.7 * original_score) + (0.3 * ce_score)
                
                # Apply Bulgarian content boost
                if use_bulgarian_boost and result.get('metadata', {}).get('language') == 'bg':
                    final_score *= self.bulgarian_boost
                    enhanced_result['bulgarian_boosted'] = True
                    self.rerank_stats["bulgarian_content_boosted"] += 1
                
                enhanced_result['final_score'] = final_score
                enhanced_result['score_improvement'] = final_score - original_score
                
                # Track improvements
                if final_score > original_score:
                    self.rerank_stats["score_improvements"] += 1
                
                reranked_results.append(enhanced_result)
            
            # Sort by final score
            reranked_results.sort(key=lambda x: x['final_score'], reverse=True)
            
            # Filter by threshold and return top_k
            filtered_results = [
                r for r in reranked_results 
                if r['final_score'] >= self.rerank_threshold
            ]
            
            final_results = filtered_results[:top_k]
            
            # Update statistics
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            self.rerank_stats["total_reranks"] += 1
            self.rerank_stats["avg_processing_time"] = (
                (self.rerank_stats["avg_processing_time"] * (self.rerank_stats["total_reranks"] - 1) + processing_time) 
                / self.rerank_stats["total_reranks"]
            )
            
            logger.info(f"✅ Reranking complete: {len(final_results)} results in {processing_time:.3f}s")
            logger.info(f"   Score improvements: {sum(1 for r in final_results if r.get('score_improvement', 0) > 0)}")
            
            return final_results
            
        except Exception as e:
            logger.error(f"❌ Reranking failed: {e}")
            # Return original results if reranking fails
            return search_results[:top_k]
    
    async def rerank_hybrid_result(
        self,
        query: str,
        hybrid_result: HybridSearchResult,
        top_k: int = 10
    ) -> HybridSearchResult:
        """
        Rerank a HybridSearchResult using cross-encoder.
        
        Args:
            query: Original search query
            hybrid_result: HybridSearchResult to rerank
            top_k: Number of top results to return
        """
        try:
            start_time = datetime.utcnow()
            
            # Rerank the results
            reranked_results = await self.rerank_results(
                query=query,
                search_results=hybrid_result.results,
                top_k=top_k,
                use_bulgarian_boost=True
            )
            
            # Calculate reranking metrics
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            
            # Create enhanced hybrid result
            enhanced_result = HybridSearchResult(
                results=reranked_results,
                total_results=len(reranked_results),
                vector_results=hybrid_result.vector_results,
                text_results=hybrid_result.text_results,
                processing_time=hybrid_result.processing_time + processing_time,
                fusion_method=f"{hybrid_result.fusion_method}_+_cross_encoder",
                weights={
                    **hybrid_result.weights,
                    "cross_encoder": 0.3,
                    "rerank_threshold": self.rerank_threshold
                }
            )
            
            return enhanced_result
            
        except Exception as e:
            logger.error(f"❌ Hybrid result reranking failed: {e}")
            return hybrid_result
    
    def get_rerank_stats(self) -> Dict[str, Any]:
        """Get reranking performance statistics."""
        return {
            **self.rerank_stats,
            "model_name": self.model_name,
            "initialized": self.initialized,
            "max_candidates": self.max_rerank_candidates,
            "threshold": self.rerank_threshold,
            "bulgarian_boost": self.bulgarian_boost
        }
    
    async def validate_relevance(
        self,
        query: str,
        content: str,
        min_relevance: float = 0.3
    ) -> Tuple[bool, float]:
        """
        Validate content relevance for a query.
        
        Args:
            query: Search query
            content: Content to validate
            min_relevance: Minimum relevance threshold
            
        Returns:
            Tuple of (is_relevant, relevance_score)
        """
        try:
            if not self.initialized:
                await self.initialize()
            
            # Truncate content for cross-encoder
            truncated_content = content[:2000]
            
            # Get relevance score
            relevance_score = self.model.predict([(query, truncated_content)])[0]
            is_relevant = relevance_score >= min_relevance
            
            return is_relevant, float(relevance_score)
            
        except Exception as e:
            logger.error(f"❌ Relevance validation failed: {e}")
            return True, 0.5  # Default to relevant with neutral score
    
    async def batch_validate_relevance(
        self,
        query: str,
        contents: List[str],
        min_relevance: float = 0.3
    ) -> List[Tuple[bool, float]]:
        """
        Batch validate content relevance for a query.
        
        Args:
            query: Search query
            contents: List of contents to validate
            min_relevance: Minimum relevance threshold
            
        Returns:
            List of (is_relevant, relevance_score) tuples
        """
        try:
            if not self.initialized:
                await self.initialize()
            
            # Prepare query-document pairs
            query_doc_pairs = []
            for content in contents:
                truncated_content = content[:2000]
                query_doc_pairs.append((query, truncated_content))
            
            # Get relevance scores
            relevance_scores = self.model.predict(query_doc_pairs)
            
            # Validate relevance
            results = []
            for score in relevance_scores:
                is_relevant = score >= min_relevance
                results.append((is_relevant, float(score)))
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Batch relevance validation failed: {e}")
            # Return default values
            return [(True, 0.5) for _ in contents]
    
    async def cleanup(self):
        """Clean up reranker resources."""
        try:
            if self.model:
                # Clear model from memory
                del self.model
                self.model = None
                
                # Clear CUDA cache if available
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
            
            logger.info("✅ Cross-encoder reranker cleanup complete")
            
        except Exception as e:
            logger.error(f"❌ Reranker cleanup failed: {e}")
