#!/usr/bin/env python3
"""
Security check script to identify sensitive information before GitHub upload.
"""

import os
import re
import glob
from typing import List, Dict, Tuple

class SecurityChecker:
    """Check for sensitive information in project files."""
    
    def __init__(self):
        self.sensitive_patterns = {
            'api_keys': [
                r'sk-[a-zA-Z0-9]{48}',  # OpenAI API keys
                r'OPENAI_API_KEY\s*=\s*["\']?sk-[a-zA-Z0-9]{48}',
                r'supabase\.co/[a-zA-Z0-9]+',  # Supabase URLs
                r'SUPABASE_URL\s*=\s*["\']?https://[a-zA-Z0-9]+\.supabase\.co',
                r'eyJ[a-zA-Z0-9_-]+\.[a-zA-Z0-9_-]+\.[a-zA-Z0-9_-]+',  # JWT tokens
                r'SUPABASE_KEY\s*=\s*["\']?eyJ[a-zA-Z0-9_-]+',
                r'COHERE_API_KEY\s*=\s*["\']?[a-zA-Z0-9-]{40,}',
            ],
            'passwords': [
                r'password\s*=\s*["\'][^"\']+["\']',
                r'PASSWORD\s*=\s*["\'][^"\']+["\']',
                r'pass\s*=\s*["\'][^"\']+["\']',
            ],
            'secrets': [
                r'secret\s*=\s*["\'][^"\']+["\']',
                r'SECRET\s*=\s*["\'][^"\']+["\']',
                r'token\s*=\s*["\'][^"\']+["\']',
                r'TOKEN\s*=\s*["\'][^"\']+["\']',
            ],
            'personal_info': [
                r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',  # Email addresses (but not example ones)
            ]
        }
        
        self.exclude_patterns = [
            r'your_.*_key_here',  # Template placeholders
            r'example@example\.com',  # Example emails
            r'user@domain\.com',  # Example emails
            r'test@test\.com',  # Test emails
            r'horizon-europe@ec\.europa\.eu',  # Official EU emails
            r'digital-europe@ec\.europa\.eu',  # Official EU emails
        ]
        
        self.safe_files = {
            '.gitignore', 'LICENSE', 'README.md', 'DEPLOYMENT_GUIDE.md',
            'EU_FUNDS_MCP_PRP.md', 'ACCURACY_IMPROVEMENT_PLAN.md',
            'PLANNING.md', 'CLAUDE.md', 'INITIAL.md'
        }
    
    def check_file(self, filepath: str) -> List[Tuple[str, str, int]]:
        """Check a single file for sensitive information."""
        issues = []
        
        try:
            with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                
            for category, patterns in self.sensitive_patterns.items():
                for pattern in patterns:
                    matches = re.finditer(pattern, content, re.IGNORECASE)
                    for match in matches:
                        # Check if it's an excluded pattern
                        is_excluded = any(
                            re.search(exclude_pattern, match.group(), re.IGNORECASE)
                            for exclude_pattern in self.exclude_patterns
                        )
                        
                        if not is_excluded:
                            line_num = content[:match.start()].count('\n') + 1
                            issues.append((category, match.group()[:50] + '...', line_num))
                            
        except Exception as e:
            print(f"Error reading {filepath}: {e}")
            
        return issues
    
    def check_project(self) -> Dict[str, List[Tuple[str, str, int]]]:
        """Check entire project for sensitive information."""
        results = {}
        
        # Get all Python files
        python_files = glob.glob('**/*.py', recursive=True)
        
        # Get all config files
        config_files = glob.glob('**/*.env*', recursive=True)
        config_files.extend(glob.glob('**/*.json', recursive=True))
        config_files.extend(glob.glob('**/*.yaml', recursive=True))
        config_files.extend(glob.glob('**/*.yml', recursive=True))
        config_files.extend(glob.glob('**/*.toml', recursive=True))
        
        # Get all text files
        text_files = glob.glob('**/*.txt', recursive=True)
        text_files.extend(glob.glob('**/*.md', recursive=True))
        
        all_files = set(python_files + config_files + text_files)
        
        # Filter out safe files and directories
        filtered_files = []
        for filepath in all_files:
            filename = os.path.basename(filepath)
            if filename not in self.safe_files and not any(
                exclude in filepath for exclude in [
                    '__pycache__', '.git', 'node_modules', '.env.example'
                ]
            ):
                filtered_files.append(filepath)
        
        for filepath in filtered_files:
            issues = self.check_file(filepath)
            if issues:
                results[filepath] = issues
                
        return results
    
    def create_gitignore(self):
        """Create or update .gitignore file."""
        gitignore_content = """# Environment variables
.env
.env.local
.env.production
.env.staging

# API Keys and secrets
*.key
*.secret
config/secrets.json

# Database
*.db
*.sqlite
*.sqlite3

# Logs
logs/
*.log

# Cache
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
*.so

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Node modules
node_modules/

# Build artifacts
build/
dist/
*.egg-info/

# Test coverage
.coverage
htmlcov/

# Temporary files
*.tmp
*.temp
temp/
"""
        
        with open('.gitignore', 'w') as f:
            f.write(gitignore_content)
        
        print("✅ Created/updated .gitignore file")
    
    def create_env_example(self):
        """Create .env.example template."""
        env_example = """# EU Funds MCP Server Configuration
# Copy this file to .env and fill in your actual values

# OpenAI API Key for embeddings
OPENAI_API_KEY=your_openai_key_here

# Supabase Configuration
SUPABASE_URL=your_supabase_url_here
SUPABASE_KEY=your_supabase_key_here

# Cohere API Key for reranking
COHERE_API_KEY=your_cohere_key_here

# Optional: Logging level
LOG_LEVEL=INFO

# Optional: Custom settings
CRAWL_DEPTH=2
SIMILARITY_THRESHOLD=0.3
"""
        
        with open('.env.example', 'w') as f:
            f.write(env_example)
        
        print("✅ Created .env.example template")
    
    def run_security_check(self):
        """Run complete security check."""
        print("🔒 SECURITY CHECK FOR GITHUB UPLOAD")
        print("=" * 50)
        
        # Check for sensitive information
        results = self.check_project()
        
        if results:
            print("❌ SENSITIVE INFORMATION FOUND:")
            for filepath, issues in results.items():
                print(f"\n📄 {filepath}:")
                for category, content, line_num in issues:
                    print(f"   Line {line_num}: {category} - {content}")
            
            print("\n🚨 DO NOT UPLOAD TO GITHUB UNTIL ISSUES ARE RESOLVED!")
            return False
        else:
            print("✅ NO SENSITIVE INFORMATION DETECTED")
            
        # Create security files
        self.create_gitignore()
        self.create_env_example()
        
        print("\n🔒 SECURITY RECOMMENDATIONS:")
        print("✅ All API keys should be in .env (not tracked by git)")
        print("✅ Use .env.example for configuration templates")
        print("✅ Review all files before committing")
        print("✅ Never commit actual API keys or secrets")
        
        print("\n🚀 READY FOR GITHUB UPLOAD!")
        return True

def main():
    """Main function."""
    checker = SecurityChecker()
    is_safe = checker.run_security_check()
    
    if is_safe:
        print("\n✅ Project is safe for GitHub upload!")
    else:
        print("\n❌ Please fix security issues before uploading!")
        exit(1)

if __name__ == "__main__":
    main()
