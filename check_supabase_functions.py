#!/usr/bin/env python3
"""
Check what RPC functions exist in Supabase.
"""

import asyncio
import sys
import os
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def check_supabase_functions():
    """Check available RPC functions in Supabase."""
    print("🔍 ПРОВЕРКА НА SUPABASE RPC ФУНКЦИИ")
    print("=" * 50)
    
    # Initialize Supabase client
    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_SERVICE_KEY") or os.getenv("SUPABASE_KEY")
    supabase: Client = create_client(url, key)
    
    # Test different RPC functions that might exist
    rpc_functions_to_test = [
        'match_eu_funds_content',
        'match_documents',
        'match_content',
        'search_content',
        'vector_search',
        'similarity_search'
    ]
    
    print("🧪 ТЕСТВАНЕ НА RPC ФУНКЦИИ:")
    
    for func_name in rpc_functions_to_test:
        try:
            # Try to call the function with minimal parameters
            result = supabase.rpc(func_name, {}).execute()
            print(f"   ✅ {func_name}: съществува")
        except Exception as e:
            error_msg = str(e).lower()
            if "function" in error_msg and "does not exist" in error_msg:
                print(f"   ❌ {func_name}: не съществува")
            elif "missing" in error_msg or "required" in error_msg:
                print(f"   ✅ {func_name}: съществува (но нужни параметри)")
            else:
                print(f"   ⚠️ {func_name}: грешка - {e}")
    
    # Check table structure for embeddings
    print(f"\n🔍 ПРОВЕРКА НА СТРУКТУРАТА НА eu_funds_content:")
    try:
        # Get table info
        result = supabase.table('eu_funds_content').select("*").limit(1).execute()
        
        if result.data:
            record = result.data[0]
            columns = list(record.keys())
            print(f"   Колони: {columns}")
            
            # Check if there's an embedding column
            if 'embedding' in columns:
                print(f"   ✅ Има embedding колона")
            else:
                print(f"   ❌ Няма embedding колона")
                
            # Check if there's a vector column
            if 'vector' in columns:
                print(f"   ✅ Има vector колона")
            else:
                print(f"   ❌ Няма vector колона")
        else:
            print(f"   ❌ Няма данни в таблицата")
            
    except Exception as e:
        print(f"   ❌ Грешка при проверка: {e}")
    
    # Try to create a simple embedding search manually
    print(f"\n🧪 ТЕСТ НА MANUAL VECTOR SEARCH:")
    try:
        # Create a simple embedding (all zeros for test)
        test_embedding = [0.0] * 1536
        
        # Try different approaches
        approaches = [
            "SELECT * FROM eu_funds_content ORDER BY embedding <-> %s LIMIT 3",
            "SELECT *, (1 - (embedding <=> %s)) as similarity FROM eu_funds_content ORDER BY embedding <=> %s LIMIT 3"
        ]
        
        for i, query in enumerate(approaches):
            try:
                print(f"   Подход {i+1}: SQL заявка...")
                # This won't work directly, but we can try RPC
                print(f"   ⚠️ Не мога да изпълня SQL директно")
            except Exception as e:
                print(f"   ❌ Подход {i+1}: {e}")
    
    except Exception as e:
        print(f"   ❌ Грешка при manual search: {e}")

if __name__ == "__main__":
    asyncio.run(check_supabase_functions())
