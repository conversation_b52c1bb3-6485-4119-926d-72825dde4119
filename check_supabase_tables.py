#!/usr/bin/env python3
"""
Check all Supabase tables and their content.
"""

import asyncio
import sys
import os
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def check_all_tables():
    """Check all tables in Supabase."""
    print("🔍 ПРОВЕРКА НА ВСИЧКИ SUPABASE ТАБЛИЦИ")
    print("=" * 50)
    
    # Initialize Supabase client
    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_SERVICE_KEY") or os.getenv("SUPABASE_KEY")
    
    if not url or not key:
        print("❌ Няма SUPABASE_URL или SUPABASE_KEY в .env файла")
        return
    
    supabase: Client = create_client(url, key)
    
    try:
        # Get all table names
        print("📋 СПИСЪК НА ТАБЛИЦИТЕ:")
        
        # Check common table names
        table_names = [
            'documents', 'content', 'chunks', 'embeddings', 
            'crawled_content', 'eu_funds_content', 'vector_store',
            'crawl_results', 'processed_content'
        ]
        
        existing_tables = []
        
        for table_name in table_names:
            try:
                result = supabase.table(table_name).select("*", count="exact").limit(1).execute()
                count = result.count if hasattr(result, 'count') else len(result.data)
                existing_tables.append((table_name, count))
                print(f"✅ {table_name}: {count} записа")
            except Exception as e:
                if "does not exist" in str(e) or "relation" in str(e):
                    print(f"❌ {table_name}: не съществува")
                else:
                    print(f"⚠️ {table_name}: грешка - {e}")
        
        print(f"\n📊 НАМЕРЕНИ ТАБЛИЦИ: {len(existing_tables)}")
        
        # Check content of existing tables
        for table_name, count in existing_tables:
            if count > 0:
                print(f"\n🔍 СЪДЪРЖАНИЕ НА {table_name.upper()}:")
                try:
                    result = supabase.table(table_name).select("*").limit(5).execute()
                    for i, record in enumerate(result.data[:3]):
                        print(f"  Запис {i+1}: {list(record.keys())}")
                        if 'content' in record:
                            content_preview = record['content'][:100] + "..." if len(record['content']) > 100 else record['content']
                            print(f"    Content: {content_preview}")
                        if 'url' in record:
                            print(f"    URL: {record['url']}")
                        if 'title' in record:
                            print(f"    Title: {record['title']}")
                        print()
                except Exception as e:
                    print(f"    Грешка при четене: {e}")
        
        # Check for vector extension
        print(f"\n🔧 ПРОВЕРКА НА VECTOR EXTENSION:")
        try:
            result = supabase.rpc('check_vector_extension').execute()
            print("✅ Vector extension е активно")
        except:
            print("⚠️ Не мога да проверя vector extension")
            
    except Exception as e:
        print(f"❌ Обща грешка: {e}")

if __name__ == "__main__":
    asyncio.run(check_all_tables())
