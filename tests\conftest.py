"""
Pytest configuration and fixtures for EU Funds MCP Server tests
Provides shared fixtures and test configuration following Context Engineering principles
"""

import pytest
import pytest_asyncio
import asyncio
import logging
import os
import sys
from typing import AsyncGenerator, Generator
from unittest.mock import AsyncMock, MagicMock

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.core.config import settings
from src.core.vector_store import VectorStore
from src.core.embeddings import EmbeddingProcessor
from src.core.text_processor import BulgarianTextProcessor
from src.core.hybrid_search import HybridSearchEngine
from src.core.reranker import CrossEncoderReranker
from src.mcp.tools import EUFundsMCPTools
from src.crawler.eu_funds_crawler import EUFundsCrawler

# Configure test logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest_asyncio.fixture
async def vector_store() -> AsyncGenerator[VectorStore, None]:
    """Provide a VectorStore instance for testing."""
    store = VectorStore()
    await store.initialize()
    yield store
    await store.cleanup()

@pytest_asyncio.fixture
async def embedding_processor() -> AsyncGenerator[EmbeddingProcessor, None]:
    """Provide an EmbeddingProcessor instance for testing."""
    processor = EmbeddingProcessor()
    await processor.initialize()
    yield processor
    await processor.cleanup()

@pytest.fixture
def text_processor() -> BulgarianTextProcessor:
    """Provide a BulgarianTextProcessor instance for testing."""
    return BulgarianTextProcessor()

@pytest_asyncio.fixture
async def hybrid_search(vector_store, embedding_processor) -> AsyncGenerator[HybridSearchEngine, None]:
    """Provide a HybridSearchEngine instance for testing."""
    engine = HybridSearchEngine(vector_store, embedding_processor)
    await engine.initialize()
    yield engine
    await engine.cleanup()

@pytest_asyncio.fixture
async def reranker() -> AsyncGenerator[CrossEncoderReranker, None]:
    """Provide a CrossEncoderReranker instance for testing."""
    reranker = CrossEncoderReranker()
    await reranker.initialize()
    yield reranker
    await reranker.cleanup()

@pytest_asyncio.fixture
async def mcp_tools() -> AsyncGenerator[EUFundsMCPTools, None]:
    """Provide an EUFundsMCPTools instance for testing."""
    tools = EUFundsMCPTools()
    await tools.initialize()
    yield tools
    await tools.cleanup()

@pytest_asyncio.fixture
async def crawler() -> AsyncGenerator[EUFundsCrawler, None]:
    """Provide an EUFundsCrawler instance for testing."""
    crawler = EUFundsCrawler()
    await crawler.initialize()
    yield crawler
    await crawler.cleanup()

@pytest.fixture
def sample_bulgarian_text() -> str:
    """Provide sample Bulgarian text for testing."""
    return """
    Оперативна програма "Иновации и конкурентоспособност" (ОПИК) 2014-2020 г. 
    подкрепя развитието на българската икономика чрез финансиране на проекти 
    за повишаване на конкурентоспособността на предприятията, развитие на 
    иновациите и предприемачеството, както и подобряване на достъпа до 
    финансиране за малките и средните предприятия.
    """

@pytest.fixture
def sample_english_text() -> str:
    """Provide sample English text for testing."""
    return """
    The European Regional Development Fund (ERDF) aims to strengthen economic 
    and social cohesion in the European Union by correcting imbalances between 
    its regions. The ERDF focuses on several key priority areas including 
    innovation and research, the digital agenda, support for small and 
    medium-sized enterprises (SMEs), and the low-carbon economy.
    """

@pytest.fixture
def mock_crawl_result():
    """Provide a mock crawl result for testing."""
    from src.core.models import CrawlResult, ContentMetadata
    
    return CrawlResult(
        url="https://test.bg",
        content="Тестово българско съдържание за европейски фондове",
        success=True,
        metadata=ContentMetadata(
            title="Тест заглавие",
            language="bg",
            content_type="general_info",
            quality_score=0.85,
            word_count=8
        )
    )

@pytest.fixture
def mock_search_results():
    """Provide mock search results for testing."""
    return [
        {
            "content": "ОПИК програмата подкрепя иновации в България",
            "similarity_score": 0.85,
            "metadata": {"language": "bg", "content_type": "program_info"},
            "source_url": "https://opic.bg/test1"
        },
        {
            "content": "European funds for SME development",
            "similarity_score": 0.75,
            "metadata": {"language": "en", "content_type": "sme_program"},
            "source_url": "https://ec.europa.eu/test2"
        },
        {
            "content": "Регионални програми за развитие",
            "similarity_score": 0.70,
            "metadata": {"language": "bg", "content_type": "regional_program"},
            "source_url": "https://eufunds.bg/test3"
        }
    ]

# Test markers for different test categories
pytest.mark.unit = pytest.mark.unit
pytest.mark.integration = pytest.mark.integration
pytest.mark.bulgarian = pytest.mark.bulgarian
pytest.mark.slow = pytest.mark.slow

# Test configuration
TEST_CONFIG = {
    "max_test_duration": 300,  # 5 minutes max per test
    "bulgarian_content_threshold": 0.5,  # 50% Bulgarian content minimum
    "performance_threshold": 1.0,  # 1 second max response time
    "coverage_threshold": 0.9,  # 90% coverage requirement
}
