#!/usr/bin/env python3
"""
Setup proper schema in Supabase with embeddings support.
"""

import asyncio
import sys
import os
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def setup_schema():
    """Setup proper schema in Supabase."""
    print("🔧 НАСТРОЙКА НА ПРАВИЛНА СХЕМА В SUPABASE")
    print("=" * 60)
    
    # Initialize Supabase client
    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_SERVICE_KEY") or os.getenv("SUPABASE_KEY")
    supabase: Client = create_client(url, key)
    
    # Read SQL schema
    try:
        with open('create_proper_schema.sql', 'r', encoding='utf-8') as f:
            sql_content = f.read()
    except Exception as e:
        print(f"❌ Грешка при четене на SQL файла: {e}")
        return False
    
    # Split SQL into individual statements
    sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
    
    print(f"📝 Изпълняване на {len(sql_statements)} SQL команди...")
    
    successful = 0
    failed = 0
    
    for i, statement in enumerate(sql_statements):
        try:
            print(f"   {i+1}/{len(sql_statements)}: ", end="")
            
            # Skip comments and empty statements
            if statement.startswith('--') or not statement.strip():
                print("пропуснато (коментар)")
                continue
            
            # Execute SQL statement
            result = supabase.rpc('exec_sql', {'sql': statement}).execute()
            print("✅ успешно")
            successful += 1
            
        except Exception as e:
            error_msg = str(e)
            
            # Check if it's a harmless error
            if any(phrase in error_msg.lower() for phrase in [
                'already exists', 'does not exist', 'extension', 'policy'
            ]):
                print(f"⚠️ предупреждение: {error_msg[:50]}...")
            else:
                print(f"❌ грешка: {error_msg[:50]}...")
                failed += 1
    
    print(f"\n📊 РЕЗУЛТАТ:")
    print(f"   ✅ Успешни: {successful}")
    print(f"   ❌ Неуспешни: {failed}")
    
    if failed == 0:
        print(f"🎉 Схемата е настроена успешно!")
        
        # Test the new functions
        print(f"\n🧪 ТЕСТВАНЕ НА НОВИТЕ ФУНКЦИИ:")
        await test_new_functions(supabase)
        
        return True
    else:
        print(f"⚠️ Има проблеми със схемата")
        return False

async def test_new_functions(supabase: Client):
    """Test the newly created functions."""
    
    # Test functions
    functions_to_test = [
        'match_eu_funds_content',
        'search_eu_funds_text', 
        'semantic_search'
    ]
    
    for func_name in functions_to_test:
        try:
            # Try with minimal parameters
            if func_name == 'match_eu_funds_content':
                test_embedding = [0.0] * 1536
                result = supabase.rpc(func_name, {
                    'query_embedding': test_embedding,
                    'match_count': 1
                }).execute()
            elif func_name == 'search_eu_funds_text':
                result = supabase.rpc(func_name, {
                    'search_query': 'програма',
                    'match_count': 1
                }).execute()
            elif func_name == 'semantic_search':
                test_embedding = [0.0] * 1536
                result = supabase.rpc(func_name, {
                    'query_embedding': test_embedding,
                    'search_query': 'програма',
                    'match_count': 1
                }).execute()
            
            print(f"   ✅ {func_name}: работи")
            
        except Exception as e:
            error_msg = str(e)
            if "does not exist" in error_msg.lower():
                print(f"   ❌ {func_name}: не съществува")
            else:
                print(f"   ⚠️ {func_name}: {error_msg[:50]}...")

if __name__ == "__main__":
    success = asyncio.run(setup_schema())
    if success:
        print("\n✅ Схемата е готова за използване!")
        print("🚀 Сега можете да стартирате advanced crawler отново")
    else:
        print("\n❌ Има проблеми със схемата!")
        exit(1)
