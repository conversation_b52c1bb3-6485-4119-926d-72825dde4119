#!/usr/bin/env python3
"""
Quick test to verify RAG system works with current data.
"""

import asyncio
import sys
import os

sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.core.hybrid_search import HybridSearchEngine

async def quick_test():
    """Quick test of RAG system."""
    print("🧪 БЪРЗ ТЕСТ НА RAG СИСТЕМАТА")
    print("=" * 40)
    
    # Initialize search engine
    search_engine = HybridSearchEngine()
    await search_engine.initialize()
    
    # Test questions
    questions = [
        "Какво финансиране има за малки предприятия?",
        "Какви програми има за иновации?",
        "Как да кандидатствам за европейско финансиране?"
    ]
    
    for i, question in enumerate(questions, 1):
        print(f"\n📝 Въпрос {i}: {question}")
        
        try:
            # Search for answer
            results = await search_engine.search(question, limit=3)
            
            if results and results.results:
                print(f"   ✅ Намерени {len(results.results)} резултата")
                print(f"   📊 Най-висок score: {results.results[0].similarity_score:.3f}")
                print(f"   📄 Отговор: {results.results[0].content[:150]}...")
            else:
                print(f"   ❌ Няма намерени резултати")
                
        except Exception as e:
            print(f"   ❌ Грешка: {e}")
    
    print(f"\n🎯 ТЕСТ ЗАВЪРШЕН!")

if __name__ == "__main__":
    asyncio.run(quick_test())
