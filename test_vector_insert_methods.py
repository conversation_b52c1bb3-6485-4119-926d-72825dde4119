#!/usr/bin/env python3
"""
Test different methods to insert vector data into Supabase.
"""

import asyncio
import sys
import os
from supabase import create_client, Client
from dotenv import load_dotenv
import openai
import json

# Load environment variables
load_dotenv()

async def test_vector_insert_methods():
    """Test different methods to insert vector data."""
    print("🧪 ТЕСТВАНЕ НА РАЗЛИЧНИ МЕТОДИ ЗА VECTOR INSERT")
    print("=" * 60)
    
    # Initialize Supabase client
    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_SERVICE_KEY") or os.getenv("SUPABASE_KEY")
    supabase: Client = create_client(url, key)
    
    # Initialize OpenAI
    openai.api_key = os.getenv("OPENAI_API_KEY")
    
    # Create a small test embedding
    print("🔧 Създаване на тестов embedding...")
    try:
        response = openai.embeddings.create(
            model="text-embedding-3-small",
            input="тест"
        )
        
        original_embedding = response.data[0].embedding
        print(f"   ✅ Оригинален embedding: {len(original_embedding)} dimensions")
        
        # Method 1: Use RPC function directly
        print(f"\n🧪 МЕТОД 1: Използване на RPC функция")
        try:
            # Try to insert using RPC function
            result = supabase.rpc('insert_with_embedding', {
                'p_title': 'Test RPC',
                'p_content': 'Test content',
                'p_source_url': 'https://test.com',
                'p_embedding': original_embedding
            }).execute()
            
            print(f"   ✅ RPC метод работи")
            
        except Exception as e:
            print(f"   ❌ RPC метод: {e}")
        
        # Method 2: Use raw SQL through database query API
        print(f"\n🧪 МЕТОД 2: Raw SQL чрез database query API")
        try:
            embedding_str = "[" + ",".join(map(str, original_embedding)) + "]"
            
            # Try using the database query API
            import requests
            
            headers = {
                'apikey': key,
                'Authorization': f'Bearer {key}',
                'Content-Type': 'application/json'
            }
            
            # Use the database query endpoint
            query_url = f"{url.replace('https://', 'https://').replace('.supabase.co', '.supabase.co')}/rest/v1/rpc/query"
            
            sql_query = f"""
            INSERT INTO eu_funds_content (title, content, source_url, content_type, language, metadata, quality_score, embedding)
            VALUES (
                'Test SQL',
                'Test content',
                'https://test.com',
                'test',
                'bg',
                '{{"test": true}}',
                0.8,
                '{embedding_str}'::vector
            ) RETURNING id;
            """
            
            response = requests.post(query_url, 
                                   headers=headers, 
                                   json={'query': sql_query})
            
            print(f"   Response: {response.status_code} - {response.text[:100]}")
            
        except Exception as e:
            print(f"   ❌ SQL метод: {e}")
        
        # Method 3: Create custom RPC function
        print(f"\n🧪 МЕТОД 3: Създаване на custom RPC функция")
        try:
            # Create RPC function for inserting with embeddings
            create_function_sql = """
            CREATE OR REPLACE FUNCTION insert_with_embedding(
                p_title TEXT,
                p_content TEXT,
                p_source_url TEXT,
                p_content_type TEXT DEFAULT 'web_page',
                p_language TEXT DEFAULT 'bg',
                p_metadata JSONB DEFAULT '{}',
                p_quality_score FLOAT DEFAULT 0.5,
                p_embedding vector(1536) DEFAULT NULL
            )
            RETURNS UUID
            LANGUAGE plpgsql
            AS $$
            DECLARE
                new_id UUID;
            BEGIN
                INSERT INTO eu_funds_content (title, content, source_url, content_type, language, metadata, quality_score, embedding)
                VALUES (p_title, p_content, p_source_url, p_content_type, p_language, p_metadata, p_quality_score, p_embedding)
                RETURNING id INTO new_id;
                
                RETURN new_id;
            END;
            $$;
            """
            
            # Try to create the function using Supabase API
            print(f"   Създаване на RPC функция...")
            
            # This would need to be done through SQL editor or API
            print(f"   ⚠️ Функцията трябва да се създаде в SQL Editor")
            
        except Exception as e:
            print(f"   ❌ Custom RPC: {e}")
        
        # Method 4: Use PostgREST with proper headers
        print(f"\n🧪 МЕТОД 4: PostgREST с правилни headers")
        try:
            import requests
            
            headers = {
                'apikey': key,
                'Authorization': f'Bearer {key}',
                'Content-Type': 'application/json',
                'Prefer': 'return=representation'
            }
            
            # Prepare data
            data = {
                "title": "Test PostgREST",
                "content": "Test content",
                "source_url": "https://test.com",
                "content_type": "test",
                "language": "bg",
                "metadata": {"test": True},
                "quality_score": 0.8,
                "embedding": original_embedding  # Try direct array
            }
            
            # Use PostgREST API
            rest_url = f"{url}/rest/v1/eu_funds_content"
            
            response = requests.post(rest_url, 
                                   headers=headers, 
                                   json=data)
            
            print(f"   Response: {response.status_code}")
            if response.status_code == 201:
                print(f"   ✅ PostgREST метод работи!")
                result_data = response.json()
                if result_data:
                    inserted_id = result_data[0]['id']
                    print(f"   📝 Записан с ID: {inserted_id}")
                    
                    # Check the embedding
                    check_result = supabase.table('eu_funds_content').select("*").eq('id', inserted_id).execute()
                    if check_result.data:
                        stored_embedding = check_result.data[0]['embedding']
                        print(f"   📊 Embedding размер: {len(stored_embedding) if stored_embedding else 'None'}")
                        
                        if isinstance(stored_embedding, list) and len(stored_embedding) == 1536:
                            print(f"   🎉 УСПЕХ! Правилен vector формат!")
                        else:
                            print(f"   ❌ Неправилен формат: {type(stored_embedding)}")
                    
                    # Clean up
                    supabase.table('eu_funds_content').delete().eq('id', inserted_id).execute()
                    print(f"   🧹 Изтрит тестов запис")
            else:
                print(f"   ❌ Грешка: {response.text}")
                
        except Exception as e:
            print(f"   ❌ PostgREST метод: {e}")
            
    except Exception as e:
        print(f"❌ Грешка при OpenAI: {e}")

if __name__ == "__main__":
    asyncio.run(test_vector_insert_methods())
