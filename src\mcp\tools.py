"""
MCP Tools Implementation for EU Funds MCP Server
Implements the 4 core MCP tools for EU funding information access
"""

import logging
import asyncio
import time
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import json

from src.core.hybrid_search import HybridSearchEngine
from src.core.reranker import CrossEncoderReranker
from src.core.config import settings
from src.core.performance import get_performance_monitor

logger = logging.getLogger(__name__)

class EUFundsMCPTools:
    """
    Core MCP tools for EU Funds information access.
    Implements 4 main tools according to PLANNING.md Phase 3.3.
    """
    
    def __init__(self):
        """Initialize MCP tools."""
        self.search_engine = HybridSearchEngine()
        self.reranker = CrossEncoderReranker()
        self.initialized = False
        
        # Tool configuration
        self.default_max_results = 10
        self.default_rerank_top_k = 5
        self.min_relevance_score = 0.3
        
        # Performance tracking
        self.tool_stats = {
            "search_eu_funds": {"calls": 0, "avg_time": 0.0},
            "get_funding_programs": {"calls": 0, "avg_time": 0.0},
            "analyze_eligibility": {"calls": 0, "avg_time": 0.0},
            "get_application_deadlines": {"calls": 0, "avg_time": 0.0}
        }
    
    async def initialize(self) -> None:
        """Initialize all MCP tools components."""
        try:
            logger.info("🔧 Initializing EU Funds MCP Tools...")
            
            # Initialize search engine
            await self.search_engine.initialize()
            logger.info("✅ Hybrid search engine initialized")
            
            # Initialize reranker
            await self.reranker.initialize()
            logger.info("✅ Cross-encoder reranker initialized")
            
            self.initialized = True
            logger.info("✅ EU Funds MCP Tools initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize MCP tools: {e}")
            raise
    
    async def search_eu_funds(
        self,
        query: str,
        max_results: int = 10,
        use_reranking: bool = True,
        filters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Tool 1: General EU funds search
        
        Args:
            query: Search query in Bulgarian or English
            max_results: Maximum number of results to return
            use_reranking: Whether to apply cross-encoder reranking
            filters: Optional filters (language, content_type, etc.)
        
        Returns:
            Dictionary with search results and metadata
        """
        start_time = time.time()
        tool_name = "search_eu_funds"
        monitor = get_performance_monitor()

        try:
            if not self.initialized:
                await self.initialize()

            logger.info(f"🔍 Searching EU funds: '{query[:50]}...'")

            # Perform hybrid search
            search_result = await self.search_engine.search(
                query=query,
                limit=max_results * 2,  # Get more candidates for reranking
                search_type="hybrid",
                weights={"vector": 0.7, "text": 0.3, "cross_encoder": 0.3}
            )
            
            # Apply reranking if requested
            if use_reranking and search_result.results:
                enhanced_result = await self.reranker.rerank_hybrid_result(
                    query=query,
                    hybrid_result=search_result,
                    top_k=max_results
                )
                final_results = enhanced_result.results
                processing_info = {
                    "fusion_method": enhanced_result.fusion_method,
                    "total_processing_time": enhanced_result.processing_time,
                    "reranking_applied": True
                }
            else:
                final_results = search_result.results[:max_results]
                processing_info = {
                    "fusion_method": search_result.fusion_method,
                    "total_processing_time": search_result.processing_time,
                    "reranking_applied": False
                }
            
            # Format results for MCP response
            formatted_results = []
            for result in final_results:
                formatted_result = {
                    "content": result.get("content", ""),
                    "source_url": result.get("source_url", ""),
                    "title": result.get("metadata", {}).get("title", ""),
                    "content_type": result.get("metadata", {}).get("content_type", ""),
                    "language": result.get("metadata", {}).get("language", ""),
                    "relevance_score": result.get("final_score", result.get("similarity_score", 0)),
                    "summary": result.get("content", "")[:200] + "..." if len(result.get("content", "")) > 200 else result.get("content", "")
                }
                formatted_results.append(formatted_result)
            
            # Update statistics and performance monitoring
            processing_time = time.time() - start_time
            self._update_tool_stats(tool_name, processing_time)
            monitor.record_request(processing_time, success=True, tool_name=tool_name)

            response = {
                "results": formatted_results,
                "total_found": len(formatted_results),
                "query": query,
                "processing_info": processing_info,
                "timestamp": datetime.utcnow().isoformat(),
                "tool": tool_name,
                "performance": {
                    "response_time_ms": round(processing_time * 1000, 2),
                    "cached": processing_info.get("cached", False)
                }
            }

            logger.info(f"✅ Search completed: {len(formatted_results)} results in {processing_time:.3f}s")
            return response

        except Exception as e:
            processing_time = time.time() - start_time
            monitor.record_request(processing_time, success=False, tool_name=tool_name)
            logger.error(f"❌ Search failed: {e}")
            return {
                "results": [],
                "total_found": 0,
                "query": query,
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat(),
                "tool": tool_name
            }
    
    async def get_funding_programs(
        self,
        sector: Optional[str] = None,
        target_group: Optional[str] = None,
        budget_range: Optional[str] = None,
        deadline_months: int = 12
    ) -> Dict[str, Any]:
        """
        Tool 2: Discover funding programs by criteria
        
        Args:
            sector: Target sector (agriculture, research, SME, etc.)
            target_group: Target beneficiaries (farmers, researchers, SMEs, etc.)
            budget_range: Budget range (small, medium, large)
            deadline_months: Look for programs with deadlines in next N months
        
        Returns:
            Dictionary with funding programs and metadata
        """
        start_time = datetime.utcnow()
        tool_name = "get_funding_programs"
        
        try:
            if not self.initialized:
                await self.initialize()
            
            # Build search query based on criteria
            query_parts = []
            if sector:
                query_parts.append(f"сектор {sector}")
            if target_group:
                query_parts.append(f"целева група {target_group}")
            if budget_range:
                query_parts.append(f"бюджет {budget_range}")
            
            query = " ".join(query_parts) if query_parts else "програми за финансиране"
            
            logger.info(f"🎯 Finding funding programs: {query}")
            
            # Search with program-specific filters
            filters = {
                "content_type": "funding_program",
                "language": "bg"
            }
            
            search_result = await self.search_eu_funds(
                query=query,
                max_results=15,
                use_reranking=True,
                filters=filters
            )
            
            # Filter and enhance results for funding programs
            programs = []
            for result in search_result.get("results", []):
                program = {
                    "program_name": result.get("title", ""),
                    "description": result.get("summary", ""),
                    "sector": sector or "Общо",
                    "target_group": target_group or "Всички",
                    "source_url": result.get("source_url", ""),
                    "relevance_score": result.get("relevance_score", 0),
                    "estimated_deadline": "Проверете официалния сайт",
                    "budget_info": "Вижте пълната информация в програмата"
                }
                programs.append(program)
            
            # Update statistics
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            self._update_tool_stats(tool_name, processing_time)
            
            response = {
                "programs": programs,
                "total_found": len(programs),
                "search_criteria": {
                    "sector": sector,
                    "target_group": target_group,
                    "budget_range": budget_range,
                    "deadline_months": deadline_months
                },
                "timestamp": datetime.utcnow().isoformat(),
                "tool": tool_name
            }
            
            logger.info(f"✅ Found {len(programs)} funding programs in {processing_time:.3f}s")
            return response
            
        except Exception as e:
            logger.error(f"❌ Program discovery failed: {e}")
            return {
                "programs": [],
                "total_found": 0,
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat(),
                "tool": tool_name
            }
    
    async def analyze_eligibility(
        self,
        organization_type: str,
        sector: str,
        project_description: str,
        budget_needed: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Tool 3: Analyze eligibility for EU funding
        
        Args:
            organization_type: Type of organization (SME, NGO, university, etc.)
            sector: Project sector
            project_description: Description of the project
            budget_needed: Estimated budget needed
        
        Returns:
            Dictionary with eligibility analysis and recommendations
        """
        start_time = datetime.utcnow()
        tool_name = "analyze_eligibility"
        
        try:
            if not self.initialized:
                await self.initialize()
            
            # Build eligibility query
            query = f"критерии за кандидатстване {organization_type} {sector} {project_description[:100]}"
            
            logger.info(f"📋 Analyzing eligibility: {organization_type} in {sector}")
            
            # Search for eligibility criteria
            search_result = await self.search_eu_funds(
                query=query,
                max_results=10,
                use_reranking=True,
                filters={"content_type": "eligibility_criteria"}
            )
            
            # Analyze eligibility based on search results
            eligibility_analysis = {
                "organization_eligible": True,  # Default assumption
                "matching_programs": [],
                "requirements_met": [],
                "missing_requirements": [],
                "recommendations": []
            }
            
            # Process search results for eligibility info
            for result in search_result.get("results", []):
                program_info = {
                    "program_name": result.get("title", ""),
                    "eligibility_score": result.get("relevance_score", 0),
                    "key_requirements": result.get("summary", ""),
                    "source_url": result.get("source_url", "")
                }
                eligibility_analysis["matching_programs"].append(program_info)
            
            # Generate recommendations based on organization type and sector
            recommendations = [
                f"Проверете специфичните критерии за {organization_type}",
                f"Уверете се, че проектът отговаря на приоритетите в сектор {sector}",
                "Подгответе необходимата документация предварително",
                "Консултирайте се с експерт по EU фондове"
            ]
            
            if budget_needed:
                recommendations.append(f"Проверете дали бюджетът от {budget_needed} е в допустимите граници")
            
            eligibility_analysis["recommendations"] = recommendations
            
            # Update statistics
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            self._update_tool_stats(tool_name, processing_time)
            
            response = {
                "eligibility_analysis": eligibility_analysis,
                "input_criteria": {
                    "organization_type": organization_type,
                    "sector": sector,
                    "project_description": project_description[:200],
                    "budget_needed": budget_needed
                },
                "timestamp": datetime.utcnow().isoformat(),
                "tool": tool_name
            }
            
            logger.info(f"✅ Eligibility analysis completed in {processing_time:.3f}s")
            return response
            
        except Exception as e:
            logger.error(f"❌ Eligibility analysis failed: {e}")
            return {
                "eligibility_analysis": {"error": str(e)},
                "timestamp": datetime.utcnow().isoformat(),
                "tool": tool_name
            }
    
    async def get_application_deadlines(
        self,
        months_ahead: int = 6,
        sector_filter: Optional[str] = None,
        urgent_only: bool = False
    ) -> Dict[str, Any]:
        """
        Tool 4: Get application deadlines for EU funding
        
        Args:
            months_ahead: Look for deadlines in next N months
            sector_filter: Filter by specific sector
            urgent_only: Show only urgent deadlines (next 30 days)
        
        Returns:
            Dictionary with upcoming deadlines and program info
        """
        start_time = datetime.utcnow()
        tool_name = "get_application_deadlines"
        
        try:
            if not self.initialized:
                await self.initialize()
            
            # Build deadline query
            if urgent_only:
                query = "спешни крайни срокове кандидатстване"
                months_ahead = 1
            else:
                query = f"крайни срокове кандидатстване следващи {months_ahead} месеца"
            
            if sector_filter:
                query += f" {sector_filter}"
            
            logger.info(f"📅 Finding deadlines: {months_ahead} months ahead")
            
            # Search for deadline information
            search_result = await self.search_eu_funds(
                query=query,
                max_results=20,
                use_reranking=True,
                filters={"content_type": "deadline_info"}
            )
            
            # Process and format deadline information
            deadlines = []
            current_date = datetime.utcnow()
            
            for result in search_result.get("results", []):
                deadline_info = {
                    "program_name": result.get("title", ""),
                    "deadline_date": "Проверете официалния сайт",
                    "days_remaining": "Неизвестно",
                    "urgency_level": "medium",
                    "description": result.get("summary", ""),
                    "source_url": result.get("source_url", ""),
                    "relevance_score": result.get("relevance_score", 0)
                }
                
                # Determine urgency based on content analysis
                content = result.get("content", "").lower()
                if any(word in content for word in ["спешно", "скоро", "наближава"]):
                    deadline_info["urgency_level"] = "high"
                elif any(word in content for word in ["отворен", "продължава"]):
                    deadline_info["urgency_level"] = "low"
                
                deadlines.append(deadline_info)
            
            # Sort by urgency and relevance
            deadlines.sort(key=lambda x: (
                {"high": 0, "medium": 1, "low": 2}[x["urgency_level"]],
                -x["relevance_score"]
            ))
            
            # Update statistics
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            self._update_tool_stats(tool_name, processing_time)
            
            response = {
                "deadlines": deadlines,
                "total_found": len(deadlines),
                "search_parameters": {
                    "months_ahead": months_ahead,
                    "sector_filter": sector_filter,
                    "urgent_only": urgent_only
                },
                "timestamp": datetime.utcnow().isoformat(),
                "tool": tool_name
            }
            
            logger.info(f"✅ Found {len(deadlines)} deadlines in {processing_time:.3f}s")
            return response
            
        except Exception as e:
            logger.error(f"❌ Deadline search failed: {e}")
            return {
                "deadlines": [],
                "total_found": 0,
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat(),
                "tool": tool_name
            }
    
    def _update_tool_stats(self, tool_name: str, processing_time: float):
        """Update tool performance statistics."""
        if tool_name in self.tool_stats:
            stats = self.tool_stats[tool_name]
            stats["calls"] += 1
            stats["avg_time"] = (
                (stats["avg_time"] * (stats["calls"] - 1) + processing_time) 
                / stats["calls"]
            )
    
    def get_tool_stats(self) -> Dict[str, Any]:
        """Get tool performance statistics."""
        return {
            "tool_statistics": self.tool_stats,
            "initialized": self.initialized,
            "timestamp": datetime.utcnow().isoformat()
        }

    async def get_performance_metrics(self) -> Dict[str, Any]:
        """
        Get comprehensive performance metrics for the MCP server.

        Returns:
            Dictionary with performance metrics, cache stats, and system health
        """
        try:
            monitor = get_performance_monitor()

            # Get overall performance metrics
            metrics = monitor.get_metrics()

            # Get tool-specific metrics
            tool_metrics = monitor.get_tool_metrics()

            # Get health status
            health_status = monitor.get_health_status()

            # Get cache statistics
            cache_stats = {}
            if hasattr(self.search_engine, 'vector_store') and self.search_engine.vector_store.search_cache:
                cache_stats = await self.search_engine.vector_store.search_cache.cache.get_stats()

            return {
                "performance": {
                    "avg_response_time": metrics.avg_response_time,
                    "p95_response_time": metrics.p95_response_time,
                    "p99_response_time": metrics.p99_response_time,
                    "requests_per_second": metrics.requests_per_second,
                    "total_requests": metrics.total_requests,
                    "success_rate": metrics.successful_requests / max(metrics.total_requests, 1)
                },
                "system": {
                    "cpu_usage": metrics.cpu_usage,
                    "memory_usage": metrics.memory_usage,
                    "memory_usage_mb": metrics.memory_usage_mb
                },
                "cache": cache_stats,
                "tools": tool_metrics,
                "health": health_status,
                "timestamp": datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"❌ Failed to get performance metrics: {e}")
            return {
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
    
    async def cleanup(self):
        """Clean up MCP tools resources."""
        try:
            if self.search_engine:
                await self.search_engine.cleanup()
            if self.reranker:
                await self.reranker.cleanup()
            
            logger.info("✅ EU Funds MCP Tools cleanup complete")
            
        except Exception as e:
            logger.error(f"❌ MCP tools cleanup failed: {e}")
