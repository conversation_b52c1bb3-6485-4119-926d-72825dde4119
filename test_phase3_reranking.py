"""
Test Phase 3.2: Cross-encoder Reranking & Quality Enhancement
Tests cross-encoder reranking integration for EU Funds MCP Server
"""

import asyncio
import logging
import sys
from datetime import datetime
from typing import List, Dict, Any

# Add src to path for imports
sys.path.append('src')

from src.core.reranker import CrossEncoderReranker
from src.core.hybrid_search import HybridSearchEngine
from src.core.embeddings import EmbeddingProcessor
from src.core.vector_store import VectorStore
from src.core.text_processor import BulgarianTextProcessor
from src.core.models import HybridSearchResult
from src.core.config import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_phase3_reranking():
    """Test Phase 3.2: Cross-encoder Reranking & Quality Enhancement"""
    
    logger.info("🧪 Testing Phase 3.2: Cross-encoder Reranking...")
    logger.info(f"📋 Configuration: {settings.embedding_model}")
    
    # Initialize components
    reranker = None
    hybrid_search = None
    
    try:
        logger.info("🔧 Step 1: Initializing reranking infrastructure...")
        
        # Initialize cross-encoder reranker
        reranker = CrossEncoderReranker()
        await reranker.initialize()
        logger.info("✅ Cross-encoder reranker initialized")
        
        # Initialize hybrid search for comparison
        hybrid_search = HybridSearchEngine()
        await hybrid_search.initialize()
        logger.info("✅ Hybrid search engine initialized")
        
        logger.info("🧠 Step 2: Testing cross-encoder relevance scoring...")
        
        # Test query-document pairs for relevance
        test_pairs = [
            {
                "query": "Как да кандидатствам за европейско финансиране?",
                "content": "Програма за развитие на селските райони 2023-2027 предоставя финансиране за модернизация на земеделските стопанства в България. Кандидатстването се извършва онлайн чрез електронната система на Министерството на земеделието.",
                "expected_relevant": True
            },
            {
                "query": "Програми за подкрепа на малки и средни предприятия",
                "content": "Хоризонт Европа е най-голямата програма за научни изследвания и иновации в ЕС с бюджет от 95.5 милиарда евро за периода 2021-2027. Програмата финансира научни проекти в области като здравеопазване, климат и дигитални технологии.",
                "expected_relevant": False
            },
            {
                "query": "Селско стопанство модернизация проекти",
                "content": "Програмата за развитие на селските райони подкрепя инвестиции в модернизация на земеделските стопанства, включително закупуване на нова техника, изграждане на складове и внедряване на иновативни технологии за устойчиво земеделие.",
                "expected_relevant": True
            }
        ]
        
        relevance_results = []
        for pair in test_pairs:
            is_relevant, score = await reranker.validate_relevance(
                query=pair["query"],
                content=pair["content"],
                min_relevance=0.3
            )
            
            relevance_results.append({
                "query": pair["query"][:40] + "...",
                "expected": pair["expected_relevant"],
                "predicted": is_relevant,
                "score": score,
                "correct": is_relevant == pair["expected_relevant"]
            })
            
            logger.info(f"✅ Query: '{pair['query'][:40]}...'")
            logger.info(f"   Relevance: {is_relevant} (score: {score:.3f})")
            logger.info(f"   Expected: {pair['expected_relevant']} - {'✅' if is_relevant == pair['expected_relevant'] else '❌'}")
        
        accuracy = sum(1 for r in relevance_results if r["correct"]) / len(relevance_results)
        logger.info(f"📊 Relevance validation accuracy: {accuracy:.2%}")
        
        logger.info("🔄 Step 3: Testing result reranking...")
        
        # Create mock search results for reranking
        mock_results = [
            {
                "content": "Програма за развитие на селските райони 2023-2027 предоставя финансиране за модернизация на земеделските стопанства в България.",
                "similarity_score": 0.75,
                "metadata": {"language": "bg", "content_type": "funding_program"},
                "source_url": "https://prsr.bg/program-2023-2027"
            },
            {
                "content": "Хоризонт Европа е най-голямата програма за научни изследвания и иновации в ЕС с бюджет от 95.5 милиарда евро.",
                "similarity_score": 0.65,
                "metadata": {"language": "bg", "content_type": "research_program"},
                "source_url": "https://horizon-europe.bg"
            },
            {
                "content": "COSME програмата подкрепя малки и средни предприятия в Европа чрез финансови инструменти и бизнес услуги.",
                "similarity_score": 0.70,
                "metadata": {"language": "bg", "content_type": "sme_program"},
                "source_url": "https://cosme.bg"
            },
            {
                "content": "European Regional Development Fund supports regional development projects across EU member states.",
                "similarity_score": 0.60,
                "metadata": {"language": "en", "content_type": "regional_fund"},
                "source_url": "https://ec.europa.eu/erdf"
            }
        ]
        
        test_query = "Програми за подкрепа на малки и средни предприятия"
        
        # Test reranking
        reranked_results = await reranker.rerank_results(
            query=test_query,
            search_results=mock_results,
            top_k=3,
            use_bulgarian_boost=True
        )
        
        logger.info(f"✅ Reranked {len(reranked_results)} results:")
        for i, result in enumerate(reranked_results):
            logger.info(f"   {i+1}. Score: {result['final_score']:.3f} (CE: {result['ce_score']:.3f})")
            logger.info(f"      Content: '{result['content'][:60]}...'")
            logger.info(f"      Bulgarian: {result.get('bulgarian_boosted', False)}")
        
        logger.info("🔍 Step 4: Testing hybrid result reranking...")
        
        # Create mock hybrid search result
        mock_hybrid_result = HybridSearchResult(
            results=mock_results,
            total_results=len(mock_results),
            vector_results=4,
            text_results=4,
            processing_time=0.15,
            fusion_method="rrf",
            weights={"vector": 0.7, "text": 0.3}
        )
        
        # Test hybrid result reranking
        enhanced_hybrid_result = await reranker.rerank_hybrid_result(
            query=test_query,
            hybrid_result=mock_hybrid_result,
            top_k=3
        )
        
        logger.info(f"✅ Enhanced hybrid result:")
        logger.info(f"   Fusion method: {enhanced_hybrid_result.fusion_method}")
        logger.info(f"   Processing time: {enhanced_hybrid_result.processing_time:.3f}s")
        logger.info(f"   Weights: {enhanced_hybrid_result.weights}")
        logger.info(f"   Results: {len(enhanced_hybrid_result.results)}")
        
        logger.info("📊 Step 5: Testing batch relevance validation...")
        
        # Test batch relevance validation
        batch_contents = [result["content"] for result in mock_results]
        batch_query = "Финансиране за малки предприятия"
        
        batch_relevance = await reranker.batch_validate_relevance(
            query=batch_query,
            contents=batch_contents,
            min_relevance=0.25
        )
        
        logger.info(f"✅ Batch relevance validation:")
        for i, (is_relevant, score) in enumerate(batch_relevance):
            logger.info(f"   Content {i+1}: {is_relevant} (score: {score:.3f})")
        
        relevant_count = sum(1 for is_rel, _ in batch_relevance if is_rel)
        logger.info(f"   Relevant contents: {relevant_count}/{len(batch_contents)}")
        
        logger.info("📈 Step 6: Testing performance and statistics...")
        
        # Get reranking statistics
        stats = reranker.get_rerank_stats()
        logger.info(f"✅ Reranking statistics:")
        logger.info(f"   Total reranks: {stats['total_reranks']}")
        logger.info(f"   Avg processing time: {stats['avg_processing_time']:.3f}s")
        logger.info(f"   Score improvements: {stats['score_improvements']}")
        logger.info(f"   Bulgarian content boosted: {stats['bulgarian_content_boosted']}")
        logger.info(f"   Model: {stats['model_name']}")
        
        logger.info("🔍 Step 7: Testing quality filtering...")
        
        # Test quality filtering with different thresholds
        thresholds = [0.1, 0.3, 0.5]
        for threshold in thresholds:
            reranker.rerank_threshold = threshold
            filtered_results = await reranker.rerank_results(
                query=test_query,
                search_results=mock_results,
                top_k=10,
                use_bulgarian_boost=True
            )
            
            logger.info(f"✅ Threshold {threshold}: {len(filtered_results)} results passed")
        
        # Reset threshold
        reranker.rerank_threshold = 0.1
        
        logger.info("📋 Phase 3.2 Reranking Summary:")
        logger.info("   ✅ Cross-encoder Reranker: Initialized and tested")
        logger.info(f"   ✅ Relevance Validation: {accuracy:.2%} accuracy")
        logger.info(f"   ✅ Result Reranking: {len(reranked_results)} results enhanced")
        logger.info(f"   ✅ Hybrid Integration: Enhanced fusion method")
        logger.info(f"   ✅ Bulgarian Optimization: Content boosting active")
        logger.info(f"   ✅ Quality Filtering: Multiple thresholds tested")
        
        logger.info("🧹 Cleaning up test resources...")
        
        # Cleanup
        if reranker:
            await reranker.cleanup()
        if hybrid_search:
            await hybrid_search.cleanup()
        
        logger.info("✅ Cleanup complete")
        
        print("\n🎉 Phase 3.2 Cross-encoder Reranking Test PASSED!")
        print("✅ All reranking components working correctly:")
        print("   - Cross-encoder relevance scoring")
        print("   - Result reranking with CE weights (0.3)")
        print("   - Bulgarian content boosting")
        print("   - Quality filtering and validation")
        print("   - Hybrid search integration")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Phase 3.2 test failed: {e}")
        
        # Cleanup on error
        try:
            if reranker:
                await reranker.cleanup()
            if hybrid_search:
                await hybrid_search.cleanup()
        except:
            pass
        
        print(f"\n❌ Phase 3.2 Cross-encoder Reranking Test FAILED!")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_phase3_reranking())
    sys.exit(0 if success else 1)
