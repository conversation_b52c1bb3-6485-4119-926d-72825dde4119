<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_db813965.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">47%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.2">coverage.py v7.9.2</a>,
            created at 2025-07-07 20:48 +0300
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6___init___py.html">src\__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41___init___py.html">src\core\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_config_py.html">src\core\config.py</a></td>
                <td>96</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="82 96">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_crawler_py.html">src\core\crawler.py</a></td>
                <td>139</td>
                <td>139</td>
                <td>0</td>
                <td class="right" data-ratio="0 139">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_database_py.html">src\core\database.py</a></td>
                <td>106</td>
                <td>73</td>
                <td>0</td>
                <td class="right" data-ratio="33 106">31%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_embeddings_py.html">src\core\embeddings.py</a></td>
                <td>209</td>
                <td>65</td>
                <td>0</td>
                <td class="right" data-ratio="144 209">69%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_hybrid_search_py.html">src\core\hybrid_search.py</a></td>
                <td>150</td>
                <td>129</td>
                <td>0</td>
                <td class="right" data-ratio="21 150">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html">src\core\models.py</a></td>
                <td>216</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="216 216">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_reranker_py.html">src\core\reranker.py</a></td>
                <td>128</td>
                <td>109</td>
                <td>0</td>
                <td class="right" data-ratio="19 128">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_text_processor_py.html">src\core\text_processor.py</a></td>
                <td>162</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="128 162">79%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_vector_store_py.html">src\core\vector_store.py</a></td>
                <td>219</td>
                <td>69</td>
                <td>0</td>
                <td class="right" data-ratio="150 219">68%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0a383f9d6d96e73___init___py.html">src\crawler\__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0a383f9d6d96e73_eu_funds_crawler_py.html">src\crawler\eu_funds_crawler.py</a></td>
                <td>146</td>
                <td>121</td>
                <td>0</td>
                <td class="right" data-ratio="25 146">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html">src\main.py</a></td>
                <td>65</td>
                <td>65</td>
                <td>4</td>
                <td class="right" data-ratio="0 65">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c58a7740ba00d4cd_tools_py.html">src\mcp\tools.py</a></td>
                <td>159</td>
                <td>140</td>
                <td>0</td>
                <td class="right" data-ratio="19 159">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40___init___py.html">src\tools\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be___init___py.html">src\utils\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>1800</td>
                <td>958</td>
                <td>4</td>
                <td class="right" data-ratio="842 1800">47%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.2">coverage.py v7.9.2</a>,
            created at 2025-07-07 20:48 +0300
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_6156a86a215061be___init___py.html"></a>
        <a id="nextFileLink" class="nav" href="z_145eef247bfb46b6___init___py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
