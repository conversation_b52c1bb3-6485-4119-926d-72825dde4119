"""
SIMPLE CONFIG TEST: Test comma-separated list parsing with Pydantic Settings
This is a MINIMAL working example to solve the crawl_domains issue.
"""

from typing import List, Annotated
from pydantic import Field, BeforeValidator
from pydantic_settings import BaseSettings, SettingsConfigDict


def parse_comma_separated_list(v) -> List[str]:
    """
    CRITICAL FUNCTION: Parse comma-separated string into list.
    This is the EXACT solution for environment variable list parsing.
    """
    print(f"DEBUG: Parsing value: {v} (type: {type(v)})")
    if isinstance(v, str):
        # Split by comma and strip whitespace
        items = [item.strip() for item in v.split(',') if item.strip()]
        print(f"DEBUG: Parsed items: {items}")
        return items
    elif isinstance(v, list):
        print(f"DEBUG: Already a list: {v}")
        return v
    print(f"DEBUG: Returning empty list for: {v}")
    return []


class SimpleSettings(BaseSettings):
    """Simple settings to test comma-separated list parsing."""
    
    model_config = SettingsConfigDict(
        env_file="examples/.env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"
    )
    
    # Test field with comma-separated list
    crawl_domains: Annotated[List[str], BeforeValidator(parse_comma_separated_list)] = Field(
        default=["default1.com", "default2.com"],
        description="Domains to crawl (comma-separated)"
    )
    
    # Simple string field for comparison
    test_string: str = Field(default="test", description="Simple string field")


def test_simple_config():
    """Test the simple configuration."""
    try:
        print("🔧 Testing simple configuration...")
        settings = SimpleSettings()
        
        print("✅ Configuration loaded successfully!")
        print(f"   crawl_domains: {settings.crawl_domains}")
        print(f"   test_string: {settings.test_string}")
        
        return True
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_simple_config()
    if success:
        print("🎉 Simple configuration test PASSED!")
    else:
        print("💥 Simple configuration test FAILED!")
