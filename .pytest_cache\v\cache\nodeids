["tests/integration/test_mcp_tools_integration.py::TestMCPToolsIntegration::test_analyze_eligibility_integration", "tests/integration/test_mcp_tools_integration.py::TestMCPToolsIntegration::test_bulgarian_language_processing", "tests/integration/test_mcp_tools_integration.py::TestMCPToolsIntegration::test_concurrent_tool_usage", "tests/integration/test_mcp_tools_integration.py::TestMCPToolsIntegration::test_cross_tool_consistency", "tests/integration/test_mcp_tools_integration.py::TestMCPToolsIntegration::test_data_quality_validation", "tests/integration/test_mcp_tools_integration.py::TestMCPToolsIntegration::test_error_handling_integration", "tests/integration/test_mcp_tools_integration.py::TestMCPToolsIntegration::test_get_application_deadlines_integration", "tests/integration/test_mcp_tools_integration.py::TestMCPToolsIntegration::test_get_funding_programs_integration", "tests/integration/test_mcp_tools_integration.py::TestMCPToolsIntegration::test_mcp_tools_initialization", "tests/integration/test_mcp_tools_integration.py::TestMCPToolsIntegration::test_response_time_requirements", "tests/integration/test_mcp_tools_integration.py::TestMCPToolsIntegration::test_search_eu_funds_english_query", "tests/integration/test_mcp_tools_integration.py::TestMCPToolsIntegration::test_search_eu_funds_real_data", "tests/integration/test_mcp_tools_integration.py::TestMCPToolsIntegration::test_tool_performance_metrics", "tests/unit/test_embeddings.py::TestEmbeddingProcessor::test_bulgarian_optimization", "tests/unit/test_embeddings.py::TestEmbeddingProcessor::test_cache_size_limit", "tests/unit/test_embeddings.py::TestEmbeddingProcessor::test_caching_functionality", "tests/unit/test_embeddings.py::TestEmbeddingProcessor::test_embed_batch_basic", "tests/unit/test_embeddings.py::TestEmbeddingProcessor::test_embed_batch_empty_list", "tests/unit/test_embeddings.py::TestEmbeddingProcessor::test_embed_batch_mixed_languages", "tests/unit/test_embeddings.py::TestEmbeddingProcessor::test_embed_batch_with_errors", "tests/unit/test_embeddings.py::TestEmbeddingProcessor::test_embed_text_bulgarian", "tests/unit/test_embeddings.py::TestEmbeddingProcessor::test_embed_text_empty", "tests/unit/test_embeddings.py::TestEmbeddingProcessor::test_embed_text_english", "tests/unit/test_embeddings.py::TestEmbeddingProcessor::test_embed_text_very_long", "tests/unit/test_embeddings.py::TestEmbeddingProcessor::test_error_handling", "tests/unit/test_embeddings.py::TestEmbeddingProcessor::test_funding_terms_recognition", "tests/unit/test_embeddings.py::TestEmbeddingProcessor::test_initialization", "tests/unit/test_embeddings.py::TestEmbeddingProcessor::test_model_info", "tests/unit/test_embeddings.py::TestEmbeddingProcessor::test_preprocessing_consistency", "tests/unit/test_embeddings.py::TestEmbeddingProcessor::test_similarity_calculation", "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_bulgarian_specific_processing", "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_calculate_quality_score_high", "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_calculate_quality_score_low", "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_chunk_creation", "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_clean_text_basic", "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_clean_text_html", "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_clean_text_special_chars", "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_detect_language_bulgarian", "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_detect_language_english", "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_detect_language_mixed", "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_funding_info_extraction", "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_funding_terms_detection", "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_initialization", "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_language_analysis_bulgarian", "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_language_analysis_english", "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_normalize_cyrillic", "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_process_text_complete", "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_process_text_empty", "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_process_text_html_content", "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_quality_score_calculation_high", "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_quality_score_calculation_low", "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_remove_stopwords_bulgarian", "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_remove_stopwords_english", "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_semantic_chunking_basic", "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_semantic_chunking_headers", "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_semantic_chunking_max_length", "tests/unit/test_vector_store.py::TestVectorStore::test_batch_operations", "tests/unit/test_vector_store.py::TestVectorStore::test_concurrent_operations", "tests/unit/test_vector_store.py::TestVectorStore::test_delete_content", "tests/unit/test_vector_store.py::TestVectorStore::test_error_handling_connection", "tests/unit/test_vector_store.py::TestVectorStore::test_error_handling_invalid_vector", "tests/unit/test_vector_store.py::TestVectorStore::test_get_content_by_id", "tests/unit/test_vector_store.py::TestVectorStore::test_get_statistics", "tests/unit/test_vector_store.py::TestVectorStore::test_hybrid_search", "tests/unit/test_vector_store.py::TestVectorStore::test_initialization", "tests/unit/test_vector_store.py::TestVectorStore::test_search_performance", "tests/unit/test_vector_store.py::TestVectorStore::test_store_content_basic", "tests/unit/test_vector_store.py::TestVectorStore::test_store_content_with_chunks", "tests/unit/test_vector_store.py::TestVectorStore::test_text_search_basic", "tests/unit/test_vector_store.py::TestVectorStore::test_update_content", "tests/unit/test_vector_store.py::TestVectorStore::test_vector_search_basic", "tests/unit/test_vector_store.py::TestVectorStore::test_vector_search_with_filters"]