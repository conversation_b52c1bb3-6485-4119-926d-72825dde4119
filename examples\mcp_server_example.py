"""
WORKING EXAMPLE: MCP Server with FastAPI
This example shows the EXACT pattern for creating an MCP server that works with <PERSON> and other AI assistants.
CRITICAL: This pattern ensures proper MCP protocol compliance.
"""

import asyncio
import json
from typing import Dict, List, Any, Optional
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
import uvicorn
from datetime import datetime
import logging


# MCP Protocol Models
class MCPRequest(BaseModel):
    """MCP request model following the protocol specification."""
    method: str = Field(..., description="MCP method name")
    params: Optional[Dict[str, Any]] = Field(default=None, description="Method parameters")
    id: Optional[str] = Field(default=None, description="Request ID")


class MCPResponse(BaseModel):
    """MCP response model following the protocol specification."""
    result: Optional[Dict[str, Any]] = Field(default=None, description="Method result")
    error: Optional[Dict[str, Any]] = Field(default=None, description="Error information")
    id: Optional[str] = Field(default=None, description="Request ID")


class MCPTool(BaseModel):
    """MCP tool definition model."""
    name: str = Field(..., description="Tool name")
    description: str = Field(..., description="Tool description")
    inputSchema: Dict[str, Any] = Field(..., description="JSON schema for tool input")


class EUFundsQuery(BaseModel):
    """EU Funds search query model."""
    query: str = Field(..., description="Search query in Bulgarian or English")
    max_results: int = Field(default=10, description="Maximum number of results")
    include_metadata: bool = Field(default=True, description="Include document metadata")


class EUFundsResult(BaseModel):
    """EU Funds search result model."""
    title: str = Field(..., description="Document title")
    content: str = Field(..., description="Document content excerpt")
    url: str = Field(..., description="Source URL")
    score: float = Field(..., description="Relevance score")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="Additional metadata")


# FastAPI App Setup
app = FastAPI(
    title="EU Funds MCP Server",
    description="Model Context Protocol server for Bulgarian EU funds information",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS middleware for MCP clients
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure based on your security requirements
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global state (in production, use proper dependency injection)
server_state = {
    "initialized": False,
    "tools": [],
    "start_time": datetime.now(),
    "request_count": 0
}


# MCP Protocol Endpoints

@app.post("/mcp/initialize")
async def initialize_mcp(request: MCPRequest) -> MCPResponse:
    """
    Initialize MCP connection.
    This is the EXACT pattern for MCP initialization.
    """
    try:
        # Define available tools
        tools = [
            MCPTool(
                name="search_eu_funds",
                description="Search Bulgarian EU funds database for programs, calls, and opportunities",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "Search query in Bulgarian or English"
                        },
                        "max_results": {
                            "type": "integer",
                            "description": "Maximum number of results (default: 10)",
                            "default": 10
                        },
                        "include_metadata": {
                            "type": "boolean",
                            "description": "Include document metadata (default: true)",
                            "default": True
                        }
                    },
                    "required": ["query"]
                }
            ),
            MCPTool(
                name="get_fund_details",
                description="Get detailed information about a specific EU fund or program",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "fund_id": {
                            "type": "string",
                            "description": "EU fund or program identifier"
                        }
                    },
                    "required": ["fund_id"]
                }
            )
        ]
        
        server_state["tools"] = tools
        server_state["initialized"] = True
        
        return MCPResponse(
            result={
                "protocolVersion": "1.0.0",
                "serverInfo": {
                    "name": "eu-funds-mcp-server",
                    "version": "1.0.0",
                    "description": "Bulgarian EU Funds Information Server"
                },
                "capabilities": {
                    "tools": {
                        "listChanged": True
                    }
                }
            },
            id=request.id
        )
    
    except Exception as e:
        logging.error(f"MCP initialization error: {e}")
        return MCPResponse(
            error={
                "code": -32603,
                "message": "Internal error during initialization",
                "data": str(e)
            },
            id=request.id
        )


@app.post("/mcp/tools/list")
async def list_tools(request: MCPRequest) -> MCPResponse:
    """
    List available MCP tools.
    This is the EXACT pattern for tool listing.
    """
    try:
        if not server_state["initialized"]:
            raise HTTPException(status_code=400, detail="Server not initialized")
        
        tools_data = [tool.dict() for tool in server_state["tools"]]
        
        return MCPResponse(
            result={"tools": tools_data},
            id=request.id
        )
    
    except Exception as e:
        logging.error(f"Tools list error: {e}")
        return MCPResponse(
            error={
                "code": -32603,
                "message": "Error listing tools",
                "data": str(e)
            },
            id=request.id
        )


@app.post("/mcp/tools/call")
async def call_tool(request: MCPRequest) -> MCPResponse:
    """
    Execute MCP tool.
    This is the EXACT pattern for tool execution.
    """
    try:
        if not server_state["initialized"]:
            raise HTTPException(status_code=400, detail="Server not initialized")
        
        server_state["request_count"] += 1
        
        tool_name = request.params.get("name") if request.params else None
        tool_arguments = request.params.get("arguments", {}) if request.params else {}
        
        if not tool_name:
            raise ValueError("Tool name is required")
        
        # Route to appropriate tool handler
        if tool_name == "search_eu_funds":
            result = await handle_search_eu_funds(tool_arguments)
        elif tool_name == "get_fund_details":
            result = await handle_get_fund_details(tool_arguments)
        else:
            raise ValueError(f"Unknown tool: {tool_name}")
        
        return MCPResponse(
            result={
                "content": [
                    {
                        "type": "text",
                        "text": json.dumps(result, ensure_ascii=False, indent=2)
                    }
                ]
            },
            id=request.id
        )
    
    except Exception as e:
        logging.error(f"Tool call error: {e}")
        return MCPResponse(
            error={
                "code": -32603,
                "message": f"Error executing tool: {str(e)}",
                "data": str(e)
            },
            id=request.id
        )


# Tool Handlers

async def handle_search_eu_funds(arguments: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle EU funds search.
    This is the EXACT pattern for implementing RAG search.
    """
    query = arguments.get("query", "")
    max_results = arguments.get("max_results", 10)
    include_metadata = arguments.get("include_metadata", True)
    
    if not query:
        raise ValueError("Search query is required")
    
    # TODO: Implement actual RAG search here
    # This is a mock implementation for the example
    mock_results = [
        EUFundsResult(
            title="Програма за иновации и конкурентоспособност",
            content="Програмата подкрепя иновативни проекти в областта на технологиите...",
            url="https://opic.bg/program-innovation",
            score=0.95,
            metadata={
                "deadline": "2024-12-31",
                "budget": "50000000",
                "eligible_entities": ["SME", "Large enterprises", "Research organizations"]
            } if include_metadata else None
        ),
        EUFundsResult(
            title="Европейски фонд за регионално развитие",
            content="ЕФРР подкрепя проекти за регионално развитие и инфраструктура...",
            url="https://eufunds.bg/erdf-program",
            score=0.87,
            metadata={
                "deadline": "2024-11-15",
                "budget": "120000000",
                "eligible_entities": ["Municipalities", "Public organizations"]
            } if include_metadata else None
        )
    ]
    
    # Filter by max_results
    results = mock_results[:max_results]
    
    return {
        "query": query,
        "total_results": len(results),
        "results": [result.dict() for result in results],
        "search_time_ms": 150  # Mock search time
    }


async def handle_get_fund_details(arguments: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle fund details retrieval.
    This is the EXACT pattern for detailed information retrieval.
    """
    fund_id = arguments.get("fund_id", "")
    
    if not fund_id:
        raise ValueError("Fund ID is required")
    
    # TODO: Implement actual fund details retrieval
    # This is a mock implementation for the example
    mock_details = {
        "id": fund_id,
        "title": "Програма за иновации и конкурентоспособност 2021-2027",
        "description": "Програмата цели подкрепа на иновативни проекти...",
        "budget_total": "500000000",
        "budget_available": "250000000",
        "deadline": "2024-12-31",
        "eligible_entities": [
            "Малки и средни предприятия",
            "Големи предприятия",
            "Научни организации",
            "Университети"
        ],
        "application_process": {
            "steps": [
                "Подготовка на проектно предложение",
                "Подаване на документи",
                "Оценка и селекция",
                "Сключване на договор"
            ],
            "required_documents": [
                "Проектно предложение",
                "Бюджет",
                "Удостоверения за правоспособност"
            ]
        },
        "contact_info": {
            "organization": "ОПИК",
            "email": "<EMAIL>",
            "phone": "+359 2 8100 100"
        }
    }
    
    return mock_details


# Health and Status Endpoints

@app.get("/health")
async def health_check():
    """
    Health check endpoint.
    This is the EXACT pattern for health monitoring.
    """
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "uptime_seconds": (datetime.now() - server_state["start_time"]).total_seconds(),
        "initialized": server_state["initialized"],
        "request_count": server_state["request_count"]
    }


@app.get("/")
async def root():
    """Root endpoint with server information."""
    return {
        "name": "EU Funds MCP Server",
        "version": "1.0.0",
        "description": "Model Context Protocol server for Bulgarian EU funds information",
        "endpoints": {
            "health": "/health",
            "docs": "/docs",
            "mcp_initialize": "/mcp/initialize",
            "mcp_tools_list": "/mcp/tools/list",
            "mcp_tools_call": "/mcp/tools/call"
        }
    }


# Server startup
def start_server(host: str = "localhost", port: int = 8000, workers: int = 1):
    """
    Start the MCP server.
    This is the EXACT function to use for server startup.
    """
    uvicorn.run(
        "mcp_server_example:app",
        host=host,
        port=port,
        workers=workers,
        reload=False,  # Set to True for development
        log_level="info"
    )


if __name__ == "__main__":
    # Start the server
    start_server()
