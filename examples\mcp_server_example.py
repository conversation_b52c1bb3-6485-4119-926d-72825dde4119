"""
EU Funds MCP Server - Complete Implementation Example

This example demonstrates the correct structure for an MCP server with Bulgarian language support
and enterprise-grade patterns. Based on mcp-crawl4ai-rag and 2025 best practices.
"""

import asyncio
import logging
from typing import List, Optional, Dict, Any
from contextlib import asynccontextmanager

from fastapi import FastAPI
from pydantic import BaseModel, Field
import mcp
from mcp.server.fastmcp import FastMCP

from config_example import Settings
from database_example import DatabaseManager
from embedding_example import EmbeddingProcessor
from search_example import HybridSearchEngine

# Configure structured logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Pydantic models for tool schemas
class SearchRequest(BaseModel):
    """Search request for EU funding information."""
    query: str = Field(..., description="Search query in Bulgarian or English")
    source_filter: Optional[str] = Field(None, description="Filter by specific source (e.g., 'ec.europa.eu')")
    limit: int = Field(10, description="Maximum number of results to return")

class SearchResult(BaseModel):
    """Search result with relevance scoring."""
    title: str = Field(..., description="Document title")
    content: str = Field(..., description="Relevant content excerpt")
    source: str = Field(..., description="Source URL or document identifier")
    relevance_score: float = Field(..., description="Relevance score (0.0 to 1.0)")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")

class FundingProgram(BaseModel):
    """EU funding program information."""
    name: str = Field(..., description="Program name in Bulgarian and English")
    description: str = Field(..., description="Program description")
    eligibility: List[str] = Field(..., description="Eligibility criteria")
    deadline: Optional[str] = Field(None, description="Application deadline")
    budget: Optional[str] = Field(None, description="Available budget")
    contact: Optional[str] = Field(None, description="Contact information")

# Global instances
settings = Settings()
db_manager: Optional[DatabaseManager] = None
embedding_processor: Optional[EmbeddingProcessor] = None
search_engine: Optional[HybridSearchEngine] = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management with proper resource initialization."""
    global db_manager, embedding_processor, search_engine
    
    logger.info("Initializing EU Funds MCP Server...")
    
    try:
        # Initialize database connection
        db_manager = DatabaseManager(settings)
        await db_manager.initialize()
        logger.info("Database connection established")
        
        # Initialize embedding processor
        embedding_processor = EmbeddingProcessor(settings)
        await embedding_processor.initialize()
        logger.info("Embedding processor initialized")
        
        # Initialize search engine
        search_engine = HybridSearchEngine(db_manager, embedding_processor, settings)
        await search_engine.initialize()
        logger.info("Search engine initialized")
        
        logger.info("EU Funds MCP Server initialization complete")
        yield
        
    except Exception as e:
        logger.error(f"Failed to initialize server: {e}")
        raise
    finally:
        # Cleanup resources
        if search_engine:
            await search_engine.cleanup()
        if embedding_processor:
            await embedding_processor.cleanup()
        if db_manager:
            await db_manager.cleanup()
        logger.info("Server cleanup complete")

# Create FastMCP app
app = FastMCP("EU Funds MCP Server", lifespan=lifespan)

@app.tool()
async def search_eu_funds(request: SearchRequest) -> List[SearchResult]:
    """
    Search for EU funding information with Bulgarian language support.
    
    Example queries:
    - "програми за финансиране на МСП" (SME funding programs)
    - "Horizon Europe Bulgaria eligibility"
    - "структурни фондове 2021-2027"
    """
    try:
        logger.info(f"Searching EU funds: {request.query}")
        
        if not search_engine:
            raise RuntimeError("Search engine not initialized")
        
        # Perform hybrid search with Bulgarian optimization
        results = await search_engine.search(
            query=request.query,
            source_filter=request.source_filter,
            limit=request.limit
        )
        
        # Convert to response format
        search_results = [
            SearchResult(
                title=result["title"],
                content=result["content"],
                source=result["source"],
                relevance_score=result["score"],
                metadata=result.get("metadata", {})
            )
            for result in results
        ]
        
        logger.info(f"Found {len(search_results)} results for query: {request.query}")
        return search_results
        
    except Exception as e:
        logger.error(f"Search failed: {e}")
        raise RuntimeError(f"Search operation failed: {str(e)}")

@app.tool()
async def get_funding_programs(
    category: Optional[str] = None,
    deadline_after: Optional[str] = None
) -> List[FundingProgram]:
    """
    Get available EU funding programs with optional filtering.
    
    Args:
        category: Program category (e.g., "research", "agriculture", "digital")
        deadline_after: ISO date string to filter programs with deadlines after this date
    
    Example usage:
    - get_funding_programs(category="research")
    - get_funding_programs(deadline_after="2025-03-01")
    """
    try:
        logger.info(f"Getting funding programs: category={category}, deadline_after={deadline_after}")
        
        if not db_manager:
            raise RuntimeError("Database manager not initialized")
        
        # Query funding programs from database
        programs = await db_manager.get_funding_programs(
            category=category,
            deadline_after=deadline_after
        )
        
        # Convert to response format
        funding_programs = [
            FundingProgram(
                name=program["name"],
                description=program["description"],
                eligibility=program["eligibility"],
                deadline=program.get("deadline"),
                budget=program.get("budget"),
                contact=program.get("contact")
            )
            for program in programs
        ]
        
        logger.info(f"Found {len(funding_programs)} funding programs")
        return funding_programs
        
    except Exception as e:
        logger.error(f"Failed to get funding programs: {e}")
        raise RuntimeError(f"Failed to retrieve funding programs: {str(e)}")

@app.tool()
async def analyze_eligibility(
    program_name: str,
    organization_type: str,
    location: str = "Bulgaria"
) -> Dict[str, Any]:
    """
    Analyze eligibility for a specific EU funding program.
    
    Args:
        program_name: Name of the funding program
        organization_type: Type of organization (e.g., "SME", "university", "NGO")
        location: Location/country (default: "Bulgaria")
    
    Returns:
        Eligibility analysis with recommendations
    """
    try:
        logger.info(f"Analyzing eligibility: {program_name} for {organization_type} in {location}")
        
        if not search_engine:
            raise RuntimeError("Search engine not initialized")
        
        # Search for program-specific eligibility criteria
        eligibility_query = f"{program_name} eligibility criteria {organization_type} {location}"
        results = await search_engine.search(
            query=eligibility_query,
            limit=5
        )
        
        # Analyze eligibility based on search results
        analysis = {
            "program_name": program_name,
            "organization_type": organization_type,
            "location": location,
            "eligible": True,  # This would be determined by actual analysis
            "confidence": 0.85,
            "requirements": [
                "Organization must be registered in Bulgaria",
                "Minimum 2 years of operation",
                "Relevant project experience"
            ],
            "recommendations": [
                "Prepare detailed project proposal",
                "Ensure compliance with state aid rules",
                "Consider partnership opportunities"
            ],
            "supporting_documents": [result["source"] for result in results[:3]]
        }
        
        logger.info(f"Eligibility analysis complete for {program_name}")
        return analysis
        
    except Exception as e:
        logger.error(f"Eligibility analysis failed: {e}")
        raise RuntimeError(f"Eligibility analysis failed: {str(e)}")

@app.tool()
async def get_application_deadlines(
    months_ahead: int = 6
) -> List[Dict[str, Any]]:
    """
    Get upcoming application deadlines for EU funding programs.
    
    Args:
        months_ahead: Number of months to look ahead (default: 6)
    
    Returns:
        List of programs with upcoming deadlines
    """
    try:
        logger.info(f"Getting application deadlines for next {months_ahead} months")
        
        if not db_manager:
            raise RuntimeError("Database manager not initialized")
        
        # Query upcoming deadlines
        deadlines = await db_manager.get_upcoming_deadlines(months_ahead)
        
        logger.info(f"Found {len(deadlines)} upcoming deadlines")
        return deadlines
        
    except Exception as e:
        logger.error(f"Failed to get deadlines: {e}")
        raise RuntimeError(f"Failed to retrieve deadlines: {str(e)}")

@app.tool()
async def get_funding_sources() -> List[Dict[str, Any]]:
    """
    Get list of available funding data sources with reliability scores.
    
    Returns:
        List of data sources with metadata
    """
    try:
        logger.info("Getting funding sources")
        
        if not db_manager:
            raise RuntimeError("Database manager not initialized")
        
        sources = await db_manager.get_data_sources()
        
        logger.info(f"Found {len(sources)} data sources")
        return sources
        
    except Exception as e:
        logger.error(f"Failed to get sources: {e}")
        raise RuntimeError(f"Failed to retrieve sources: {str(e)}")

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint for monitoring."""
    try:
        # Check database connection
        if db_manager:
            await db_manager.health_check()
        
        # Check embedding processor
        if embedding_processor:
            await embedding_processor.health_check()
        
        return {
            "status": "healthy",
            "timestamp": asyncio.get_event_loop().time(),
            "components": {
                "database": "healthy" if db_manager else "not_initialized",
                "embeddings": "healthy" if embedding_processor else "not_initialized",
                "search": "healthy" if search_engine else "not_initialized"
            }
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": asyncio.get_event_loop().time()
        }

if __name__ == "__main__":
    import uvicorn
    
    # Run the server
    uvicorn.run(
        app,
        host=settings.host,
        port=settings.port,
        log_level="info"
    )
