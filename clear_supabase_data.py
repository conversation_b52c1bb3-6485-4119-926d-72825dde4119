#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to clear all crawled data from Supabase database.
This will delete all records from eu_funds_content table.
"""

import asyncio
import os
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def clear_all_data():
    """Clear all data from eu_funds_content table"""
    
    # Initialize Supabase client
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_SERVICE_KEY")
    
    if not supabase_url or not supabase_key:
        print("❌ Missing Supabase credentials in environment variables")
        return False
    
    supabase: Client = create_client(supabase_url, supabase_key)
    
    try:
        # Get count of existing records
        count_response = supabase.table("eu_funds_content").select("id", count="exact").execute()
        record_count = count_response.count
        
        print(f"📊 Found {record_count} records in eu_funds_content table")
        
        if record_count == 0:
            print("✅ Table is already empty")
            return True
        
        # Delete all records without confirmation for automated clearing
        print("🗑️  Deleting all records...")
        # Get all IDs first, then delete them
        all_records = supabase.table("eu_funds_content").select("id").execute()

        if all_records.data:
            for record in all_records.data:
                supabase.table("eu_funds_content").delete().eq("id", record["id"]).execute()
        else:
            print("ℹ️  No records found to delete")
        
        # Verify deletion
        final_count_response = supabase.table("eu_funds_content").select("id", count="exact").execute()
        final_count = final_count_response.count
        
        if final_count == 0:
            print(f"✅ Successfully deleted all {record_count} records")
            print("🧹 Database is now clean and ready for new crawling")
            return True
        else:
            print(f"⚠️  Warning: {final_count} records still remain")
            return False
            
    except Exception as e:
        print(f"❌ Error clearing data: {str(e)}")
        return False

if __name__ == "__main__":
    success = asyncio.run(clear_all_data())
    if success:
        print("\n🎯 Ready to proceed with improved crawling!")
    else:
        print("\n❌ Data clearing failed. Please check the errors above.")
