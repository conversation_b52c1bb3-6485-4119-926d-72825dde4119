#!/usr/bin/env python3
"""
Real-world test with actual Supabase data to validate RAG functionality.
Tests the system with realistic questions and measures accuracy.
"""

import asyncio
import sys
import os
from datetime import datetime
from typing import List, Dict, Any, Tuple
import json

sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.core.hybrid_search import HybridSearchEngine
from src.core.vector_store import VectorStore

class RealWorldTester:
    """Real-world tester for RAG system with actual Supabase data."""
    
    def __init__(self):
        self.search_engine = HybridSearchEngine()
        self.vector_store = VectorStore()
        
        # Real-world test questions based on actual EU funding scenarios
        self.test_questions = [
            {
                "question": "Какво финансиране има за малки и средни предприятия?",
                "category": "funding",
                "expected_keywords": ["МСП", "малки", "средни", "предприятия", "финансиране", "подкрепа"],
                "difficulty": "easy"
            },
            {
                "question": "Какви са условията за кандидатстване по ОПИК?",
                "category": "conditions",
                "expected_keywords": ["ОПИК", "условия", "кандидатстване", "критерии", "изисквания"],
                "difficulty": "medium"
            },
            {
                "question": "Колко е максималният размер на проекта за иновации?",
                "category": "funding_amounts",
                "expected_keywords": ["максимален", "размер", "проект", "иновации", "сума", "лева"],
                "difficulty": "hard"
            },
            {
                "question": "Кои програми финансират научни изследвания?",
                "category": "programs",
                "expected_keywords": ["програми", "научни", "изследвания", "ОПНОИР", "наука"],
                "difficulty": "medium"
            },
            {
                "question": "Как да кандидатствам за европейско финансиране?",
                "category": "procedures",
                "expected_keywords": ["кандидатстване", "процедура", "документи", "заявление", "стъпки"],
                "difficulty": "easy"
            },
            {
                "question": "Какви са сроковете за изпълнение на проектите?",
                "category": "timelines",
                "expected_keywords": ["срокове", "изпълнение", "проекти", "месеца", "години"],
                "difficulty": "medium"
            },
            {
                "question": "Има ли финансиране за дигитализация на МСП?",
                "category": "specific_funding",
                "expected_keywords": ["дигитализация", "МСП", "технологии", "цифрови", "трансформация"],
                "difficulty": "hard"
            },
            {
                "question": "Какви документи са нужни за кандидатстване?",
                "category": "documentation",
                "expected_keywords": ["документи", "кандидатстване", "заявление", "приложения", "удостоверения"],
                "difficulty": "easy"
            },
            {
                "question": "Кой може да кандидатства по програмите за регионално развитие?",
                "category": "eligibility",
                "expected_keywords": ["кандидатства", "регионално", "развитие", "общини", "организации"],
                "difficulty": "medium"
            },
            {
                "question": "Какъв е процентът на съфинансиране от ЕС?",
                "category": "cofinancing",
                "expected_keywords": ["процент", "съфинансиране", "ЕС", "национално", "85%", "80%"],
                "difficulty": "hard"
            }
        ]
    
    async def run_comprehensive_test(self) -> Dict[str, Any]:
        """Run comprehensive real-world test."""
        print("🧪 СТАРТИРАНЕ НА РЕАЛЕН ТЕСТ С АКТУАЛНИ ДАННИ")
        print("=" * 60)
        
        start_time = datetime.now()
        
        # Initialize systems
        await self.vector_store.initialize()
        await self.search_engine.initialize()
        
        # Check data availability
        try:
            result = self.vector_store.supabase.table('eu_funds_content').select("id", count="exact").execute()
            data_count = result.count if hasattr(result, 'count') else len(result.data)
        except Exception as e:
            print(f"❌ Грешка при проверка на данните: {e}")
            data_count = 0
        print(f"📊 Налични данни: {data_count} записа")
        
        if data_count < 10:
            print("⚠️ ВНИМАНИЕ: Малко данни за тестване!")
            print("   Препоръчвам да изчакаш crawling-а да завърши")
            return {"error": "insufficient_data", "data_count": data_count}
        
        print(f"✅ Достатъчно данни за тестване")
        print(f"🎯 Тестови въпроси: {len(self.test_questions)}")
        print()
        
        # Run tests
        test_results = []
        total_score = 0
        
        for i, test_case in enumerate(self.test_questions):
            print(f"📝 Тест {i+1}/{len(self.test_questions)}: {test_case['question']}")
            
            try:
                # Get answer from RAG system
                result = await self._test_single_question(test_case)
                test_results.append(result)
                total_score += result["score"]
                
                # Print result
                status = "✅" if result["score"] >= 0.7 else "⚠️" if result["score"] >= 0.5 else "❌"
                print(f"   {status} Резултат: {result['score']:.1%} ({result['score_details']})")
                
            except Exception as e:
                print(f"   ❌ Грешка: {e}")
                test_results.append({
                    "question": test_case["question"],
                    "error": str(e),
                    "score": 0
                })
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # Calculate final metrics
        avg_score = total_score / len(self.test_questions) if self.test_questions else 0
        
        # Generate comprehensive report
        report = {
            "test_summary": {
                "total_questions": len(self.test_questions),
                "average_score": avg_score,
                "duration_seconds": duration,
                "data_count": data_count,
                "timestamp": start_time.isoformat()
            },
            "detailed_results": test_results,
            "performance_analysis": self._analyze_performance(test_results),
            "recommendations": self._generate_test_recommendations(avg_score, test_results)
        }
        
        return report
    
    async def _test_single_question(self, test_case: Dict) -> Dict[str, Any]:
        """Test a single question and evaluate the answer."""
        question = test_case["question"]
        
        # Get answer from RAG system
        search_results = await self.search_engine.search(
            query=question,
            limit=5
        )
        
        if not search_results or not search_results.results:
            return {
                "question": question,
                "category": test_case["category"],
                "answer": "Няма намерени резултати",
                "score": 0,
                "score_details": "no_results",
                "similarity_scores": [],
                "response_time": 0
            }
        
        # Evaluate answer quality
        score_details = self._evaluate_answer_quality(search_results, test_case)
        
        return {
            "question": question,
            "category": test_case["category"],
            "answer": search_results.results[0].content[:200] + "..." if search_results.results else "",
            "score": score_details["total_score"],
            "score_details": score_details["breakdown"],
            "similarity_scores": [r.similarity_score for r in search_results.results[:3]],
            "response_time": search_results.response_time,
            "num_results": len(search_results.results)
        }
    
    def _evaluate_answer_quality(self, search_results, test_case: Dict) -> Dict[str, Any]:
        """Evaluate the quality of the answer."""
        scores = {
            "relevance": 0,
            "keyword_match": 0,
            "similarity": 0,
            "completeness": 0
        }
        
        if not search_results.results:
            return {"total_score": 0, "breakdown": "no_results"}
        
        top_result = search_results.results[0]
        all_content = " ".join([r.content.lower() for r in search_results.results[:3]])
        
        # 1. Similarity score (30%)
        scores["similarity"] = min(top_result.similarity_score, 1.0) * 0.3
        
        # 2. Keyword matching (40%)
        expected_keywords = test_case.get("expected_keywords", [])
        if expected_keywords:
            matched_keywords = sum(1 for keyword in expected_keywords if keyword.lower() in all_content)
            scores["keyword_match"] = (matched_keywords / len(expected_keywords)) * 0.4
        
        # 3. Relevance to category (20%)
        category_relevance = self._check_category_relevance(all_content, test_case["category"])
        scores["relevance"] = category_relevance * 0.2
        
        # 4. Completeness (10%)
        content_length = len(all_content)
        scores["completeness"] = min(content_length / 500, 1.0) * 0.1
        
        total_score = sum(scores.values())
        
        return {
            "total_score": total_score,
            "breakdown": f"sim:{scores['similarity']:.2f} kw:{scores['keyword_match']:.2f} rel:{scores['relevance']:.2f} comp:{scores['completeness']:.2f}"
        }
    
    def _check_category_relevance(self, content: str, category: str) -> float:
        """Check relevance to specific category."""
        category_keywords = {
            "funding": ["финансиране", "подкрепа", "средства", "помощ", "грант"],
            "conditions": ["условия", "критерии", "изисквания", "правила"],
            "programs": ["програма", "програми", "ОПИК", "ОПРР", "ОПНОИР"],
            "procedures": ["процедура", "кандидатстване", "заявление", "стъпки"],
            "amounts": ["сума", "размер", "лева", "евро", "максимален", "минимален"],
            "eligibility": ["може", "кандидатства", "право", "организации"],
            "documentation": ["документи", "заявление", "приложения"],
            "timelines": ["срок", "време", "месец", "година"],
            "cofinancing": ["съфинансиране", "процент", "национално"]
        }
        
        relevant_keywords = category_keywords.get(category, [])
        if not relevant_keywords:
            return 0.5  # Default relevance
        
        matches = sum(1 for keyword in relevant_keywords if keyword in content)
        return min(matches / len(relevant_keywords), 1.0)
    
    def _analyze_performance(self, results: List[Dict]) -> Dict[str, Any]:
        """Analyze overall performance patterns."""
        if not results:
            return {}
        
        # Group by category
        by_category = {}
        for result in results:
            category = result.get("category", "unknown")
            if category not in by_category:
                by_category[category] = []
            by_category[category].append(result["score"])
        
        # Calculate category averages
        category_performance = {
            cat: sum(scores) / len(scores) for cat, scores in by_category.items()
        }
        
        # Response time analysis
        response_times = [r.get("response_time", 0) for r in results if r.get("response_time")]
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0
        
        return {
            "category_performance": category_performance,
            "average_response_time": avg_response_time,
            "best_category": max(category_performance.items(), key=lambda x: x[1]) if category_performance else None,
            "worst_category": min(category_performance.items(), key=lambda x: x[1]) if category_performance else None
        }
    
    def _generate_test_recommendations(self, avg_score: float, results: List[Dict]) -> List[str]:
        """Generate recommendations based on test results."""
        recommendations = []
        
        if avg_score >= 0.85:
            recommendations.append("🎉 Отлична производителност! Системата е готова за production")
        elif avg_score >= 0.70:
            recommendations.append("✅ Добра производителност, подходяща за реално използване")
        elif avg_score >= 0.50:
            recommendations.append("⚠️ Средна производителност, нужни подобрения")
        else:
            recommendations.append("❌ Ниска производителност, нужни значителни подобрения")
        
        # Analyze specific issues
        low_scores = [r for r in results if r.get("score", 0) < 0.5]
        if len(low_scores) > len(results) * 0.3:
            recommendations.append("Много въпроси с ниски резултати - проверете качеството на данните")
        
        no_results = [r for r in results if "no_results" in str(r.get("score_details", ""))]
        if no_results:
            recommendations.append(f"{len(no_results)} въпроса без резултати - нужно повече данни")
        
        return recommendations
    
    def print_test_report(self, report: Dict[str, Any]):
        """Print formatted test report."""
        if "error" in report:
            print(f"❌ ТЕСТ НЕУСПЕШЕН: {report['error']}")
            return
        
        summary = report["test_summary"]
        
        print(f"\n🎯 ФИНАЛЕН РЕЗУЛТАТ")
        print("=" * 40)
        print(f"📊 Обща точност: {summary['average_score']:.1%}")
        print(f"📝 Тестови въпроси: {summary['total_questions']}")
        print(f"⏱️ Средно време: {summary.get('duration_seconds', 0) / summary['total_questions']:.1f}s на въпрос")
        print(f"💾 Използвани данни: {summary['data_count']} записа")
        
        # Performance by category
        if "performance_analysis" in report:
            analysis = report["performance_analysis"]
            if "category_performance" in analysis:
                print(f"\n📋 РЕЗУЛТАТИ ПО КАТЕГОРИИ:")
                for category, score in analysis["category_performance"].items():
                    status = "✅" if score >= 0.7 else "⚠️" if score >= 0.5 else "❌"
                    print(f"   {status} {category}: {score:.1%}")
        
        # Recommendations
        if report.get("recommendations"):
            print(f"\n💡 ПРЕПОРЪКИ:")
            for rec in report["recommendations"]:
                print(f"   • {rec}")

async def main():
    """Main function."""
    tester = RealWorldTester()
    report = await tester.run_comprehensive_test()
    tester.print_test_report(report)
    
    # Save detailed report
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    with open(f"real_world_test_report_{timestamp}.json", "w", encoding="utf-8") as f:
        json.dump(report, f, ensure_ascii=False, indent=2, default=str)
    
    return report

if __name__ == "__main__":
    asyncio.run(main())
