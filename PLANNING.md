# PLANNING.md - EU Funds MCP Server Architecture & Planning

## 🎯 Project Vision
Democratize access to European funding programs information for Bulgaria through an enterprise-grade RAG system that serves as a "tool for tools" for other LLMs.

## 🏗️ Core Architecture

### Technology Stack
- **MCP Server**: FastAPI-based Model Context Protocol server
- **RAG System**: Hybrid search with pgvector + Bulgarian FTS  
- **Crawler**: Crawl4AI v0.6.0 with browser pooling
- **Database**: Supabase with pgvector extension
- **Embeddings**: multilingual-e5-large-instruct (1024 dimensions)
- **Monitoring**: Prometheus + Grafana + structured logging

### Key Components
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   MCP Client    │───▶│   MCP Server    │───▶│   RAG Engine    │
│   (Claude/etc)  │    │   (FastAPI)     │    │   (Hybrid)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                       │
                                ▼                       ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │    Crawler      │    │    Database     │
                       │   (Crawl4AI)    │    │   (Supabase)    │
                       └─────────────────┘    └─────────────────┘
```

## 📊 Development Phases

### Phase 1: Foundation (Week 1-2) - 72 minutes total
**20 стъпки от EU_FUNDS_MCP_PRP.md**
- Project structure setup
- Core modules (config, logging, monitoring)
- Database schema and models
- Basic MCP server framework

### Phase 2: Core RAG Implementation (Week 3-4)
- Embedding system with multilingual-e5-large-instruct
- Hybrid search (semantic + keyword)
- Vector operations with pgvector HNSW
- Query processing for Bulgarian language

### Phase 3: Advanced Features (Week 5-6)
- Crawl4AI integration with browser pooling
- Content processing with GPT-4o-mini
- Change detection and scheduling
- Performance optimization

### Phase 4: Production Deployment (Week 7-8)
- Docker multi-stage builds
- Kubernetes manifests
- Monitoring and alerting
- Security hardening

## 🇧🇬 Bulgarian-Specific Requirements

### Target Domains
- **eufunds.bg** - Main EU funds portal
- **opic.bg** - Innovation and Competitiveness OP
- **esif.bg** - European Structural and Investment Funds

### Language Processing
- Bulgarian full-text search with PostgreSQL
- Hybrid search weights: CE: 0.3, Hybrid: 0.7
- Query expansion for Bulgarian terms
- Content filtering for EU funds terminology

### Data Structure
- Document types: programs, calls, guidelines, news, regulations
- Metadata: deadlines, budgets, eligible entities
- Structured extraction from Bulgarian government sites

## 🔧 Implementation Status

### Current Environment ✅
- Workspace: `C:\Users\<USER>\Desktop\nov opit`
- Supabase project: `mcp-crawl4ai-rag` (jbdpiowmhaxghnzvhxse)
- Production API keys configured
- MCP servers installed (filesystem, memory, brave-search)

### Next Immediate Actions
1. **Execute СТЪПКА 1-9** from EU_FUNDS_MCP_PRP.md
2. **Fix Pydantic configuration issue** 
3. **Complete Phase 1 foundation**
4. **Validate with real Bulgarian EU funds data**

## 🎯 Success Metrics
- RAG accuracy > 90% for Bulgarian EU funds queries
- Response time < 2 seconds for hybrid search
- 99.9% uptime for MCP server
- Support for 1000+ concurrent users
