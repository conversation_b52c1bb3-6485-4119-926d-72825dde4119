"""
Structured JSON logging for EU Funds MCP Server.

This module provides structured JSON logging with correlation IDs for request tracing,
log levels and filtering, and integration with monitoring systems.
"""

import json
import sys
import uuid
from datetime import datetime
from typing import Any, Dict, Optional
from contextvars import ContextVar

from loguru import logger
from .config import get_settings

# Context variable for correlation ID
correlation_id: ContextVar[Optional[str]] = ContextVar('correlation_id', default=None)


class StructuredLogger:
    """Structured logger with JSON output and correlation ID support."""
    
    def __init__(self):
        self.settings = get_settings()
        self._setup_logger()
    
    def _setup_logger(self):
        """Setup loguru logger with structured JSON format."""
        # Remove default handler
        logger.remove()
        
        # Simple formatter for now (avoid JSON formatting issues)
        def simple_formatter(record):
            """Format log record as simple text."""
            timestamp = record["time"].strftime("%Y-%m-%d %H:%M:%S")
            level = record["level"].name
            message = record["message"]
            corr_id = correlation_id.get() or "N/A"

            return f"[{timestamp}] {level} | {corr_id} | {message}"
        
        # Add handler with simple format
        logger.add(
            sys.stdout,
            format=simple_formatter,
            level=self.settings.monitoring.log_level,
            serialize=False,
            backtrace=True,
            diagnose=True,
        )

        # Add file handler for production
        if self.settings.environment == "production":
            logger.add(
                "logs/eu-funds-mcp.log",
                format=simple_formatter,
                level=self.settings.monitoring.log_level,
                rotation="100 MB",
                retention="30 days",
                compression="gz",
                serialize=False,
                backtrace=True,
                diagnose=True,
            )
    
    def set_correlation_id(self, corr_id: Optional[str] = None) -> str:
        """Set correlation ID for request tracing."""
        if corr_id is None:
            corr_id = str(uuid.uuid4())
        correlation_id.set(corr_id)
        return corr_id
    
    def get_correlation_id(self) -> Optional[str]:
        """Get current correlation ID."""
        return correlation_id.get()
    
    def clear_correlation_id(self):
        """Clear correlation ID."""
        correlation_id.set(None)
    
    def log_request(self, method: str, path: str, **kwargs):
        """Log HTTP request."""
        logger.info(
            f"HTTP Request: {method} {path}",
            extra={
                "event_type": "http_request",
                "http_method": method,
                "http_path": path,
                **kwargs
            }
        )
    
    def log_response(self, status_code: int, duration_ms: float, **kwargs):
        """Log HTTP response."""
        logger.info(
            f"HTTP Response: {status_code} ({duration_ms:.2f}ms)",
            extra={
                "event_type": "http_response",
                "http_status": status_code,
                "duration_ms": duration_ms,
                **kwargs
            }
        )
    
    def log_database_query(self, query: str, duration_ms: float, **kwargs):
        """Log database query."""
        logger.debug(
            f"Database Query ({duration_ms:.2f}ms)",
            extra={
                "event_type": "database_query",
                "query": query,
                "duration_ms": duration_ms,
                **kwargs
            }
        )
    
    def log_crawler_event(self, event: str, url: str, **kwargs):
        """Log crawler event."""
        logger.info(
            f"Crawler: {event} - {url}",
            extra={
                "event_type": "crawler",
                "crawler_event": event,
                "url": url,
                **kwargs
            }
        )
    
    def log_rag_operation(self, operation: str, query: str, results_count: int, **kwargs):
        """Log RAG operation."""
        logger.info(
            f"RAG {operation}: {results_count} results for query",
            extra={
                "event_type": "rag_operation",
                "rag_operation": operation,
                "query": query,
                "results_count": results_count,
                **kwargs
            }
        )
    
    def log_mcp_tool_call(self, tool_name: str, arguments: Dict[str, Any], **kwargs):
        """Log MCP tool call."""
        logger.info(
            f"MCP Tool Call: {tool_name}",
            extra={
                "event_type": "mcp_tool_call",
                "tool_name": tool_name,
                "arguments": arguments,
                **kwargs
            }
        )
    
    def log_error(self, error: Exception, context: str = "", **kwargs):
        """Log error with context."""
        logger.error(
            f"Error in {context}: {str(error)}",
            extra={
                "event_type": "error",
                "error_type": type(error).__name__,
                "error_message": str(error),
                "context": context,
                **kwargs
            }
        )
    
    def log_performance_metric(self, metric_name: str, value: float, unit: str = "", **kwargs):
        """Log performance metric."""
        logger.info(
            f"Performance Metric: {metric_name} = {value}{unit}",
            extra={
                "event_type": "performance_metric",
                "metric_name": metric_name,
                "metric_value": value,
                "metric_unit": unit,
                **kwargs
            }
        )


# Global logger instance
structured_logger = StructuredLogger()

# Convenience functions
def set_correlation_id(corr_id: Optional[str] = None) -> str:
    """Set correlation ID for request tracing."""
    return structured_logger.set_correlation_id(corr_id)

def get_correlation_id() -> Optional[str]:
    """Get current correlation ID."""
    return structured_logger.get_correlation_id()

def clear_correlation_id():
    """Clear correlation ID."""
    structured_logger.clear_correlation_id()

# Export logger for direct use
__all__ = [
    "structured_logger",
    "set_correlation_id",
    "get_correlation_id", 
    "clear_correlation_id",
    "logger"
]
