"""
Optimized logging system for EU Funds MCP Server.
Memory-efficient structured logging with JSON output.
"""

import json
import sys
import time
from pathlib import Path
from typing import Any, Dict, Optional

from loguru import logger

from .config import get_settings


class JSONFormatter:
    """Custom JSON formatter for structured logging."""
    
    def format(self, record: Dict[str, Any]) -> str:
        """Format log record as JSO<PERSON>."""
        # Extract basic info
        log_entry = {
            "timestamp": record["time"].isoformat(),
            "level": record["level"].name,
            "message": record["message"],
            "module": record["name"],
            "function": record["function"],
            "line": record["line"],
        }
        
        # Add extra fields if present
        if "extra" in record and record["extra"]:
            log_entry.update(record["extra"])
        
        # Add exception info if present
        if record.get("exception"):
            log_entry["exception"] = {
                "type": record["exception"].type.__name__,
                "value": str(record["exception"].value),
                "traceback": record["exception"].traceback
            }
        
        return json.dumps(log_entry, ensure_ascii=False)


def setup_logging() -> None:
    """Setup application logging with memory optimization."""
    settings = get_settings()
    
    # Remove default logger
    logger.remove()
    
    # Configure format based on settings
    if settings.monitoring.log_format == "json":
        formatter = JSONFormatter()
        format_string = formatter.format
    else:
        format_string = (
            "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
            "<level>{message}</level>"
        )
    
    # Console logging
    logger.add(
        sys.stdout,
        format=format_string,
        level=settings.monitoring.log_level,
        colorize=settings.monitoring.log_format != "json",
        backtrace=settings.debug,
        diagnose=settings.debug,
        enqueue=True,  # Thread-safe logging
        catch=True,    # Catch exceptions in logging
    )
    
    # File logging (if specified)
    if settings.monitoring.log_file:
        log_path = Path(settings.monitoring.log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        logger.add(
            log_path,
            format=format_string,
            level=settings.monitoring.log_level,
            rotation="100 MB",  # Rotate at 100MB
            retention="7 days",  # Keep logs for 7 days
            compression="gz",    # Compress old logs
            enqueue=True,
            catch=True,
        )
    
    # Set third-party loggers to WARNING to reduce noise
    import logging
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("httpcore").setLevel(logging.WARNING)
    logging.getLogger("asyncio").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)


def get_logger(name: str) -> Any:
    """Get a logger instance for a module."""
    return logger.bind(module=name)


class LoggerMixin:
    """Mixin class to add logging capabilities to any class."""
    
    @property
    def logger(self) -> Any:
        """Get logger for this class."""
        return get_logger(self.__class__.__name__)


def log_execution_time(func_name: str):
    """Decorator to log function execution time."""
    def decorator(func):
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                execution_time = time.time() - start_time
                logger.info(
                    f"Function {func_name} completed",
                    execution_time=execution_time,
                    function=func_name
                )
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(
                    f"Function {func_name} failed",
                    execution_time=execution_time,
                    function=func_name,
                    error=str(e)
                )
                raise
        
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                logger.info(
                    f"Function {func_name} completed",
                    execution_time=execution_time,
                    function=func_name
                )
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(
                    f"Function {func_name} failed",
                    execution_time=execution_time,
                    function=func_name,
                    error=str(e)
                )
                raise
        
        # Return appropriate wrapper based on function type
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


def log_api_request(request_id: str, method: str, path: str, **kwargs):
    """Log API request with structured data."""
    logger.info(
        f"API Request: {method} {path}",
        request_id=request_id,
        method=method,
        path=path,
        **kwargs
    )


def log_api_response(request_id: str, status_code: int, execution_time: float, **kwargs):
    """Log API response with structured data."""
    logger.info(
        f"API Response: {status_code}",
        request_id=request_id,
        status_code=status_code,
        execution_time=execution_time,
        **kwargs
    )


def log_database_operation(operation: str, table: str, **kwargs):
    """Log database operation with structured data."""
    logger.info(
        f"Database {operation}: {table}",
        operation=operation,
        table=table,
        **kwargs
    )


def log_crawl_operation(url: str, status: str, **kwargs):
    """Log crawling operation with structured data."""
    logger.info(
        f"Crawl {status}: {url}",
        url=url,
        status=status,
        **kwargs
    )


def log_rag_operation(query: str, results_count: int, **kwargs):
    """Log RAG operation with structured data."""
    logger.info(
        f"RAG Query: {query[:100]}...",
        query_preview=query[:100],
        results_count=results_count,
        **kwargs
    )


# Initialize logging on module import
setup_logging()
