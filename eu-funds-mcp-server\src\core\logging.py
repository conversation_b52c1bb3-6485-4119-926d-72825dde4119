"""
Structured logging configuration using Loguru.
Provides JSON logging with correlation IDs and proper formatting.
"""

import sys
import json
import uuid
from typing import Dict, Any, Optional
from contextvars import <PERSON>text<PERSON>ar
from loguru import logger
from .config import get_settings


# Context variable for correlation ID
correlation_id: ContextVar[Optional[str]] = ContextVar('correlation_id', default=None)


def get_correlation_id() -> str:
    """Get or generate correlation ID for request tracing."""
    current_id = correlation_id.get()
    if current_id is None:
        current_id = str(uuid.uuid4())
        correlation_id.set(current_id)
    return current_id


def set_correlation_id(cid: str) -> None:
    """Set correlation ID for request tracing."""
    correlation_id.set(cid)


def json_formatter(record: Dict[str, Any]) -> str:
    """Format log record as JSON with correlation ID."""
    # Extract basic fields
    log_entry = {
        "timestamp": record["time"].isoformat(),
        "level": record["level"].name,
        "logger": record["name"],
        "message": record["message"],
        "module": record["module"],
        "function": record["function"],
        "line": record["line"],
    }

    # Add correlation ID if available
    cid = correlation_id.get()
    if cid:
        log_entry["correlation_id"] = cid

    # Add extra fields from record
    if "extra" in record and record["extra"]:
        log_entry.update(record["extra"])

    # Add exception info if present
    if record["exception"]:
        log_entry["exception"] = {
            "type": record["exception"].type.__name__,
            "value": str(record["exception"].value),
            "traceback": record["exception"].traceback
        }

    return json.dumps(log_entry, ensure_ascii=False, default=str)


def setup_logging() -> None:
    """Setup structured logging configuration."""
    settings = get_settings()

    # Remove default handler
    logger.remove()

    # Configure based on format preference
    if settings.log_format.lower() == "json":
        # JSON format for production
        logger.add(
            sys.stdout,
            format=json_formatter,
            level=settings.log_level.upper(),
            serialize=False,
            backtrace=True,
            diagnose=True,
            enqueue=True,
        )
    else:
        # Human-readable format for development
        format_string = (
            "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
            "<level>{message}</level>"
        )

        logger.add(
            sys.stdout,
            format=format_string,
            level=settings.log_level.upper(),
            colorize=True,
            backtrace=True,
            diagnose=True,
            enqueue=True,
        )

    # Add file logging for errors
    logger.add(
        "logs/error.log",
        format=json_formatter,
        level="ERROR",
        rotation="10 MB",
        retention="30 days",
        compression="gz",
        serialize=False,
        backtrace=True,
        diagnose=True,
        enqueue=True,
    )

    # Add file logging for all messages
    logger.add(
        "logs/app.log",
        format=json_formatter,
        level=settings.log_level.upper(),
        rotation="50 MB",
        retention="7 days",
        compression="gz",
        serialize=False,
        backtrace=True,
        diagnose=True,
        enqueue=True,
    )


def get_logger(name: str) -> Any:
    """Get logger instance with specified name."""
    return logger.bind(logger_name=name)


# Note: Call setup_logging() explicitly when needed
