{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.9.2", "globals": "6bffd01dd258eccb43d2814845b0153e", "files": {"z_145eef247bfb46b6___init___py": {"hash": "d5ecb8faa6afb3e3674e4c6b73d3d945", "index": {"url": "z_145eef247bfb46b6___init___py.html", "file": "src\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ce21df766c911d41___init___py": {"hash": "af2536b900c40d86e45608a1572065a4", "index": {"url": "z_ce21df766c911d41___init___py.html", "file": "src\\core\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ce21df766c911d41_config_py": {"hash": "87e51d7b170da38a3731354935d3bdfa", "index": {"url": "z_ce21df766c911d41_config_py.html", "file": "src\\core\\config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 96, "n_excluded": 0, "n_missing": 14, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ce21df766c911d41_crawler_py": {"hash": "444cb5ac2b6c85a8604558ce3de657c9", "index": {"url": "z_ce21df766c911d41_crawler_py.html", "file": "src\\core\\crawler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 139, "n_excluded": 0, "n_missing": 139, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ce21df766c911d41_database_py": {"hash": "65b2b177923859b5b9ac9aade56eeb5e", "index": {"url": "z_ce21df766c911d41_database_py.html", "file": "src\\core\\database.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 106, "n_excluded": 0, "n_missing": 73, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ce21df766c911d41_embeddings_py": {"hash": "ee744f39e1a809a5f568a6ec882e6db9", "index": {"url": "z_ce21df766c911d41_embeddings_py.html", "file": "src\\core\\embeddings.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 209, "n_excluded": 0, "n_missing": 65, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ce21df766c911d41_hybrid_search_py": {"hash": "7b0435ec58507289dcf5674a52c7dc99", "index": {"url": "z_ce21df766c911d41_hybrid_search_py.html", "file": "src\\core\\hybrid_search.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 150, "n_excluded": 0, "n_missing": 129, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ce21df766c911d41_models_py": {"hash": "c83eaff1bf10dbc2b5412528caa569df", "index": {"url": "z_ce21df766c911d41_models_py.html", "file": "src\\core\\models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 216, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ce21df766c911d41_reranker_py": {"hash": "f7984c5016f4b4875950c1027222bb57", "index": {"url": "z_ce21df766c911d41_reranker_py.html", "file": "src\\core\\reranker.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 128, "n_excluded": 0, "n_missing": 109, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ce21df766c911d41_text_processor_py": {"hash": "cb45533d13ed2545ed5db821f6fca3a5", "index": {"url": "z_ce21df766c911d41_text_processor_py.html", "file": "src\\core\\text_processor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 162, "n_excluded": 0, "n_missing": 34, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ce21df766c911d41_vector_store_py": {"hash": "a0b61796e0740c0f2cc858a8146ea7d2", "index": {"url": "z_ce21df766c911d41_vector_store_py.html", "file": "src\\core\\vector_store.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 219, "n_excluded": 0, "n_missing": 69, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c0a383f9d6d96e73___init___py": {"hash": "b82c367b81e6b67a2379cc76e7e1d146", "index": {"url": "z_c0a383f9d6d96e73___init___py.html", "file": "src\\crawler\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c0a383f9d6d96e73_eu_funds_crawler_py": {"hash": "b34748d9955e54d1e84c3169de54f4ef", "index": {"url": "z_c0a383f9d6d96e73_eu_funds_crawler_py.html", "file": "src\\crawler\\eu_funds_crawler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 146, "n_excluded": 0, "n_missing": 121, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_main_py": {"hash": "d479541880bc58e9cb21ae66a30eb3cd", "index": {"url": "z_145eef247bfb46b6_main_py.html", "file": "src\\main.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 65, "n_excluded": 4, "n_missing": 65, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c58a7740ba00d4cd_tools_py": {"hash": "1d0227f1e8f52a92e73fe53307c38c12", "index": {"url": "z_c58a7740ba00d4cd_tools_py.html", "file": "src\\mcp\\tools.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 159, "n_excluded": 0, "n_missing": 140, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bef636d57f78fa40___init___py": {"hash": "196cb3dc91128cd934fef77aa59cebdd", "index": {"url": "z_bef636d57f78fa40___init___py.html", "file": "src\\tools\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6156a86a215061be___init___py": {"hash": "f52181a4944fb498eddc4f326743f4bd", "index": {"url": "z_6156a86a215061be___init___py.html", "file": "src\\utils\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}