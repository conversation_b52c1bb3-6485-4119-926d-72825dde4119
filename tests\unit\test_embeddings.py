"""
Unit tests for EmbeddingProcessor
Tests embedding generation, caching, and Bulgarian language optimization
"""

import pytest
import numpy as np
from unittest.mock import patch, MagicMock, AsyncMock
from src.core.embeddings import EmbeddingProcessor
from src.core.models import EmbeddingResult

@pytest.mark.unit
class TestEmbeddingProcessor:
    """Test suite for embedding processing functionality."""
    
    @pytest.mark.asyncio
    async def test_initialization(self, embedding_processor):
        """Test embedding processor initialization."""
        assert embedding_processor is not None
        assert hasattr(embedding_processor, 'model')
        assert hasattr(embedding_processor, 'embedding_cache')
    
    @pytest.mark.asyncio
    async def test_embed_text_bulgarian(self, embedding_processor, sample_bulgarian_text):
        """Test embedding generation for Bulgarian text."""
        result = await embedding_processor.embed_text(sample_bulgarian_text)
        
        assert isinstance(result, EmbeddingResult)
        assert result.success
        assert result.vector is not None
        assert len(result.vector) == 384  # Expected dimension
        assert isinstance(result.vector, (list, np.ndarray))
        assert result.model_name is not None
    
    @pytest.mark.asyncio
    async def test_embed_text_english(self, embedding_processor, sample_english_text):
        """Test embedding generation for English text."""
        result = await embedding_processor.embed_text(sample_english_text)
        
        assert isinstance(result, EmbeddingResult)
        assert result.success
        assert result.vector is not None
        assert len(result.vector) == 384
        assert result.model_name is not None
    
    @pytest.mark.asyncio
    async def test_embed_text_empty(self, embedding_processor):
        """Test embedding generation for empty text."""
        result = await embedding_processor.embed_text("")
        
        assert isinstance(result, EmbeddingResult)
        assert not result.success
        assert result.vector is None
        assert result.error is not None
    
    @pytest.mark.asyncio
    async def test_embed_text_very_long(self, embedding_processor):
        """Test embedding generation for very long text."""
        long_text = "Това е дълъг български текст. " * 1000  # Very long text
        result = await embedding_processor.embed_text(long_text)
        
        assert isinstance(result, EmbeddingResult)
        # Should either succeed with truncation or fail gracefully
        if result.success:
            assert result.vector is not None
            assert len(result.vector) == 384
        else:
            assert result.error is not None
    
    @pytest.mark.asyncio
    async def test_embed_batch_basic(self, embedding_processor):
        """Test batch embedding generation."""
        texts = [
            "Първи български текст за тестване",
            "Втори текст с различно съдържание",
            "Трети текст за пълнота на теста"
        ]
        
        batch_result = await embedding_processor.embed_batch(texts)

        assert len(batch_result.embeddings) == len(texts)
        assert all(isinstance(result, EmbeddingResult) for result in batch_result.embeddings)
        assert all(result.success for result in batch_result.embeddings)
        assert all(len(result.vector) == 384 for result in batch_result.embeddings)
        assert batch_result.success_rate == 1.0
        assert batch_result.total_texts == len(texts)
    
    @pytest.mark.asyncio
    async def test_embed_batch_mixed_languages(self, embedding_processor):
        """Test batch embedding with mixed languages."""
        texts = [
            "Български текст за европейски фондове",
            "English text about EU funding",
            "Смесен текст с mixed content"
        ]
        
        batch_result = await embedding_processor.embed_batch(texts)

        assert len(batch_result.embeddings) == len(texts)
        assert all(result.success for result in batch_result.embeddings)
        # All should have same dimension regardless of language
        assert all(len(result.vector) == 384 for result in batch_result.embeddings)
        assert batch_result.success_rate == 1.0
    
    @pytest.mark.asyncio
    async def test_embed_batch_empty_list(self, embedding_processor):
        """Test batch embedding with empty list."""
        batch_result = await embedding_processor.embed_batch([])
        assert batch_result.embeddings == []
        assert batch_result.total_texts == 0
        assert batch_result.successful_embeddings == 0
    
    @pytest.mark.asyncio
    async def test_embed_batch_with_errors(self, embedding_processor):
        """Test batch embedding with some invalid inputs."""
        texts = [
            "Валиден български текст",
            "",  # Empty text
            "Друг валиден текст",
            None  # Invalid input
        ]
        
        batch_result = await embedding_processor.embed_batch(texts)

        # Should have some embeddings but not all successful
        assert batch_result.total_texts == len(texts)
        assert batch_result.successful_embeddings < len(texts)
        assert batch_result.success_rate < 1.0
        assert len(batch_result.failed_indices) > 0
    
    @pytest.mark.asyncio
    async def test_caching_functionality(self, embedding_processor):
        """Test embedding caching mechanism."""
        test_text = "Тест текст за кеширане на embeddings"
        
        # First call - should generate embedding
        result1 = await embedding_processor.embed_text(test_text)
        assert result1.success
        
        # Second call - should use cache
        result2 = await embedding_processor.embed_text(test_text)
        assert result2.success
        
        # Results should be identical
        np.testing.assert_array_equal(result1.vector, result2.vector)
        assert result1.model_name == result2.model_name
    
    @pytest.mark.asyncio
    async def test_cache_size_limit(self, embedding_processor):
        """Test cache size limitations."""
        # Generate many different embeddings to test cache limits
        for i in range(150):  # More than typical cache size
            text = f"Тест текст номер {i} за проверка на кеш лимита"
            result = await embedding_processor.embed_text(text)
            assert result.success
        
        # Cache should not grow indefinitely
        cache_size = len(embedding_processor.embedding_cache)
        assert cache_size <= 100  # Reasonable cache size limit
    
    @pytest.mark.asyncio
    async def test_similarity_calculation(self, embedding_processor):
        """Test vector similarity calculation."""
        text1 = "Европейски фондове за иновации"
        text2 = "ЕС финансиране на иновативни проекти"
        text3 = "Котки и кучета като домашни любимци"
        
        result1 = await embedding_processor.embed_text(text1)
        result2 = await embedding_processor.embed_text(text2)
        result3 = await embedding_processor.embed_text(text3)
        
        assert all(r.success for r in [result1, result2, result3])
        
        # Calculate similarities
        sim_1_2 = embedding_processor.calculate_similarity(result1.vector, result2.vector)
        sim_1_3 = embedding_processor.calculate_similarity(result1.vector, result3.vector)
        
        # Related texts should be more similar than unrelated
        assert sim_1_2 > sim_1_3
        assert 0 <= sim_1_2 <= 1  # Cosine similarity bounds
        assert 0 <= sim_1_3 <= 1
    
    @pytest.mark.asyncio
    async def test_bulgarian_optimization(self, embedding_processor):
        """Test Bulgarian language optimization features."""
        bulgarian_funding_text = "ОПИК програма за подкрепа на МСП в България"
        english_funding_text = "SME support program in European Union"
        
        bg_result = await embedding_processor.embed_text(bulgarian_funding_text)
        en_result = await embedding_processor.embed_text(english_funding_text)
        
        assert bg_result.success
        assert en_result.success
        
        # Both should produce valid embeddings
        assert len(bg_result.vector) == len(en_result.vector)
        
        # Test that Bulgarian content is properly processed
        assert bg_result.model_name is not None
    
    @pytest.mark.asyncio
    async def test_error_handling(self, embedding_processor):
        """Test error handling in embedding generation."""
        # Test with various problematic inputs
        problematic_inputs = [
            None,
            "",
            " " * 1000,  # Only whitespace
            "a" * 10000,  # Extremely long
        ]
        
        for problematic_input in problematic_inputs:
            result = await embedding_processor.embed_text(problematic_input)
            assert isinstance(result, EmbeddingResult)
            # Should either succeed or fail gracefully
            if not result.success:
                assert result.error is not None
    
    @pytest.mark.asyncio
    async def test_model_info(self, embedding_processor):
        """Test model information retrieval."""
        model_info = embedding_processor.get_model_info()
        
        assert isinstance(model_info, dict)
        assert 'model_name' in model_info
        assert 'dimensions' in model_info
        assert 'max_sequence_length' in model_info
        
        assert model_info['dimensions'] == 384
        assert model_info['model_name'] is not None
    
    @pytest.mark.asyncio
    async def test_preprocessing_consistency(self, embedding_processor):
        """Test that text preprocessing is consistent."""
        original_text = "  Това е   тест  с много   spaces  \n\n  "
        cleaned_text = "Това е тест с много spaces"
        
        result1 = await embedding_processor.embed_text(original_text)
        result2 = await embedding_processor.embed_text(cleaned_text)
        
        assert result1.success
        assert result2.success
        
        # Should produce very similar embeddings after preprocessing
        similarity = embedding_processor.calculate_similarity(result1.vector, result2.vector)
        assert similarity > 0.95  # Very high similarity expected
    
    @pytest.mark.asyncio
    async def test_funding_terms_recognition(self, embedding_processor):
        """Test recognition of EU funding-related terms."""
        funding_terms = [
            "европейски фондове",
            "ОПИК програма", 
            "безвъзмездна помощ",
            "МСП финансиране",
            "иновации и конкурентоспособност"
        ]
        
        generic_terms = [
            "времето днес",
            "готвене на храна",
            "спортни новини",
            "музикални инструменти"
        ]
        
        funding_batch = await embedding_processor.embed_batch(funding_terms)
        generic_batch = await embedding_processor.embed_batch(generic_terms)

        funding_results = funding_batch.embeddings
        generic_results = generic_batch.embeddings

        assert all(r.success for r in funding_results)
        assert all(r.success for r in generic_results)

        # Funding terms should cluster together more than with generic terms
        funding_similarities = []
        for i in range(len(funding_results)):
            for j in range(i+1, len(funding_results)):
                sim = embedding_processor.calculate_similarity(
                    funding_results[i].vector,
                    funding_results[j].vector
                )
                funding_similarities.append(sim)
        
        cross_similarities = []
        for funding_result in funding_results[:2]:  # Sample to avoid too many comparisons
            for generic_result in generic_results[:2]:
                sim = embedding_processor.calculate_similarity(
                    funding_result.vector,
                    generic_result.vector
                )
                cross_similarities.append(sim)
        
        # Funding terms should be more similar to each other
        avg_funding_sim = np.mean(funding_similarities)
        avg_cross_sim = np.mean(cross_similarities)
        
        assert avg_funding_sim > avg_cross_sim
