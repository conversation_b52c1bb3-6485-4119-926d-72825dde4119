#!/usr/bin/env python3
"""
Test the RPC function with proper parameters.
"""

import asyncio
import sys
import os
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def test_rpc_function():
    """Test RPC function with proper parameters."""
    print("🧪 ТЕСТВАНЕ НА RPC ФУНКЦИЯ С ПАРАМЕТРИ")
    print("=" * 50)
    
    # Initialize Supabase client
    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_SERVICE_KEY") or os.getenv("SUPABASE_KEY")
    supabase: Client = create_client(url, key)
    
    # Test with proper parameters
    try:
        print("🔍 Тестване на match_eu_funds_content с параметри...")
        
        # Create test embedding
        test_embedding = [0.1] * 1536  # Small non-zero values
        
        result = supabase.rpc('match_eu_funds_content', {
            'query_embedding': test_embedding,
            'match_threshold': 0.1,  # Low threshold for testing
            'match_count': 3
        }).execute()
        
        print(f"   ✅ Функцията работи!")
        print(f"   📊 Намерени резултати: {len(result.data)}")
        
        if result.data:
            for i, item in enumerate(result.data[:2]):
                print(f"   Резултат {i+1}:")
                print(f"     Title: {item.get('title', 'N/A')}")
                print(f"     Similarity: {item.get('similarity', 0):.3f}")
                print(f"     Content: {item.get('content', '')[:100]}...")
        else:
            print(f"   ⚠️ Няма резултати (вероятно няма embeddings в данните)")
            
    except Exception as e:
        print(f"   ❌ Грешка: {e}")
        
        # Check if it's a function not found error
        if "function" in str(e).lower() and "not found" in str(e).lower():
            print(f"   💡 Функцията не съществува или има грешка в параметрите")
        elif "embedding" in str(e).lower():
            print(f"   💡 Проблем с embedding данните")

if __name__ == "__main__":
    asyncio.run(test_rpc_function())
