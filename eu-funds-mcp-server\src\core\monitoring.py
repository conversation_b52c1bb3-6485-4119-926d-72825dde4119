"""
Health monitoring and metrics for EU Funds MCP Server.

This module provides health endpoints, Prometheus metrics, and connection testing
for monitoring the MCP server's operational status.
"""

import asyncio
import time
from typing import Dict, Any, Optional
from datetime import datetime

from fastapi import FastAPI, HTTPException
from fastapi.responses import J<PERSON><PERSON>esponse
from prometheus_client import Counter, Histogram, Gauge, generate_latest, CONTENT_TYPE_LATEST
from supabase import create_client, Client

from .config import get_settings
from .logging import structured_logger

# Prometheus metrics
REQUEST_COUNT = Counter('mcp_requests_total', 'Total MCP requests', ['method', 'endpoint'])
REQUEST_DURATION = Histogram('mcp_request_duration_seconds', 'Request duration')
ACTIVE_CONNECTIONS = Gauge('mcp_active_connections', 'Active database connections')
CRAWL_DOCUMENTS = Gauge('mcp_crawl_documents_total', 'Total crawled documents')
SEARCH_QUERIES = Counter('mcp_search_queries_total', 'Total search queries', ['search_type'])
ERRORS = Counter('mcp_errors_total', 'Total errors', ['error_type'])


class HealthChecker:
    """Health check manager for the MCP server."""
    
    def __init__(self):
        self.settings = get_settings()
        self.supabase: Optional[Client] = None
        self._last_health_check = None
        self._health_status = {}
    
    async def initialize(self):
        """Initialize health checker with database connection."""
        try:
            self.supabase = create_client(
                self.settings.database.supabase_url,
                self.settings.database.supabase_service_key
            )
            structured_logger.log_performance_metric("health_checker_init", 1, "success")
        except Exception as e:
            structured_logger.log_error(e, "health_checker_initialization")
            raise
    
    async def check_database_health(self) -> Dict[str, Any]:
        """Check database connectivity and performance."""
        start_time = time.time()
        
        try:
            # Test basic connectivity
            response = self.supabase.table('funding_documents').select('count').execute()
            
            # Test vector extension
            vector_test = self.supabase.rpc('semantic_search', {
                'query_embedding': [0.0] * 1024,
                'match_threshold': 0.9,
                'match_count': 1
            }).execute()
            
            duration = (time.time() - start_time) * 1000
            
            return {
                "status": "healthy",
                "response_time_ms": round(duration, 2),
                "documents_count": len(response.data) if response.data else 0,
                "vector_search_available": True,
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            duration = (time.time() - start_time) * 1000
            structured_logger.log_error(e, "database_health_check")
            ERRORS.labels(error_type="database_health").inc()
            
            return {
                "status": "unhealthy",
                "error": str(e),
                "response_time_ms": round(duration, 2),
                "timestamp": datetime.utcnow().isoformat()
            }
    
    async def check_embedding_service(self) -> Dict[str, Any]:
        """Check embedding service availability."""
        try:
            # This would test the embedding model loading
            # For now, we'll just check if the model name is configured
            model_name = self.settings.ai.embedding_model_name
            
            return {
                "status": "healthy",
                "model_name": model_name,
                "model_dimension": self.settings.ai.embedding_dimension,
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            structured_logger.log_error(e, "embedding_service_health_check")
            ERRORS.labels(error_type="embedding_service").inc()
            
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
    
    async def check_crawler_health(self) -> Dict[str, Any]:
        """Check web crawler service health."""
        try:
            # Check if target domains are accessible
            domains = self.settings.crawler.crawl_domains.split(',')
            
            return {
                "status": "healthy",
                "target_domains": domains,
                "browser_pool_size": self.settings.crawler.browser_pool_size,
                "max_concurrent_requests": self.settings.crawler.max_concurrent_requests,
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            structured_logger.log_error(e, "crawler_health_check")
            ERRORS.labels(error_type="crawler_service").inc()
            
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
    
    async def get_comprehensive_health(self) -> Dict[str, Any]:
        """Get comprehensive health status of all services."""
        start_time = time.time()
        
        # Run all health checks concurrently
        database_health, embedding_health, crawler_health = await asyncio.gather(
            self.check_database_health(),
            self.check_embedding_service(),
            self.check_crawler_health(),
            return_exceptions=True
        )
        
        # Determine overall status
        all_healthy = all(
            health.get("status") == "healthy" 
            for health in [database_health, embedding_health, crawler_health]
            if isinstance(health, dict)
        )
        
        overall_status = "healthy" if all_healthy else "degraded"
        
        health_report = {
            "status": overall_status,
            "timestamp": datetime.utcnow().isoformat(),
            "response_time_ms": round((time.time() - start_time) * 1000, 2),
            "services": {
                "database": database_health,
                "embedding": embedding_health,
                "crawler": crawler_health
            },
            "system": {
                "version": "0.1.0",
                "environment": self.settings.environment,
                "debug_mode": self.settings.debug
            }
        }
        
        # Cache the result
        self._last_health_check = time.time()
        self._health_status = health_report
        
        # Log health check
        structured_logger.log_performance_metric(
            "health_check_duration", 
            health_report["response_time_ms"], 
            "ms"
        )
        
        return health_report


# Global health checker instance
health_checker = HealthChecker()


def create_health_endpoints(app: FastAPI):
    """Add health check endpoints to FastAPI app."""
    
    @app.get("/health")
    async def health_check():
        """Basic health check endpoint."""
        try:
            health_status = await health_checker.get_comprehensive_health()
            status_code = 200 if health_status["status"] == "healthy" else 503
            return JSONResponse(content=health_status, status_code=status_code)
        except Exception as e:
            structured_logger.log_error(e, "health_endpoint")
            return JSONResponse(
                content={
                    "status": "unhealthy",
                    "error": str(e),
                    "timestamp": datetime.utcnow().isoformat()
                },
                status_code=503
            )
    
    @app.get("/health/database")
    async def database_health():
        """Database-specific health check."""
        health_status = await health_checker.check_database_health()
        status_code = 200 if health_status["status"] == "healthy" else 503
        return JSONResponse(content=health_status, status_code=status_code)
    
    @app.get("/health/ready")
    async def readiness_check():
        """Kubernetes readiness probe."""
        health_status = await health_checker.get_comprehensive_health()
        if health_status["status"] == "healthy":
            return {"status": "ready"}
        else:
            raise HTTPException(status_code=503, detail="Service not ready")
    
    @app.get("/health/live")
    async def liveness_check():
        """Kubernetes liveness probe."""
        return {"status": "alive", "timestamp": datetime.utcnow().isoformat()}
    
    @app.get("/metrics")
    async def prometheus_metrics():
        """Prometheus metrics endpoint."""
        return generate_latest()


# Utility functions for metrics
def record_request(method: str, endpoint: str):
    """Record a request in metrics."""
    REQUEST_COUNT.labels(method=method, endpoint=endpoint).inc()

def record_request_duration(duration: float):
    """Record request duration."""
    REQUEST_DURATION.observe(duration)

def record_search_query(search_type: str):
    """Record a search query."""
    SEARCH_QUERIES.labels(search_type=search_type).inc()

def record_error(error_type: str):
    """Record an error."""
    ERRORS.labels(error_type=error_type).inc()

def update_document_count(count: int):
    """Update total document count."""
    CRAWL_DOCUMENTS.set(count)


__all__ = [
    "health_checker",
    "create_health_endpoints",
    "record_request",
    "record_request_duration", 
    "record_search_query",
    "record_error",
    "update_document_count"
]
