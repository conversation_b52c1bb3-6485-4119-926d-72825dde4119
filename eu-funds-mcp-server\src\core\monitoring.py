"""
Health monitoring and metrics collection for EU Funds MCP Server.
Provides comprehensive system monitoring and alerting capabilities.
"""

import time
import psutil
import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from .config import get_settings
from .logging import get_logger
from .exceptions import DatabaseError, MCPServerError


@dataclass
class HealthStatus:
    """Health status data structure."""
    service: str
    status: str  # "healthy", "degraded", "unhealthy"
    timestamp: datetime
    response_time_ms: Optional[float] = None
    details: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        data = asdict(self)
        data["timestamp"] = self.timestamp.isoformat()
        return data


@dataclass
class SystemMetrics:
    """System performance metrics."""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    memory_available_mb: float
    disk_percent: float
    disk_used_gb: float
    disk_free_gb: float

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        data = asdict(self)
        data["timestamp"] = self.timestamp.isoformat()
        return data


class HealthChecker:
    """Health monitoring for all system components."""

    def __init__(self):
        self.settings = get_settings()
        self.logger = get_logger(__name__)
        self._last_check = {}

    async def check_database_health(self) -> HealthStatus:
        """Check database connectivity and performance."""
        start_time = time.time()
        
        try:
            # TODO: Implement actual database health check
            # This is a mock implementation
            await asyncio.sleep(0.01)  # Simulate database query
            
            response_time = (time.time() - start_time) * 1000
            
            return HealthStatus(
                service="database",
                status="healthy",
                timestamp=datetime.now(),
                response_time_ms=response_time,
                details={
                    "connection_pool": "active",
                    "query_performance": "optimal"
                }
            )
            
        except Exception as e:
            self.logger.error(f"Database health check failed: {e}")
            return HealthStatus(
                service="database",
                status="unhealthy",
                timestamp=datetime.now(),
                response_time_ms=(time.time() - start_time) * 1000,
                error=str(e)
            )

    async def check_crawler_health(self) -> HealthStatus:
        """Check crawler service health."""
        start_time = time.time()
        
        try:
            # TODO: Implement actual crawler health check
            # This is a mock implementation
            await asyncio.sleep(0.005)  # Simulate crawler check
            
            response_time = (time.time() - start_time) * 1000
            
            return HealthStatus(
                service="crawler",
                status="healthy",
                timestamp=datetime.now(),
                response_time_ms=response_time,
                details={
                    "browser_pool": "ready",
                    "active_crawls": 0
                }
            )
            
        except Exception as e:
            self.logger.error(f"Crawler health check failed: {e}")
            return HealthStatus(
                service="crawler",
                status="unhealthy",
                timestamp=datetime.now(),
                response_time_ms=(time.time() - start_time) * 1000,
                error=str(e)
            )

    async def check_rag_health(self) -> HealthStatus:
        """Check RAG system health."""
        start_time = time.time()
        
        try:
            # TODO: Implement actual RAG health check
            # This is a mock implementation
            await asyncio.sleep(0.008)  # Simulate RAG check
            
            response_time = (time.time() - start_time) * 1000
            
            return HealthStatus(
                service="rag",
                status="healthy",
                timestamp=datetime.now(),
                response_time_ms=response_time,
                details={
                    "embedding_model": "loaded",
                    "vector_index": "ready"
                }
            )
            
        except Exception as e:
            self.logger.error(f"RAG health check failed: {e}")
            return HealthStatus(
                service="rag",
                status="unhealthy",
                timestamp=datetime.now(),
                response_time_ms=(time.time() - start_time) * 1000,
                error=str(e)
            )

    async def check_all_services(self) -> Dict[str, HealthStatus]:
        """Check health of all services."""
        checks = await asyncio.gather(
            self.check_database_health(),
            self.check_crawler_health(),
            self.check_rag_health(),
            return_exceptions=True
        )
        
        results = {}
        service_names = ["database", "crawler", "rag"]
        
        for i, check in enumerate(checks):
            if isinstance(check, Exception):
                results[service_names[i]] = HealthStatus(
                    service=service_names[i],
                    status="unhealthy",
                    timestamp=datetime.now(),
                    error=str(check)
                )
            else:
                results[check.service] = check
        
        return results

    def get_overall_status(self, service_statuses: Dict[str, HealthStatus]) -> str:
        """Determine overall system health status."""
        statuses = [status.status for status in service_statuses.values()]
        
        if all(status == "healthy" for status in statuses):
            return "healthy"
        elif any(status == "unhealthy" for status in statuses):
            return "unhealthy"
        else:
            return "degraded"


class MetricsCollector:
    """System metrics collection and monitoring."""

    def __init__(self):
        self.logger = get_logger(__name__)
        self._metrics_history: List[SystemMetrics] = []
        self._max_history = 1000  # Keep last 1000 metrics

    def collect_system_metrics(self) -> SystemMetrics:
        """Collect current system performance metrics."""
        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=0.1)
            
            # Memory metrics
            memory = psutil.virtual_memory()
            memory_used_mb = memory.used / (1024 * 1024)
            memory_available_mb = memory.available / (1024 * 1024)
            
            # Disk metrics
            disk = psutil.disk_usage('/')
            disk_used_gb = disk.used / (1024 * 1024 * 1024)
            disk_free_gb = disk.free / (1024 * 1024 * 1024)
            
            metrics = SystemMetrics(
                timestamp=datetime.now(),
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_used_mb=memory_used_mb,
                memory_available_mb=memory_available_mb,
                disk_percent=(disk.used / disk.total) * 100,
                disk_used_gb=disk_used_gb,
                disk_free_gb=disk_free_gb
            )
            
            # Store in history
            self._metrics_history.append(metrics)
            if len(self._metrics_history) > self._max_history:
                self._metrics_history.pop(0)
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"Failed to collect system metrics: {e}")
            raise MCPServerError(f"Metrics collection failed: {e}")

    def get_metrics_summary(self, minutes: int = 5) -> Dict[str, Any]:
        """Get metrics summary for the last N minutes."""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        recent_metrics = [
            m for m in self._metrics_history 
            if m.timestamp >= cutoff_time
        ]
        
        if not recent_metrics:
            return {"error": "No recent metrics available"}
        
        # Calculate averages
        avg_cpu = sum(m.cpu_percent for m in recent_metrics) / len(recent_metrics)
        avg_memory = sum(m.memory_percent for m in recent_metrics) / len(recent_metrics)
        avg_disk = sum(m.disk_percent for m in recent_metrics) / len(recent_metrics)
        
        return {
            "period_minutes": minutes,
            "sample_count": len(recent_metrics),
            "averages": {
                "cpu_percent": round(avg_cpu, 2),
                "memory_percent": round(avg_memory, 2),
                "disk_percent": round(avg_disk, 2)
            },
            "latest": recent_metrics[-1].to_dict() if recent_metrics else None
        }
