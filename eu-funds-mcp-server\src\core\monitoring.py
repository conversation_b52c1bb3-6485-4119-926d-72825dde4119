"""
Health monitoring and metrics for EU Funds MCP Server.
Optimized for low resource usage.
"""

import asyncio
import time
from typing import Dict, Any, Optional
from datetime import datetime, timedelta

import httpx
from fastapi import HTTPException

from .config import get_settings
from .logging import get_logger

logger = get_logger(__name__)


class HealthChecker:
    """Lightweight health checker for system components."""
    
    def __init__(self):
        self.settings = get_settings()
        self._last_check: Optional[datetime] = None
        self._cached_status: Optional[Dict[str, Any]] = None
        self._cache_duration = timedelta(seconds=30)  # Cache for 30 seconds
    
    async def get_health_status(self, force_refresh: bool = False) -> Dict[str, Any]:
        """Get comprehensive health status with caching."""
        now = datetime.now()
        
        # Use cached result if available and fresh
        if (not force_refresh and 
            self._cached_status and 
            self._last_check and 
            now - self._last_check < self._cache_duration):
            return self._cached_status
        
        # Perform health checks
        status = {
            "status": "healthy",
            "timestamp": now.isoformat(),
            "version": "0.1.0",
            "environment": self.settings.environment,
            "checks": {}
        }
        
        # Check database connectivity
        db_status = await self._check_database()
        status["checks"]["database"] = db_status
        
        # Check Redis connectivity (if configured)
        redis_status = await self._check_redis()
        status["checks"]["redis"] = redis_status
        
        # Check system resources
        system_status = await self._check_system_resources()
        status["checks"]["system"] = system_status
        
        # Determine overall status
        failed_checks = [
            name for name, check in status["checks"].items() 
            if not check.get("healthy", False)
        ]
        
        if failed_checks:
            status["status"] = "unhealthy"
            status["failed_checks"] = failed_checks
        
        # Cache the result
        self._cached_status = status
        self._last_check = now
        
        return status
    
    async def _check_database(self) -> Dict[str, Any]:
        """Check Supabase database connectivity."""
        try:
            from supabase import create_client
            
            supabase = create_client(
                self.settings.database.supabase_url,
                self.settings.database.supabase_service_key
            )
            
            # Simple query to test connectivity
            start_time = time.time()
            result = supabase.table("funding_documents").select("count", count="exact").execute()
            response_time = time.time() - start_time
            
            return {
                "healthy": True,
                "response_time_ms": round(response_time * 1000, 2),
                "document_count": result.count if result.count else 0,
                "message": "Database connection successful"
            }
            
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return {
                "healthy": False,
                "error": str(e),
                "message": "Database connection failed"
            }
    
    async def _check_redis(self) -> Dict[str, Any]:
        """Check Redis connectivity."""
        try:
            import redis.asyncio as redis
            
            redis_client = redis.from_url(
                self.settings.redis.redis_url,
                password=self.settings.redis.redis_password,
                db=self.settings.redis.redis_db,
                socket_timeout=5
            )
            
            start_time = time.time()
            await redis_client.ping()
            response_time = time.time() - start_time
            
            # Get basic info
            info = await redis_client.info("memory")
            used_memory = info.get("used_memory_human", "unknown")
            
            await redis_client.close()
            
            return {
                "healthy": True,
                "response_time_ms": round(response_time * 1000, 2),
                "used_memory": used_memory,
                "message": "Redis connection successful"
            }
            
        except Exception as e:
            logger.warning(f"Redis health check failed: {e}")
            return {
                "healthy": False,
                "error": str(e),
                "message": "Redis connection failed (optional service)"
            }
    
    async def _check_system_resources(self) -> Dict[str, Any]:
        """Check basic system resources."""
        try:
            import psutil
            
            # Memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # CPU usage (quick sample)
            cpu_percent = psutil.cpu_percent(interval=0.1)
            
            # Disk usage
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            
            # Determine if resources are healthy
            healthy = (
                memory_percent < 90 and  # Less than 90% memory usage
                cpu_percent < 95 and     # Less than 95% CPU usage
                disk_percent < 90        # Less than 90% disk usage
            )
            
            return {
                "healthy": healthy,
                "memory_percent": round(memory_percent, 1),
                "cpu_percent": round(cpu_percent, 1),
                "disk_percent": round(disk_percent, 1),
                "memory_available_gb": round(memory.available / (1024**3), 2),
                "message": "System resources OK" if healthy else "High resource usage detected"
            }
            
        except ImportError:
            # psutil not available, return basic info
            return {
                "healthy": True,
                "message": "System monitoring not available (psutil not installed)"
            }
        except Exception as e:
            logger.error(f"System resource check failed: {e}")
            return {
                "healthy": False,
                "error": str(e),
                "message": "System resource check failed"
            }


# Global health checker instance
health_checker = HealthChecker()


async def get_health() -> Dict[str, Any]:
    """Get current health status."""
    return await health_checker.get_health_status()


async def get_readiness() -> Dict[str, Any]:
    """Check if service is ready to accept requests."""
    health_status = await health_checker.get_health_status()
    
    # Service is ready if database is healthy
    database_healthy = health_status["checks"].get("database", {}).get("healthy", False)
    
    return {
        "ready": database_healthy,
        "timestamp": datetime.now().isoformat(),
        "message": "Service ready" if database_healthy else "Service not ready - database unavailable"
    }


async def get_liveness() -> Dict[str, Any]:
    """Basic liveness check."""
    return {
        "alive": True,
        "timestamp": datetime.now().isoformat(),
        "uptime_seconds": time.time() - start_time,
        "message": "Service is alive"
    }


# Track service start time
start_time = time.time()
