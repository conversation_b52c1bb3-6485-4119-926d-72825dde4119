# INITIAL.md - Template за Feature Requests

## FEATURE REQUEST TEMPLATE

Когато искаш нова функционалност за EU Funds MCP Server, използвай този формат:

### FEATURE: 
- [Опиши накратко какво искаш]

### CONTEXT:
- [Защо е нужно това]
- [Как се вписва в общата архитектура]

### EXAMPLES:
- [Дай конкретни примери за използване]
- [Покажи очаквани входове и изходи]

### TECHNICAL REQUIREMENTS:
- [Специфични технически изисквания]
- [Интеграции с други компоненти]

### SUCCESS CRITERIA:
- [ ] [Конкретен критерий 1]
- [ ] [Конкретен критерий 2]
- [ ] [Конкретен критерий 3]

## ПРИМЕР:

### FEATURE: 
- Добавяне на real-time notifications за нови EU фондове

### CONTEXT:
- Потребителите искат да бъдат уведомявани веднага при публикуване на нови програми
- Интегрира се с crawler компонента и notification системата

### EXAMPLES:
- Когато се публикува нова програма на eufunds.bg, потребителят получава уведомление
- Уведомлението съдържа: име на програмата, краен срок, бюджет

### TECHNICAL REQUIREMENTS:
- WebSocket връзка за real-time комуникация
- Integration с crawler change detection
- Notification queue с Redis

### SUCCESS CRITERIA:
- [ ] WebSocket endpoint работи правилно
- [ ] Уведомленията се изпращат в реално време
- [ ] Няма дублиращи се уведомления
- [ ] Работи с всички target domains
