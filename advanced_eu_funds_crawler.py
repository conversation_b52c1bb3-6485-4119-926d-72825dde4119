#!/usr/bin/env python3
"""
Advanced EU Funds Crawler based on mcp-crawl4ai-rag approach.
Implements smart crawling with depth 2, parallel processing, and intelligent chunking.
"""

import asyncio
import aiohttp
import sys
import os
from datetime import datetime
from typing import List, Dict, Any, Set, Optional
from urllib.parse import urljoin, urlparse, urldefrag
from bs4 import BeautifulSoup
import re
import json
import time
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor
import openai

sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.core.vector_store import VectorStore
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

@dataclass
class CrawlResult:
    """Result of crawling a single page."""
    url: str
    content: str
    title: str
    success: bool
    error: Optional[str] = None
    internal_links: List[str] = None
    word_count: int = 0

class AdvancedEUFundsCrawler:
    """Advanced crawler implementing mcp-crawl4ai-rag approach for EU funds."""
    
    def __init__(self):
        self.vector_store = VectorStore()
        
        # Configuration
        self.max_depth = 2
        self.max_pages = 50
        self.max_concurrent = 5
        self.delay = 1.0
        self.timeout = 30
        self.chunk_size = 3000  # Larger chunks for better context
        
        # Starting URLs for EU funds
        self.start_urls = [
            "https://www.eufunds.bg/bg",
            "https://www.eufunds.bg/bg/programi",
            "https://www.eufunds.bg/bg/novini",
            "https://www.eufunds.bg/bg/sabitiya",
            "https://www.eufunds.bg/bg/opik",
            "https://www.eufunds.bg/bg/oprd",
            "https://www.eufunds.bg/bg/oprhr",
        ]
        
        # URL patterns to include (Bulgarian pages only)
        self.include_patterns = [
            r"eufunds\.bg/bg",
            r"eufunds\.bg/bg/programi",
            r"eufunds\.bg/bg/novini",
            r"eufunds\.bg/bg/sabitiya",
            r"eufunds\.bg/bg/opik",
            r"eufunds\.bg/bg/oprd",
            r"eufunds\.bg/bg/oprhr",
        ]
        
        # URL patterns to exclude
        self.exclude_patterns = [
            r"\.pdf$", r"\.doc$", r"\.xls$", r"\.zip$",
            r"/en/", r"/en$",  # Exclude English pages
            r"javascript:", r"mailto:", r"tel:",
            r"#", r"\?print=", r"/print/",
            r"\.jpg$", r"\.png$", r"\.gif$",
        ]
        
        # Tracking
        self.visited_urls: Set[str] = set()
        self.crawl_results: List[CrawlResult] = []
        
        # OpenAI setup for embeddings
        openai.api_key = os.getenv("OPENAI_API_KEY")
    
    async def crawl_comprehensive(self) -> Dict[str, Any]:
        """Run comprehensive crawling with depth 2."""
        print("🚀 СТАРТИРАНЕ НА ADVANCED EU FUNDS CRAWLER")
        print("=" * 60)
        
        start_time = datetime.now()
        
        # Initialize vector store
        await self.vector_store.initialize()
        
        # Clear existing data
        print("🧹 Изчистване на стари данни...")
        await self._clear_old_data()
        
        # Start crawling
        await self._crawl_recursive()
        
        # Process and store results
        total_chunks = await self._process_and_store_results()
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # Generate report
        return {
            "success": True,
            "pages_crawled": len(self.crawl_results),
            "chunks_stored": total_chunks,
            "duration_seconds": duration,
            "urls_crawled": [r.url for r in self.crawl_results[:10]],
            "average_words_per_page": sum(r.word_count for r in self.crawl_results) / len(self.crawl_results) if self.crawl_results else 0
        }
    
    async def _clear_old_data(self):
        """Clear old data from Supabase."""
        try:
            # Clear eu_funds_content table
            result = self.vector_store.supabase.table('eu_funds_content').delete().neq('id', '00000000-0000-0000-0000-000000000000').execute()
            print(f"   ✅ Изчистени стари данни")
        except Exception as e:
            print(f"   ⚠️ Грешка при изчистване: {e}")
    
    async def _crawl_recursive(self):
        """Crawl recursively with depth control."""
        current_urls = set(self.start_urls)
        
        for depth in range(self.max_depth + 1):
            if not current_urls or len(self.visited_urls) >= self.max_pages:
                break
            
            print(f"\n📊 DEPTH {depth}: {len(current_urls)} URLs за crawling")
            
            # Crawl current level
            results = await self._crawl_batch(list(current_urls))
            
            # Collect next level URLs
            next_urls = set()
            for result in results:
                if result.success and result.internal_links:
                    for link in result.internal_links:
                        normalized_link = self._normalize_url(link)
                        if (normalized_link not in self.visited_urls and 
                            self._should_include_url(normalized_link)):
                            next_urls.add(normalized_link)
            
            current_urls = next_urls
            
            # Respectful delay between depth levels
            if depth < self.max_depth:
                await asyncio.sleep(2)
    
    async def _crawl_batch(self, urls: List[str]) -> List[CrawlResult]:
        """Crawl a batch of URLs in parallel."""
        semaphore = asyncio.Semaphore(self.max_concurrent)
        
        async def crawl_single_with_semaphore(url):
            async with semaphore:
                return await self._crawl_single_page(url)
        
        tasks = [crawl_single_with_semaphore(url) for url in urls if url not in self.visited_urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        valid_results = []
        for result in results:
            if isinstance(result, CrawlResult):
                valid_results.append(result)
                self.crawl_results.append(result)
                self.visited_urls.add(result.url)
        
        return valid_results
    
    async def _crawl_single_page(self, url: str) -> CrawlResult:
        """Crawl a single page."""
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                }
                
                async with session.get(url, headers=headers) as response:
                    if response.status != 200:
                        return CrawlResult(url=url, content="", title="", success=False, error=f"HTTP {response.status}")
                    
                    html = await response.text()
                    soup = BeautifulSoup(html, 'html.parser')
                    
                    # Extract title
                    title_tag = soup.find('title')
                    title = title_tag.get_text().strip() if title_tag else ""
                    
                    # Extract clean content
                    content = self._extract_clean_content(soup)
                    
                    # Extract internal links
                    internal_links = self._extract_internal_links(soup, url)
                    
                    # Calculate word count
                    word_count = len(content.split())
                    
                    print(f"   ✅ {url} - {word_count} думи")
                    
                    # Respectful delay
                    await asyncio.sleep(self.delay)
                    
                    return CrawlResult(
                        url=url,
                        content=content,
                        title=title,
                        success=True,
                        internal_links=internal_links,
                        word_count=word_count
                    )
                    
        except Exception as e:
            print(f"   ❌ {url} - {e}")
            return CrawlResult(url=url, content="", title="", success=False, error=str(e))
    
    def _extract_clean_content(self, soup: BeautifulSoup) -> str:
        """Extract clean text content from HTML."""
        # Remove unwanted elements
        for element in soup(["script", "style", "nav", "header", "footer", "aside", "form"]):
            element.decompose()
        
        # Get text
        text = soup.get_text()
        
        # Clean up text
        lines = (line.strip() for line in text.splitlines())
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        text = ' '.join(chunk for chunk in chunks if chunk)
        
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        return text.strip()
    
    def _extract_internal_links(self, soup: BeautifulSoup, base_url: str) -> List[str]:
        """Extract internal links from the page."""
        links = []
        
        for link in soup.find_all('a', href=True):
            href = link['href']
            full_url = urljoin(base_url, href)
            normalized_url = self._normalize_url(full_url)
            
            if self._should_include_url(normalized_url):
                links.append(normalized_url)
        
        return list(set(links))  # Remove duplicates
    
    def _normalize_url(self, url: str) -> str:
        """Normalize URL by removing fragments."""
        return urldefrag(url)[0]
    
    def _should_include_url(self, url: str) -> bool:
        """Check if URL should be included in crawling."""
        # Check include patterns
        include_match = any(re.search(pattern, url) for pattern in self.include_patterns)
        if not include_match:
            return False
        
        # Check exclude patterns
        exclude_match = any(re.search(pattern, url) for pattern in self.exclude_patterns)
        if exclude_match:
            return False
        
        # Check domain
        parsed = urlparse(url)
        if parsed.netloc not in ['www.eufunds.bg', 'eufunds.bg']:
            return False
        
        return True
    
    def _smart_chunk_content(self, content: str) -> List[str]:
        """Smart chunking based on mcp-crawl4ai-rag approach."""
        chunks = []
        start = 0
        content_length = len(content)
        
        while start < content_length:
            end = start + self.chunk_size
            
            if end >= content_length:
                chunks.append(content[start:].strip())
                break
            
            chunk = content[start:end]
            
            # Try to break at paragraph
            if '\n\n' in chunk:
                last_break = chunk.rfind('\n\n')
                if last_break > self.chunk_size * 0.3:
                    end = start + last_break
            # Try to break at sentence
            elif '. ' in chunk:
                last_period = chunk.rfind('. ')
                if last_period > self.chunk_size * 0.3:
                    end = start + last_period + 1
            
            chunk = content[start:end].strip()
            if chunk:
                chunks.append(chunk)
            
            start = end
        
        return chunks
    
    async def _process_and_store_results(self) -> int:
        """Process crawl results and store in Supabase."""
        print(f"\n💾 ОБРАБОТКА И ЗАПИСВАНЕ НА {len(self.crawl_results)} СТРАНИЦИ")
        
        total_chunks = 0
        
        for result in self.crawl_results:
            if not result.success or len(result.content) < 100:
                continue
            
            # Chunk the content
            chunks = self._smart_chunk_content(result.content)
            
            # Store each chunk
            for i, chunk in enumerate(chunks):
                try:
                    # Create metadata
                    metadata = {
                        "source_url": result.url,
                        "title": result.title or f"Content from {result.url}",
                        "content_type": "web_page",
                        "language": "bg",
                        "chunk_index": i,
                        "total_chunks": len(chunks),
                        "word_count": len(chunk.split()),
                        "crawl_timestamp": datetime.now().isoformat(),
                    }
                    
                    # Store in Supabase
                    await self._store_chunk_in_supabase(chunk, metadata)
                    total_chunks += 1
                    
                except Exception as e:
                    print(f"   ❌ Грешка при записване на chunk: {e}")
        
        print(f"   ✅ Записани {total_chunks} chunks")
        return total_chunks
    
    async def _store_chunk_in_supabase(self, content: str, metadata: Dict[str, Any]):
        """Store a single chunk in Supabase with embedding."""
        try:
            # Create embedding
            embedding = await self._create_embedding(content)

            # Prepare data with embedding
            data = {
                "title": metadata["title"],
                "content": content,
                "source_url": metadata["source_url"],
                "content_type": metadata["content_type"],
                "language": metadata["language"],
                "metadata": metadata,
                "quality_score": self._calculate_quality_score(content),
                "embedding": embedding  # Add embedding to data
            }

            # Insert into Supabase
            result = self.vector_store.supabase.table('eu_funds_content').insert(data).execute()

        except Exception as e:
            print(f"   ⚠️ Грешка при Supabase запис: {e}")
            # Print more details for debugging
            if "column" in str(e).lower() and "embedding" in str(e).lower():
                print(f"   💡 Възможно е таблицата да няма embedding колона")
            print(f"   🔍 Детайли: {e}")
    
    async def _create_embedding(self, text: str) -> List[float]:
        """Create embedding for text."""
        try:
            response = openai.embeddings.create(
                model="text-embedding-3-small",
                input=text
            )
            return response.data[0].embedding
        except Exception as e:
            print(f"   ⚠️ Грешка при embedding: {e}")
            return [0.0] * 1536  # Default embedding
    
    def _calculate_quality_score(self, content: str) -> float:
        """Calculate quality score for content."""
        score = 0.5  # Base score
        
        # Length bonus
        if len(content) > 500:
            score += 0.1
        if len(content) > 1000:
            score += 0.1
        
        # EU funds keywords bonus
        eu_keywords = ["програма", "финансиране", "фонд", "проект", "кандидатстване", "средства"]
        keyword_count = sum(1 for keyword in eu_keywords if keyword.lower() in content.lower())
        score += min(keyword_count * 0.05, 0.2)
        
        return min(score, 1.0)

async def main():
    """Main function."""
    crawler = AdvancedEUFundsCrawler()
    
    try:
        report = await crawler.crawl_comprehensive()
        
        print("\n🎉 CRAWLING ЗАВЪРШЕН!")
        print("=" * 50)
        print(f"📄 Crawl-нати страници: {report['pages_crawled']}")
        print(f"💾 Записани chunks: {report['chunks_stored']}")
        print(f"⏱️ Общо време: {report['duration_seconds']:.1f}s")
        print(f"📊 Средно думи на страница: {report['average_words_per_page']:.0f}")
        
        if report['urls_crawled']:
            print(f"\n📋 ПРИМЕРНИ URL-И:")
            for url in report['urls_crawled']:
                print(f"   • {url}")
        
        # Save report
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        with open(f"advanced_crawl_report_{timestamp}.json", "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
        
        return report
        
    except Exception as e:
        print(f"❌ КРИТИЧНА ГРЕШКА: {e}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}

if __name__ == "__main__":
    asyncio.run(main())
