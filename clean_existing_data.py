#!/usr/bin/env python3
"""
Скрипт за почистване на съществуващи HTML данни в Supabase.

Този скрипт:
1. Взима всички записи от eu_funds_content
2. Почиства HTML съдържанието с BeautifulSoup
3. Обновява записите с почистеното съдържание
4. Регенерира embeddings за почистените данни
"""

import asyncio
import logging
from typing import List, Dict, Any
from supabase import create_client

from src.core.config import settings
from src.core.text_processor import BulgarianTextProcessor
from src.core.embeddings import EmbeddingProcessor

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DataCleaner:
    """Почиства съществуващи HTML данни в Supabase."""
    
    def __init__(self):
        self.supabase = create_client(settings.supabase_url, settings.supabase_service_key)
        self.text_processor = BulgarianTextProcessor()
        self.embedding_processor = EmbeddingProcessor()
        
    async def initialize(self):
        """Инициализира компонентите."""
        logger.info("🔧 Инициализиране на компонентите...")
        # BulgarianTextProcessor няма initialize метод
        await self.embedding_processor.initialize()
        logger.info("✅ Компонентите са инициализирани")
    
    async def get_all_content(self) -> List[Dict[str, Any]]:
        """Взима всички записи от eu_funds_content."""
        try:
            logger.info("📊 Взимане на всички записи от базата...")
            
            result = self.supabase.table('eu_funds_content').select('*').execute()
            
            logger.info(f"✅ Намерени {len(result.data)} записа")
            return result.data
            
        except Exception as e:
            logger.error(f"❌ Грешка при взимане на данни: {e}")
            return []
    
    async def clean_single_record(self, record: Dict[str, Any]) -> Dict[str, Any]:
        """Почиства един запис."""
        try:
            content_id = record['id']
            original_content = record['content']
            
            logger.info(f"🧹 Почистване на запис {content_id}...")
            
            # Почистване на HTML съдържанието
            processed_text = await self.text_processor.process_text(
                text=original_content,
                source_url=record.get('source_url', ''),
                preserve_structure=True
            )
            
            # Генериране на нови embeddings
            embedding_result = await self.embedding_processor.embed_text_with_context(
                text=processed_text.cleaned_text,
                document_context=f"Документ от {record.get('source_url', 'неизвестен източник')}",
                is_query=False
            )
            
            if not embedding_result or not embedding_result.success:
                logger.error(f"❌ Неуспешно генериране на embedding за {content_id}")
                return None
            
            # Подготовка на обновените данни (без embedding - няма такава колона)
            updated_record = {
                'content': processed_text.cleaned_text,
                'title': processed_text.extracted_info.get('title', record.get('title', '')),
                'language': processed_text.language_metrics.language if hasattr(processed_text.language_metrics, 'language') else record.get('language', 'bg'),
                'updated_at': 'now()'
            }
            
            logger.info(f"✅ Почистен запис {content_id}")
            return {
                'id': content_id,
                'updates': updated_record,
                'original_length': len(original_content),
                'cleaned_length': len(processed_text.cleaned_text)
            }
            
        except Exception as e:
            logger.error(f"❌ Грешка при почистване на запис {record.get('id', 'unknown')}: {e}")
            return None
    
    async def update_record(self, content_id: str, updates: Dict[str, Any]) -> bool:
        """Обновява запис в базата."""
        try:
            result = self.supabase.table('eu_funds_content').update(updates).eq('id', content_id).execute()
            
            if result.data:
                logger.info(f"✅ Обновен запис {content_id}")
                return True
            else:
                logger.error(f"❌ Неуспешно обновяване на запис {content_id}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Грешка при обновяване на запис {content_id}: {e}")
            return False
    
    async def clean_all_data(self, batch_size: int = 5) -> Dict[str, int]:
        """Почиства всички данни в батчове."""
        try:
            # Взимане на всички записи
            all_records = await self.get_all_content()
            
            if not all_records:
                logger.warning("⚠️ Няма записи за почистване")
                return {'total': 0, 'cleaned': 0, 'failed': 0}
            
            stats = {'total': len(all_records), 'cleaned': 0, 'failed': 0}
            
            logger.info(f"🚀 Започвам почистване на {stats['total']} записа в батчове от {batch_size}...")
            
            # Обработка в батчове
            for i in range(0, len(all_records), batch_size):
                batch = all_records[i:i + batch_size]
                batch_num = (i // batch_size) + 1
                total_batches = (len(all_records) + batch_size - 1) // batch_size
                
                logger.info(f"📦 Обработка на батч {batch_num}/{total_batches} ({len(batch)} записа)...")
                
                # Почистване на записите в батча
                cleaning_tasks = [self.clean_single_record(record) for record in batch]
                cleaned_results = await asyncio.gather(*cleaning_tasks, return_exceptions=True)
                
                # Обновяване на записите
                for result in cleaned_results:
                    if isinstance(result, Exception):
                        logger.error(f"❌ Грешка в батча: {result}")
                        stats['failed'] += 1
                        continue
                    
                    if result is None:
                        stats['failed'] += 1
                        continue
                    
                    # Обновяване в базата
                    success = await self.update_record(result['id'], result['updates'])

                    if success:
                        stats['cleaned'] += 1
                        logger.info(f"📊 Запис {result['id']}: {result['original_length']} → {result['cleaned_length']} символа")
                    else:
                        stats['failed'] += 1
                
                logger.info(f"✅ Батч {batch_num} завършен. Прогрес: {stats['cleaned']}/{stats['total']}")
            
            logger.info(f"🎉 ПОЧИСТВАНЕТО ЗАВЪРШИ!")
            logger.info(f"📊 Статистики:")
            logger.info(f"   • Общо записи: {stats['total']}")
            logger.info(f"   • Почистени: {stats['cleaned']}")
            logger.info(f"   • Неуспешни: {stats['failed']}")
            logger.info(f"   • Успешност: {(stats['cleaned']/stats['total']*100):.1f}%")
            
            return stats
            
        except Exception as e:
            logger.error(f"❌ Критична грешка при почистване: {e}")
            return {'total': 0, 'cleaned': 0, 'failed': 0}

async def main():
    """Главна функция."""
    cleaner = DataCleaner()
    
    try:
        await cleaner.initialize()
        stats = await cleaner.clean_all_data(batch_size=3)  # Малки батчове за стабилност
        
        if stats['cleaned'] > 0:
            logger.info("🎯 Препоръчвам да тествате точността отново с accuracy_test.py")
        
    except Exception as e:
        logger.error(f"❌ Критична грешка: {e}")

if __name__ == "__main__":
    asyncio.run(main())
