#!/usr/bin/env python3
"""
Run crawler with depth 3 to get more comprehensive data.
"""

import asyncio
import sys
import os
from datetime import datetime

sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.crawler.eu_funds_crawler import EUFundsCrawler

async def run_depth3_crawling():
    """Run comprehensive crawling with depth 3."""
    print("🚀 СТАРТИРАНЕ НА CRAWLING С ДЪЛБОЧИНА 3")
    print("=" * 60)
    print(f"⏰ Започва в: {datetime.now().strftime('%H:%M:%S')}")
    print("🎯 Цел: Максимално покритие на eufunds.bg")
    print("⚠️ Внимание: Това може да отнеме 10-15 минути")
    print()
    
    crawler = EUFundsCrawler()

    # Configure for depth 3
    crawler.crawl_depth = 3
    crawler.max_pages = 100  # Increase for depth 3
    crawler.delay = 1.0  # Respectful crawling
    crawler.timeout = 30

    print("⚙️ КОНФИГУРАЦИЯ:")
    print(f"   📊 Дълбочина: {crawler.crawl_depth}")
    print(f"   📄 Макс страници: {crawler.max_pages}")
    print(f"   ⏱️ Delay: {crawler.delay}s")
    print(f"   ⏰ Timeout: {crawler.timeout}s")
    print()

    try:
        # Start crawling - use the available method
        results = await crawler.crawl_test_sites()
        
        print("\n🎉 CRAWLING ЗАВЪРШЕН!")
        print("=" * 40)
        print(f"📄 Обработени страници: {results.get('pages_crawled', 0)}")
        print(f"💾 Записани chunks: {results.get('chunks_stored', 0)}")
        print(f"❌ Неуспешни страници: {results.get('failed_pages', 0)}")
        print(f"⏱️ Общо време: {results.get('total_time', 'N/A')}")
        
        if results.get('sample_urls'):
            print(f"\n📋 ПРИМЕРНИ URL-И:")
            for url in results.get('sample_urls', [])[:5]:
                print(f"   • {url}")
        
        # Check final data count
        print(f"\n🔍 ПРОВЕРКА НА ФИНАЛНИТЕ ДАННИ:")
        from src.core.vector_store import VectorStore
        vector_store = VectorStore()
        await vector_store.initialize()
        
        # Count total records
        total_count = await vector_store.count_documents()
        print(f"📊 Общо записи в базата: {total_count}")
        
        if total_count > 50:
            print("✅ Отлично! Имаме достатъчно данни за качествен RAG")
        elif total_count > 20:
            print("⚠️ Добре, но може да се подобри")
        else:
            print("❌ Малко данни - препоръчвам повторно crawling")
            
    except Exception as e:
        print(f"❌ ГРЕШКА ПРИ CRAWLING: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(run_depth3_crawling())
