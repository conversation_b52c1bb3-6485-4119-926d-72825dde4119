#!/usr/bin/env python3
"""
Check if embeddings are properly stored in the database.
"""

import asyncio
import sys
import os
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def check_embeddings():
    """Check embeddings in the database."""
    print("🔍 ПРОВЕРКА НА EMBEDDINGS В БАЗАТА")
    print("=" * 50)
    
    # Initialize Supabase client
    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_SERVICE_KEY") or os.getenv("SUPABASE_KEY")
    supabase: Client = create_client(url, key)
    
    # Check total records
    try:
        result = supabase.table('eu_funds_content').select("id, title, embedding").limit(10).execute()
        
        print(f"📊 Общо записи: {len(result.data)}")
        
        if not result.data:
            print(f"❌ Няма данни в таблицата!")
            return
        
        # Check embeddings
        records_with_embeddings = 0
        records_without_embeddings = 0
        
        for i, record in enumerate(result.data):
            embedding = record.get('embedding')
            title = record.get('title', 'N/A')
            
            if embedding is not None and len(embedding) > 0:
                records_with_embeddings += 1
                if i < 3:  # Show first 3
                    print(f"   ✅ Запис {i+1}: {title[:50]}... - embedding: {len(embedding)} dimensions")
            else:
                records_without_embeddings += 1
                if i < 3:  # Show first 3
                    print(f"   ❌ Запис {i+1}: {title[:50]}... - БЕЗ embedding")
        
        print(f"\n📈 СТАТИСТИКА:")
        print(f"   ✅ С embeddings: {records_with_embeddings}")
        print(f"   ❌ Без embeddings: {records_without_embeddings}")
        
        if records_with_embeddings == 0:
            print(f"\n💡 ПРОБЛЕМ: Няма записи с embeddings!")
            print(f"   Възможни причини:")
            print(f"   1. Crawler-ът не записва embeddings правилно")
            print(f"   2. OpenAI API key не работи")
            print(f"   3. Грешка при създаване на embeddings")
        
        # Test embedding creation
        print(f"\n🧪 ТЕСТ НА EMBEDDING CREATION:")
        try:
            import openai
            openai.api_key = os.getenv("OPENAI_API_KEY")
            
            response = openai.embeddings.create(
                model="text-embedding-3-small",
                input="тест"
            )
            
            test_embedding = response.data[0].embedding
            print(f"   ✅ OpenAI API работи - embedding: {len(test_embedding)} dimensions")
            
        except Exception as e:
            print(f"   ❌ OpenAI API грешка: {e}")
            
    except Exception as e:
        print(f"❌ Грешка при проверка: {e}")

if __name__ == "__main__":
    asyncio.run(check_embeddings())
