[build-system]
requires = ["setuptools>=68.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "eu-funds-mcp-server"
version = "0.1.0"
description = "Enterprise-Grade MCP Server for EU Funding Programs"
authors = [{name = "EU Funds MCP Team", email = "<EMAIL>"}]
license = {text = "MIT"}
readme = "README.md"
requires-python = ">=3.12"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
]

dependencies = [
    # MCP Protocol
    "mcp>=1.0.0",

    # Web Framework
    "fastapi>=0.115.0",
    "uvicorn[standard]>=0.32.0",

    # Web Crawling
    "crawl4ai>=0.6.0",
    "playwright>=1.48.0",
    "beautifulsoup4>=4.12.0",

    # AI & Embeddings
    "sentence-transformers>=4.1.0",
    "openai>=1.54.0",
    "cohere>=5.11.0",

    # Database
    "supabase>=2.15.0",
    "psycopg2-binary>=2.9.0",
    "pgvector>=0.3.0",

    # Data Processing
    "pydantic>=2.10.0",
    "pydantic-settings>=2.7.0",

    # Utilities
    "python-dotenv>=1.0.0",
    "loguru>=0.7.0",
    "redis>=5.2.0",
    "httpx>=0.28.0",

    # Monitoring
    "prometheus-client>=0.22.0",

    # Async Support
    "asyncio-mqtt>=0.16.0",
    "aiofiles>=24.1.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=8.3.0",
    "pytest-asyncio>=0.24.0",
    "pytest-cov>=6.0.0",
    "black>=24.10.0",
    "ruff>=0.8.0",
    "mypy>=1.13.0",
    "pre-commit>=4.0.0",
]

test = [
    "pytest>=8.3.0",
    "pytest-asyncio>=0.24.0",
    "pytest-mock>=3.14.0",
    "httpx>=0.28.0",
    "respx>=0.22.0",
]

[project.scripts]
eu-funds-mcp = "src.mcp_server.main:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["src*"]

[tool.black]
line-length = 88
target-version = ['py312']
include = '\.pyi?$'

[tool.ruff]
target-version = "py312"
line-length = 88
select = ["E", "F", "I", "N", "W", "UP"]
ignore = ["E501", "N806"]

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
