[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "eu-funds-mcp-server"
version = "0.1.0"
description = "Enterprise-Grade MCP Server for EU Funding Programs"
authors = [
    {name = "EU Funds MCP Team", email = "<EMAIL>"},
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.12"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
]

dependencies = [
    # MCP Protocol
    "mcp>=1.0.0",
    
    # Web Crawling
    "crawl4ai>=0.6.0",
    "scrapy>=2.11.0",
    "playwright>=1.40.0",
    
    # RAG & AI
    "llama-index>=0.10.0",
    "sentence-transformers>=4.1.0",
    "openai>=1.0.0",
    "cohere>=4.0.0",
    
    # Database
    "psycopg2-binary>=2.9.0",
    "sqlalchemy>=2.0.0",
    "alembic>=1.13.0",
    "supabase>=2.0.0",
    
    # Web Framework
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    
    # Data Processing
    "unstructured>=0.11.0",
    "easyocr>=1.7.0",
    "pandas>=2.1.0",
    "numpy>=1.24.0",
    
    # Configuration & Validation
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "python-dotenv>=1.0.0",
    
    # Logging & Monitoring
    "loguru>=0.7.0",
    "prometheus-client>=0.19.0",
    
    # Async & HTTP
    "httpx>=0.25.0",
    "aiofiles>=23.2.0",
    "redis>=5.0.0",
    
    # Utilities
    "python-multipart>=0.0.6",
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "black>=23.9.0",
    "ruff>=0.1.0",
    "mypy>=1.6.0",
    "pre-commit>=3.5.0",
]

test = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "httpx>=0.25.0",
    "pytest-mock>=3.12.0",
]

performance = [
    "locust>=2.17.0",
    "memory-profiler>=0.61.0",
    "py-spy>=0.3.14",
]

[project.urls]
Homepage = "https://github.com/eu-funds/mcp-server"
Documentation = "https://docs.eufunds-mcp.com"
Repository = "https://github.com/eu-funds/mcp-server"
Issues = "https://github.com/eu-funds/mcp-server/issues"

[project.scripts]
eu-funds-mcp = "src.mcp_server.main:main"

[tool.hatch.build.targets.wheel]
packages = ["src"]

[tool.black]
line-length = 88
target-version = ['py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.ruff]
target-version = "py312"
line-length = 88
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.ruff.per-file-ignores]
"__init__.py" = ["F401"]

[tool.mypy]
python_version = "3.12"
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_return_any = true
strict_equality = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
asyncio_mode = "auto"
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "e2e: marks tests as end-to-end tests",
]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
