[project]
name = "eu-funds-mcp-server"
version = "0.1.0"
description = "Enterprise-Grade MCP Server for EU Funding Programs in Bulgaria"
readme = "README.md"
requires-python = ">=3.12"
authors = [
    {name = "EU Funds MCP Team", email = "<EMAIL>"}
]
license = {text = "MIT"}
keywords = ["mcp", "eu-funds", "rag", "crawling", "bulgaria"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
]

# Core dependencies - optimized for low RAM usage
dependencies = [
    # MCP Protocol & FastAPI
    "mcp==1.7.1",
    "fastapi==0.115.0",
    "uvicorn[standard]==0.32.0",
    
    # Web Crawling - lightweight version
    "crawl4ai==0.6.2",
    "httpx==0.28.0",
    "beautifulsoup4==4.12.3",
    
    # Database & Vector Search
    "supabase==2.15.1",
    "pgvector==0.3.6",
    
    # RAG & Embeddings - optimized models
    "sentence-transformers==4.1.0",
    "openai==1.71.0",
    "rank-bm25==0.2.2",
    
    # Configuration & Validation
    "pydantic==2.10.3",
    "pydantic-settings==2.7.0",
    "python-dotenv==1.0.1",
    
    # Utilities
    "loguru==0.7.2",
    "redis==5.2.0",
    "aiofiles==24.1.0",
    "python-multipart==0.0.12",
]

# Development dependencies
[project.optional-dependencies]
dev = [
    "pytest==8.3.4",
    "pytest-asyncio==0.24.0",
    "pytest-cov==6.0.0",
    "black==24.10.0",
    "ruff==0.8.4",
    "mypy==1.13.0",
    "pre-commit==4.0.1",
]

test = [
    "pytest==8.3.4",
    "pytest-asyncio==0.24.0",
    "pytest-mock==3.14.0",
    "httpx==0.28.0",
    "factory-boy==3.3.1",
]

monitoring = [
    "prometheus-client==0.21.0",
    "structlog==24.4.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src"]

# Code quality tools
[tool.black]
line-length = 88
target-version = ['py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.ruff]
target-version = "py312"
line-length = 88
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.ruff.per-file-ignores]
"__init__.py" = ["F401"]
"tests/**/*" = ["B011"]

[tool.mypy]
python_version = "3.12"
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_return_any = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "crawl4ai.*",
    "supabase.*",
    "pgvector.*",
    "rank_bm25.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "e2e: marks tests as end-to-end tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
