"""
Test Phase 3.3: MCP Tools Implementation
Tests all 4 MCP tools for EU Funds MCP Server with minimal test data
"""

import asyncio
import logging
import sys
from datetime import datetime

# Add src to path for imports
sys.path.append('src')

from src.mcp.tools import EUFundsMCPTools
from src.core.config import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_phase3_mcp_tools():
    """Test Phase 3.3: MCP Tools Implementation"""
    
    logger.info("🧪 Testing Phase 3.3: MCP Tools Implementation...")
    logger.info(f"📋 Configuration: {settings.embedding_model}")
    
    # Initialize MCP tools
    mcp_tools = None
    
    try:
        logger.info("🔧 Step 1: Initializing MCP Tools...")
        
        mcp_tools = EUFundsMCPTools()
        await mcp_tools.initialize()
        logger.info("✅ EU Funds MCP Tools initialized")
        
        logger.info("🔍 Step 2: Testing search_eu_funds tool...")
        
        # Test general search
        search_queries = [
            "програми за финансиране на малки предприятия",
            "селско стопанство модернизация",
            "научни изследвания иновации"
        ]
        
        for query in search_queries:
            logger.info(f"   Testing query: '{query}'")
            
            result = await mcp_tools.search_eu_funds(
                query=query,
                max_results=5,
                use_reranking=True
            )
            
            logger.info(f"   ✅ Found {result.get('total_found', 0)} results")
            logger.info(f"   Processing time: {result.get('processing_info', {}).get('total_processing_time', 0):.3f}s")
            
            # Show first result if available
            if result.get('results'):
                first_result = result['results'][0]
                logger.info(f"   Top result: '{first_result.get('title', 'No title')[:50]}...'")
                logger.info(f"   Relevance: {first_result.get('relevance_score', 0):.3f}")
        
        logger.info("🎯 Step 3: Testing get_funding_programs tool...")
        
        # Test program discovery
        program_tests = [
            {
                "sector": "селско стопанство",
                "target_group": "фермери",
                "budget_range": "средни"
            },
            {
                "sector": "иновации",
                "target_group": "МСП",
                "budget_range": "малки"
            },
            {
                "sector": "научни изследвания",
                "target_group": "университети"
            }
        ]
        
        for test_case in program_tests:
            logger.info(f"   Testing: {test_case}")
            
            result = await mcp_tools.get_funding_programs(
                sector=test_case.get("sector"),
                target_group=test_case.get("target_group"),
                budget_range=test_case.get("budget_range"),
                deadline_months=12
            )
            
            logger.info(f"   ✅ Found {result.get('total_found', 0)} programs")
            
            # Show first program if available
            if result.get('programs'):
                first_program = result['programs'][0]
                logger.info(f"   Top program: '{first_program.get('program_name', 'No name')[:50]}...'")
                logger.info(f"   Relevance: {first_program.get('relevance_score', 0):.3f}")
        
        logger.info("📋 Step 4: Testing analyze_eligibility tool...")
        
        # Test eligibility analysis
        eligibility_tests = [
            {
                "organization_type": "МСП",
                "sector": "технологии",
                "project_description": "Разработка на иновативна мобилна апликация за електронна търговия",
                "budget_needed": "50000 евро"
            },
            {
                "organization_type": "НПО",
                "sector": "образование",
                "project_description": "Програма за обучение на младежи в дигитални умения",
                "budget_needed": "25000 евро"
            },
            {
                "organization_type": "университет",
                "sector": "научни изследвания",
                "project_description": "Изследване на възобновяеми енергийни източници"
            }
        ]
        
        for test_case in eligibility_tests:
            logger.info(f"   Testing: {test_case['organization_type']} in {test_case['sector']}")
            
            result = await mcp_tools.analyze_eligibility(
                organization_type=test_case["organization_type"],
                sector=test_case["sector"],
                project_description=test_case["project_description"],
                budget_needed=test_case.get("budget_needed")
            )
            
            analysis = result.get('eligibility_analysis', {})
            logger.info(f"   ✅ Eligible: {analysis.get('organization_eligible', False)}")
            logger.info(f"   Matching programs: {len(analysis.get('matching_programs', []))}")
            logger.info(f"   Recommendations: {len(analysis.get('recommendations', []))}")
        
        logger.info("📅 Step 5: Testing get_application_deadlines tool...")
        
        # Test deadline search
        deadline_tests = [
            {
                "months_ahead": 6,
                "sector_filter": None,
                "urgent_only": False
            },
            {
                "months_ahead": 3,
                "sector_filter": "селско стопанство",
                "urgent_only": False
            },
            {
                "months_ahead": 1,
                "sector_filter": None,
                "urgent_only": True
            }
        ]
        
        for test_case in deadline_tests:
            logger.info(f"   Testing: {test_case}")
            
            result = await mcp_tools.get_application_deadlines(
                months_ahead=test_case["months_ahead"],
                sector_filter=test_case["sector_filter"],
                urgent_only=test_case["urgent_only"]
            )
            
            logger.info(f"   ✅ Found {result.get('total_found', 0)} deadlines")
            
            # Show first deadline if available
            if result.get('deadlines'):
                first_deadline = result['deadlines'][0]
                logger.info(f"   Top deadline: '{first_deadline.get('program_name', 'No name')[:50]}...'")
                logger.info(f"   Urgency: {first_deadline.get('urgency_level', 'unknown')}")
        
        logger.info("📊 Step 6: Testing tool statistics...")
        
        # Get tool statistics
        stats = mcp_tools.get_tool_stats()
        logger.info("✅ Tool Statistics:")
        
        for tool_name, tool_stats in stats.get('tool_statistics', {}).items():
            calls = tool_stats.get('calls', 0)
            avg_time = tool_stats.get('avg_time', 0)
            logger.info(f"   {tool_name}: {calls} calls, avg {avg_time:.3f}s")
        
        logger.info("🧪 Step 7: Testing error handling...")
        
        # Test error handling with invalid inputs
        try:
            result = await mcp_tools.search_eu_funds(
                query="",  # Empty query
                max_results=0  # Invalid max_results
            )
            logger.info(f"   ✅ Empty query handled: {result.get('total_found', 0)} results")
        except Exception as e:
            logger.info(f"   ✅ Error properly caught: {str(e)[:50]}...")
        
        logger.info("📋 Phase 3.3 MCP Tools Summary:")
        logger.info("   ✅ search_eu_funds: General search functionality")
        logger.info("   ✅ get_funding_programs: Program discovery by criteria")
        logger.info("   ✅ analyze_eligibility: Eligibility analysis and recommendations")
        logger.info("   ✅ get_application_deadlines: Deadline information with urgency")
        logger.info("   ✅ Tool Statistics: Performance tracking working")
        logger.info("   ✅ Error Handling: Graceful error management")
        
        logger.info("🧹 Cleaning up test resources...")
        
        # Cleanup
        if mcp_tools:
            await mcp_tools.cleanup()
        
        logger.info("✅ Cleanup complete")
        
        print("\n🎉 Phase 3.3 MCP Tools Implementation Test PASSED!")
        print("✅ All 4 MCP tools working correctly:")
        print("   - search_eu_funds: Advanced hybrid search with reranking")
        print("   - get_funding_programs: Criteria-based program discovery")
        print("   - analyze_eligibility: Smart eligibility analysis")
        print("   - get_application_deadlines: Time-sensitive deadline info")
        print("   - Performance tracking and error handling")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Phase 3.3 test failed: {e}")
        
        # Cleanup on error
        try:
            if mcp_tools:
                await mcp_tools.cleanup()
        except:
            pass
        
        print(f"\n❌ Phase 3.3 MCP Tools Implementation Test FAILED!")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_phase3_mcp_tools())
    sys.exit(0 if success else 1)
