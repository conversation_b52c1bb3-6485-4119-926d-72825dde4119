# EU Funds MCP Server

Enterprise-Grade MCP Server for EU Funding Programs in Bulgaria

## 🎯 Overview

This MCP (Model Context Protocol) server provides intelligent access to European Union funding programs information for Bulgaria. It combines advanced web crawling, vector search, and RAG (Retrieval-Augmented Generation) capabilities to democratize access to EU funding opportunities.

## ✨ Features

- **🕷️ Intelligent Web Crawling**: Crawl4AI-powered extraction from official EU funds websites
- **🔍 Hybrid Search**: Combines semantic vector search with traditional keyword search
- **🧠 RAG System**: Advanced retrieval-augmented generation for Bulgarian language
- **📊 Vector Database**: Supabase PostgreSQL with pgvector for fast similarity search
- **🚀 Production Ready**: Docker, monitoring, logging, health checks
- **💾 Memory Optimized**: Designed for low RAM usage (512MB-1GB)

## 🏗️ Architecture

```
eu-funds-mcp-server/
├── src/
│   ├── mcp_server/     # FastAPI MCP server
│   ├── crawler/        # Web crawling with Crawl4AI
│   ├── rag/           # RAG system with hybrid search
│   ├── database/      # Supabase operations
│   ├── core/          # Configuration, logging, monitoring
│   └── utils/         # Utilities and helpers
├── tests/             # Comprehensive test suite
├── docker/            # Docker configuration
├── monitoring/        # Prometheus & Grafana
└── docs/             # Documentation
```

## 🚀 Quick Start

### Prerequisites

- Python 3.12+
- Supabase account
- OpenAI API key
- Docker (optional)

### Installation

1. **Clone and setup**:
```bash
git clone <repository-url>
cd eu-funds-mcp-server
cp .env.example .env
# Edit .env with your configuration
```

2. **Install dependencies**:
```bash
pip install -e .
```

3. **Setup database**:
```bash
# Database schema is automatically created
# Configure your Supabase URL and keys in .env
```

4. **Run the server**:
```bash
python -m src.mcp_server.main
```

### Docker Setup

```bash
# Build image
docker build -f docker/Dockerfile -t eu-funds-mcp .

# Run container
docker run --env-file .env -p 8051:8051 eu-funds-mcp
```

## 📋 Configuration

Key environment variables:

```bash
# Database
DB_SUPABASE_URL=https://your-project.supabase.co
DB_SUPABASE_SERVICE_KEY=your-service-key

# RAG System  
RAG_OPENAI_API_KEY=sk-your-openai-key
RAG_EMBEDDING_MODEL=sentence-transformers/multilingual-e5-large-instruct

# Server
MCP_HOST=0.0.0.0
MCP_PORT=8051
MCP_TRANSPORT=sse

# Memory Optimization
CRAWLER_MAX_CONCURRENT_CRAWLS=2
DB_MAX_CONNECTIONS=5
```

## 🔍 API Endpoints

- `GET /` - Server information
- `GET /health` - Comprehensive health check
- `GET /health/ready` - Readiness probe
- `GET /health/live` - Liveness probe
- `GET /mcp` - MCP protocol information

## 🧪 Testing

```bash
# Run all tests
pytest

# Run specific test types
pytest tests/unit/
pytest tests/integration/
pytest tests/e2e/

# With coverage
pytest --cov=src tests/
```

## 📊 Monitoring

The server includes built-in monitoring:

- **Health Checks**: Database, Redis, system resources
- **Metrics**: Prometheus-compatible metrics (optional)
- **Logging**: Structured JSON logging with Loguru
- **Performance**: Request timing and resource usage

## 🔧 Development

### Code Quality

```bash
# Format code
black src/ tests/

# Lint code  
ruff check src/ tests/

# Type checking
mypy src/
```

### Pre-commit Hooks

```bash
pre-commit install
pre-commit run --all-files
```

## 🐳 Production Deployment

### Docker Compose

```yaml
version: '3.8'
services:
  eu-funds-mcp:
    build: .
    ports:
      - "8051:8051"
    environment:
      - ENVIRONMENT=production
    env_file:
      - .env
    healthcheck:
      test: ["CMD", "python", "health_check.py"]
      interval: 30s
      timeout: 10s
      retries: 3
```

### Kubernetes

See `k8s/` directory for Kubernetes manifests.

## 📚 Documentation

- [Architecture Guide](docs/architecture.md)
- [API Reference](docs/api.md)
- [Deployment Guide](docs/deployment.md)
- [Development Guide](docs/development.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run quality checks
6. Submit a pull request

## 📄 License

MIT License - see [LICENSE](LICENSE) file.

## 🆘 Support

- Create an issue for bugs or feature requests
- Check the [documentation](docs/) for detailed guides
- Review health check endpoints for troubleshooting

## 🎯 Roadmap

- [ ] Phase 1: Foundation & Infrastructure ✅
- [ ] Phase 2: Database & Core Models
- [ ] Phase 3: Web Crawling System  
- [ ] Phase 4: RAG Implementation
- [ ] Phase 5: MCP Server Integration
- [ ] Phase 6: Production Deployment

---

**Status**: 🚧 Phase 1 Complete - Foundation & Infrastructure Ready
