#!/usr/bin/env python3
"""
Comprehensive data quality analysis for EU Funds RAG system.
Based on industry best practices for RAG data quality assessment.
"""

import sys
import os
import asyncio
from typing import Dict, List, Any
import statistics
import re
from collections import Counter

sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.core.vector_store import VectorStore
from src.core.config import settings

class DataQualityAnalyzer:
    """Analyzes data quality based on industry standards for RAG systems."""
    
    def __init__(self):
        self.vector_store = VectorStore()
        
    async def analyze_data_quality(self) -> Dict[str, Any]:
        """Comprehensive data quality analysis."""
        print("🔍 АНАЛИЗ НА КАЧЕСТВОТО НА ДАННИТЕ В SUPABASE")
        print("=" * 60)
        
        await self.vector_store.initialize()
        
        # Get all content
        content_data = await self._get_all_content()
        
        if not content_data:
            print("❌ Няма данни в базата!")
            return {}
        
        print(f"📊 Общо документи: {len(content_data)}")
        
        # Analyze different quality metrics
        results = {
            "total_documents": len(content_data),
            "content_length_analysis": self._analyze_content_length(content_data),
            "content_diversity": self._analyze_content_diversity(content_data),
            "language_quality": self._analyze_language_quality(content_data),
            "information_density": self._analyze_information_density(content_data),
            "eu_program_coverage": self._analyze_eu_program_coverage(content_data),
            "data_freshness": self._analyze_data_freshness(content_data),
            "overall_score": 0
        }
        
        # Calculate overall quality score
        results["overall_score"] = self._calculate_overall_score(results)
        
        self._print_detailed_analysis(results)
        
        return results
    
    async def _get_all_content(self) -> List[Dict]:
        """Get all content from Supabase."""
        try:
            response = self.vector_store.supabase.table("eu_funds_content").select("*").execute()
            return response.data
        except Exception as e:
            print(f"❌ Грешка при извличане на данни: {e}")
            return []
    
    def _analyze_content_length(self, content_data: List[Dict]) -> Dict[str, Any]:
        """Analyze content length distribution."""
        lengths = [len(item.get('content', '')) for item in content_data]
        
        return {
            "min_length": min(lengths),
            "max_length": max(lengths),
            "avg_length": statistics.mean(lengths),
            "median_length": statistics.median(lengths),
            "total_chars": sum(lengths),
            "quality_score": self._score_content_length(lengths)
        }
    
    def _analyze_content_diversity(self, content_data: List[Dict]) -> Dict[str, Any]:
        """Analyze content diversity and uniqueness."""
        urls = [item.get('url', '') for item in content_data]
        titles = [item.get('title', '') for item in content_data]
        
        unique_urls = len(set(urls))
        unique_titles = len(set(titles))
        
        # Check for duplicate content
        content_hashes = []
        for item in content_data:
            content = item.get('content', '')[:500]  # First 500 chars
            content_hashes.append(hash(content))
        
        unique_content = len(set(content_hashes))
        
        return {
            "unique_urls": unique_urls,
            "unique_titles": unique_titles,
            "unique_content_snippets": unique_content,
            "diversity_ratio": unique_content / len(content_data) if content_data else 0,
            "quality_score": min(unique_content / len(content_data) * 100, 100) if content_data else 0
        }
    
    def _analyze_language_quality(self, content_data: List[Dict]) -> Dict[str, Any]:
        """Analyze Bulgarian language quality."""
        bulgarian_indicators = [
            'програма', 'финансиране', 'европейски', 'фондове', 'проект',
            'кандидатстване', 'средства', 'подкрепа', 'развитие', 'иновации'
        ]
        
        total_indicators = 0
        total_words = 0
        
        for item in content_data:
            content = item.get('content', '').lower()
            words = content.split()
            total_words += len(words)
            
            for indicator in bulgarian_indicators:
                total_indicators += content.count(indicator)
        
        indicator_density = total_indicators / total_words if total_words > 0 else 0
        
        return {
            "total_bulgarian_indicators": total_indicators,
            "total_words": total_words,
            "indicator_density": indicator_density,
            "quality_score": min(indicator_density * 1000, 100)  # Scale to 0-100
        }
    
    def _analyze_information_density(self, content_data: List[Dict]) -> Dict[str, Any]:
        """Analyze information density and usefulness."""
        high_value_terms = [
            'програма', 'финансиране', 'условия', 'изисквания', 'срок',
            'сума', 'процедура', 'кандидатстване', 'проект', 'дейности',
            'бенефициенти', 'критерии', 'документи', 'deadline'
        ]
        
        noise_indicators = [
            'cookie', 'javascript', 'navigation', 'menu', 'footer',
            'header', 'sidebar', 'advertisement', 'popup'
        ]
        
        total_value_terms = 0
        total_noise = 0
        
        for item in content_data:
            content = item.get('content', '').lower()
            
            for term in high_value_terms:
                total_value_terms += content.count(term)
            
            for noise in noise_indicators:
                total_noise += content.count(noise)
        
        signal_to_noise = total_value_terms / max(total_noise, 1)
        
        return {
            "high_value_terms": total_value_terms,
            "noise_indicators": total_noise,
            "signal_to_noise_ratio": signal_to_noise,
            "quality_score": min(signal_to_noise * 20, 100)
        }
    
    def _analyze_eu_program_coverage(self, content_data: List[Dict]) -> Dict[str, Any]:
        """Analyze coverage of EU programs."""
        eu_programs = [
            'horizon europe', 'хоризонт европа', 'ОПИК', 'ОПРР', 'ОПНОИР',
            'ERDF', 'ESF', 'cohesion fund', 'кохезионен фонд',
            'структурни фондове', 'инвестиционни фондове'
        ]
        
        program_mentions = Counter()
        
        for item in content_data:
            content = item.get('content', '').lower()
            for program in eu_programs:
                if program.lower() in content:
                    program_mentions[program] += 1
        
        coverage_ratio = len(program_mentions) / len(eu_programs)
        
        return {
            "programs_mentioned": dict(program_mentions),
            "programs_covered": len(program_mentions),
            "total_programs_checked": len(eu_programs),
            "coverage_ratio": coverage_ratio,
            "quality_score": coverage_ratio * 100
        }
    
    def _analyze_data_freshness(self, content_data: List[Dict]) -> Dict[str, Any]:
        """Analyze data freshness and relevance."""
        current_year_mentions = 0
        recent_year_mentions = 0
        
        for item in content_data:
            content = item.get('content', '')
            
            # Check for current year (2025)
            if '2025' in content:
                current_year_mentions += 1
            
            # Check for recent years (2023-2025)
            if any(year in content for year in ['2023', '2024', '2025']):
                recent_year_mentions += 1
        
        freshness_ratio = recent_year_mentions / len(content_data) if content_data else 0
        
        return {
            "current_year_mentions": current_year_mentions,
            "recent_year_mentions": recent_year_mentions,
            "freshness_ratio": freshness_ratio,
            "quality_score": freshness_ratio * 100
        }
    
    def _score_content_length(self, lengths: List[int]) -> float:
        """Score content length quality (optimal range: 500-5000 chars)."""
        optimal_count = sum(1 for length in lengths if 500 <= length <= 5000)
        return (optimal_count / len(lengths) * 100) if lengths else 0
    
    def _calculate_overall_score(self, results: Dict[str, Any]) -> float:
        """Calculate weighted overall quality score."""
        weights = {
            "content_length_analysis": 0.15,
            "content_diversity": 0.20,
            "language_quality": 0.20,
            "information_density": 0.25,
            "eu_program_coverage": 0.15,
            "data_freshness": 0.05
        }
        
        total_score = 0
        for metric, weight in weights.items():
            if metric in results and "quality_score" in results[metric]:
                total_score += results[metric]["quality_score"] * weight
        
        return round(total_score, 1)
    
    def _print_detailed_analysis(self, results: Dict[str, Any]):
        """Print detailed analysis results."""
        print("\n📊 ДЕТАЙЛЕН АНАЛИЗ НА КАЧЕСТВОТО:")
        print("-" * 50)
        
        # Content Length
        length_data = results["content_length_analysis"]
        print(f"📏 ДЪЛЖИНА НА СЪДЪРЖАНИЕТО:")
        print(f"   • Минимум: {length_data['min_length']} символа")
        print(f"   • Максимум: {length_data['max_length']} символа")
        print(f"   • Средно: {length_data['avg_length']:.0f} символа")
        print(f"   • Общо: {length_data['total_chars']} символа")
        print(f"   • Оценка: {length_data['quality_score']:.1f}/100")
        
        # Content Diversity
        diversity_data = results["content_diversity"]
        print(f"\n🎯 РАЗНООБРАЗИЕ НА СЪДЪРЖАНИЕТО:")
        print(f"   • Уникални URL: {diversity_data['unique_urls']}")
        print(f"   • Уникални заглавия: {diversity_data['unique_titles']}")
        print(f"   • Уникално съдържание: {diversity_data['unique_content_snippets']}")
        print(f"   • Коефициент на разнообразие: {diversity_data['diversity_ratio']:.2f}")
        print(f"   • Оценка: {diversity_data['quality_score']:.1f}/100")
        
        # Language Quality
        lang_data = results["language_quality"]
        print(f"\n🇧🇬 КАЧЕСТВО НА БЪЛГАРСКИЯ ЕЗИК:")
        print(f"   • Български индикатори: {lang_data['total_bulgarian_indicators']}")
        print(f"   • Общо думи: {lang_data['total_words']}")
        print(f"   • Плътност на индикатори: {lang_data['indicator_density']:.4f}")
        print(f"   • Оценка: {lang_data['quality_score']:.1f}/100")
        
        # Information Density
        info_data = results["information_density"]
        print(f"\n💎 ПЛЪТНОСТ НА ИНФОРМАЦИЯТА:")
        print(f"   • Ценни термини: {info_data['high_value_terms']}")
        print(f"   • Шум: {info_data['noise_indicators']}")
        print(f"   • Съотношение сигнал/шум: {info_data['signal_to_noise_ratio']:.2f}")
        print(f"   • Оценка: {info_data['quality_score']:.1f}/100")
        
        # EU Program Coverage
        program_data = results["eu_program_coverage"]
        print(f"\n🇪🇺 ПОКРИТИЕ НА ЕС ПРОГРАМИ:")
        print(f"   • Споменати програми: {program_data['programs_covered']}/{program_data['total_programs_checked']}")
        print(f"   • Коефициент на покритие: {program_data['coverage_ratio']:.2f}")
        print(f"   • Оценка: {program_data['quality_score']:.1f}/100")
        
        # Data Freshness
        fresh_data = results["data_freshness"]
        print(f"\n📅 АКТУАЛНОСТ НА ДАННИТЕ:")
        print(f"   • Споменавания на 2025: {fresh_data['current_year_mentions']}")
        print(f"   • Споменавания на 2023-2025: {fresh_data['recent_year_mentions']}")
        print(f"   • Коефициент на актуалност: {fresh_data['freshness_ratio']:.2f}")
        print(f"   • Оценка: {fresh_data['quality_score']:.1f}/100")
        
        # Overall Score
        overall = results["overall_score"]
        print(f"\n🎯 ОБЩА ОЦЕНКА: {overall}/100")
        
        if overall >= 80:
            print("🟢 ОТЛИЧНО КАЧЕСТВО - готово за production RAG система")
        elif overall >= 60:
            print("🟡 ДОБРО КАЧЕСТВО - препоръчват се малки подобрения")
        elif overall >= 40:
            print("🟠 СРЕДНО КАЧЕСТВО - нужни са значителни подобрения")
        else:
            print("🔴 НИСКО КАЧЕСТВО - необходимо е преработване на данните")

async def main():
    """Main analysis function."""
    analyzer = DataQualityAnalyzer()
    await analyzer.analyze_data_quality()

if __name__ == "__main__":
    asyncio.run(main())
