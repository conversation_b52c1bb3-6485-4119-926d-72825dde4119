"""
WORKING EXAMPLE: Pydantic Settings Configuration
This example shows the EXACT pattern for handling environment variables with Pydantic v2.
CRITICAL: This pattern SOLVES the comma-separated list parsing issue.
"""

from typing import List, Optional, Annotated
from pydantic import Field, field_validator, BeforeValidator
from pydantic_settings import BaseSettings, SettingsConfigDict
import os
from pathlib import Path


def parse_comma_separated_list(v) -> List[str]:
    """
    CRITICAL FUNCTION: Parse comma-separated string into list.
    This is the EXACT solution for environment variable list parsing.
    """
    if isinstance(v, str):
        # Split by comma and strip whitespace
        items = [item.strip() for item in v.split(',') if item.strip()]
        return items
    elif isinstance(v, list):
        return v
    return []


def parse_comma_separated_int_list(v) -> List[int]:
    """Parse comma-separated string into list of integers."""
    if isinstance(v, str):
        items = [int(item.strip()) for item in v.split(',') if item.strip()]
        return items
    elif isinstance(v, list):
        return [int(item) for item in v]
    return []


class DatabaseConfig(BaseSettings):
    """Database configuration section."""
    
    # Supabase configuration
    supabase_url: str = Field(..., description="Supabase project URL")
    supabase_key: str = Field(..., description="Supabase service role key")
    
    # PostgreSQL configuration
    max_connections: int = Field(default=20, description="Maximum database connections")
    connection_timeout: int = Field(default=30, description="Connection timeout in seconds")
    
    # pgvector configuration
    vector_dimensions: int = Field(default=1024, description="Vector dimensions for embeddings")
    hnsw_m: int = Field(default=16, description="HNSW index parameter m")
    hnsw_ef_construction: int = Field(default=64, description="HNSW index parameter ef_construction")


class CrawlerConfig(BaseSettings):
    """Crawler configuration section."""
    
    # Target domains - CRITICAL: Use the custom validator
    crawl_domains: Annotated[List[str], BeforeValidator(parse_comma_separated_list)] = Field(
        default=["eufunds.bg", "opic.bg", "esif.bg"],
        description="Domains to crawl for EU funds information"
    )
    
    # Crawl4AI configuration
    max_concurrent_crawls: int = Field(default=5, description="Maximum concurrent crawl operations")
    crawl_timeout: int = Field(default=60, description="Crawl timeout in seconds")
    browser_pool_size: int = Field(default=3, description="Browser pool size for Crawl4AI")
    
    # Content filtering
    min_content_length: int = Field(default=100, description="Minimum content length to process")
    max_content_length: int = Field(default=50000, description="Maximum content length to process")


class EmbeddingConfig(BaseSettings):
    """Embedding and RAG configuration section."""
    
    # OpenAI configuration
    openai_api_key: str = Field(..., description="OpenAI API key for embeddings")
    embedding_model: str = Field(
        default="text-embedding-3-large", 
        description="OpenAI embedding model"
    )
    
    # Cohere configuration
    cohere_api_key: str = Field(..., description="Cohere API key for reranking")
    rerank_model: str = Field(
        default="rerank-multilingual-v3.0",
        description="Cohere rerank model"
    )
    
    # Search configuration
    max_search_results: int = Field(default=20, description="Maximum search results to return")
    similarity_threshold: float = Field(default=0.7, description="Similarity threshold for vector search")
    
    # Hybrid search weights
    semantic_weight: float = Field(default=0.7, description="Weight for semantic search")
    keyword_weight: float = Field(default=0.3, description="Weight for keyword search")


class MCPConfig(BaseSettings):
    """MCP server configuration section."""
    
    # Server configuration
    host: str = Field(default="localhost", description="MCP server host")
    port: int = Field(default=8000, description="MCP server port")
    workers: int = Field(default=1, description="Number of worker processes")
    
    # Security
    allowed_origins: Annotated[List[str], BeforeValidator(parse_comma_separated_list)] = Field(
        default=["*"],
        description="Allowed CORS origins"
    )
    
    # Performance
    request_timeout: int = Field(default=30, description="Request timeout in seconds")
    max_request_size: int = Field(default=10485760, description="Maximum request size in bytes")


class Settings(BaseSettings):
    """
    MAIN SETTINGS CLASS - Use this pattern for all configuration.
    
    CRITICAL FEATURES:
    1. Nested configuration sections
    2. Proper environment variable handling
    3. Custom validators for complex types
    4. Comprehensive field descriptions
    """
    
    # Environment
    environment: str = Field(default="development", description="Environment (development/production)")
    debug: bool = Field(default=False, description="Enable debug mode")
    
    # Logging
    log_level: str = Field(default="INFO", description="Logging level")
    log_format: str = Field(default="json", description="Logging format (json/text)")
    
    # Configuration sections
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    crawler: CrawlerConfig = Field(default_factory=CrawlerConfig)
    embedding: EmbeddingConfig = Field(default_factory=EmbeddingConfig)
    mcp: MCPConfig = Field(default_factory=MCPConfig)
    
    # Model configuration
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        env_nested_delimiter="__",  # Allows DATABASE__SUPABASE_URL format
        extra="ignore"  # Ignore extra environment variables
    )
    
    @field_validator('environment')
    @classmethod
    def validate_environment(cls, v):
        """Validate environment value."""
        allowed = ['development', 'staging', 'production']
        if v not in allowed:
            raise ValueError(f'Environment must be one of: {allowed}')
        return v
    
    @field_validator('log_level')
    @classmethod
    def validate_log_level(cls, v):
        """Validate log level."""
        allowed = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in allowed:
            raise ValueError(f'Log level must be one of: {allowed}')
        return v.upper()


# USAGE EXAMPLE
def load_settings() -> Settings:
    """
    Load settings from environment variables.
    This is the EXACT function to use in your application.
    """
    try:
        settings = Settings()
        return settings
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        raise


# TEST FUNCTION
def test_configuration():
    """Test configuration loading - use this pattern for validation."""
    try:
        settings = load_settings()
        
        print("✅ Configuration loaded successfully!")
        print(f"Environment: {settings.environment}")
        print(f"Debug mode: {settings.debug}")
        print(f"Crawl domains: {settings.crawler.crawl_domains}")
        print(f"Database URL: {settings.database.supabase_url[:20]}...")
        print(f"Vector dimensions: {settings.database.vector_dimensions}")
        
        return True
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


if __name__ == "__main__":
    # Test the configuration
    success = test_configuration()
    if success:
        print("🎉 Configuration example working correctly!")
    else:
        print("💥 Configuration example failed!")
