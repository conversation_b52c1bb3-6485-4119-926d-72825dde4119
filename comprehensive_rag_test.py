#!/usr/bin/env python3
"""
Comprehensive RAG test based on mcp-crawl4ai-rag approach.
Tests the system with realistic EU funds questions and measures accuracy.
"""

import asyncio
import sys
import os
from datetime import datetime
from typing import List, Dict, Any, Tuple
import json
from dataclasses import dataclass

sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.core.hybrid_search import HybridSearchEngine
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

@dataclass
class TestQuestion:
    """Test question with expected criteria."""
    question: str
    category: str
    expected_keywords: List[str]
    difficulty: str
    min_score_threshold: float = 0.6

class ComprehensiveRAGTester:
    """Comprehensive RAG tester based on mcp-crawl4ai-rag approach."""
    
    def __init__(self):
        self.search_engine = HybridSearchEngine()
        
        # Initialize Supabase client
        url = os.getenv("SUPABASE_URL")
        key = os.getenv("SUPABASE_SERVICE_KEY") or os.getenv("SUPABASE_KEY")
        self.supabase: Client = create_client(url, key)
        
        # Comprehensive test questions for EU funds
        self.test_questions = [
            TestQuestion(
                question="Какво финансиране има за малки и средни предприятия?",
                category="funding_sme",
                expected_keywords=["МСП", "малки", "средни", "предприятия", "финансиране", "подкрепа"],
                difficulty="easy",
                min_score_threshold=0.7
            ),
            TestQuestion(
                question="Какви са условията за кандидатстване по ОПИК?",
                category="opik_conditions",
                expected_keywords=["ОПИК", "условия", "кандидатстване", "критерии", "изисквания"],
                difficulty="medium",
                min_score_threshold=0.6
            ),
            TestQuestion(
                question="Колко е максималният размер на проекта за иновации?",
                category="innovation_funding",
                expected_keywords=["максимален", "размер", "проект", "иновации", "сума", "лева"],
                difficulty="hard",
                min_score_threshold=0.5
            ),
            TestQuestion(
                question="Кои програми финансират научни изследвания?",
                category="research_programs",
                expected_keywords=["програми", "научни", "изследвания", "ОПНОИР", "наука"],
                difficulty="medium",
                min_score_threshold=0.6
            ),
            TestQuestion(
                question="Как да кандидатствам за европейско финансиране?",
                category="application_process",
                expected_keywords=["кандидатстване", "процедура", "документи", "заявление", "стъпки"],
                difficulty="easy",
                min_score_threshold=0.7
            ),
            TestQuestion(
                question="Какви са сроковете за изпълнение на проектите?",
                category="project_timelines",
                expected_keywords=["срокове", "изпълнение", "проекти", "месеца", "години"],
                difficulty="medium",
                min_score_threshold=0.6
            ),
            TestQuestion(
                question="Има ли финансиране за дигитализация на МСП?",
                category="digitalization",
                expected_keywords=["дигитализация", "МСП", "технологии", "цифрови", "трансформация"],
                difficulty="hard",
                min_score_threshold=0.5
            ),
            TestQuestion(
                question="Какви документи са нужни за кандидатстване?",
                category="required_documents",
                expected_keywords=["документи", "кандидатстване", "заявление", "приложения", "удостоверения"],
                difficulty="easy",
                min_score_threshold=0.7
            ),
            TestQuestion(
                question="Кой може да кандидатства по програмите за регионално развитие?",
                category="regional_eligibility",
                expected_keywords=["кандидатства", "регионално", "развитие", "общини", "организации"],
                difficulty="medium",
                min_score_threshold=0.6
            ),
            TestQuestion(
                question="Какъв е процентът на съфинансиране от ЕС?",
                category="cofinancing_rates",
                expected_keywords=["процент", "съфинансиране", "ЕС", "национално", "85%", "80%"],
                difficulty="hard",
                min_score_threshold=0.5
            ),
            # Additional specific questions
            TestQuestion(
                question="Какви са приоритетите на ОПРР?",
                category="oprr_priorities",
                expected_keywords=["ОПРР", "приоритети", "регионално", "развитие", "инфраструктура"],
                difficulty="medium",
                min_score_threshold=0.6
            ),
            TestQuestion(
                question="Как се подава заявление за финансиране?",
                category="application_submission",
                expected_keywords=["заявление", "подаване", "финансиране", "система", "онлайн"],
                difficulty="easy",
                min_score_threshold=0.7
            ),
        ]
    
    async def run_comprehensive_test(self) -> Dict[str, Any]:
        """Run comprehensive RAG test."""
        print("🧪 СТАРТИРАНЕ НА COMPREHENSIVE RAG TEST")
        print("=" * 60)
        
        start_time = datetime.now()
        
        # Initialize search engine
        await self.search_engine.initialize()
        
        # Check data availability
        data_count = await self._get_data_count()
        print(f"📊 Налични данни: {data_count} записа")
        
        if data_count < 10:
            return {
                "success": False,
                "error": "insufficient_data",
                "data_count": data_count,
                "message": "Недостатъчно данни за качествен тест. Нужни поне 10 записа."
            }
        
        print(f"✅ Достатъчно данни за тестване")
        print(f"🎯 Тестови въпроси: {len(self.test_questions)}")
        print()
        
        # Run tests
        test_results = []
        total_score = 0
        passed_tests = 0
        
        for i, test_case in enumerate(self.test_questions):
            print(f"📝 Тест {i+1}/{len(self.test_questions)}: {test_case.question}")
            
            try:
                result = await self._test_single_question(test_case)
                test_results.append(result)
                total_score += result["score"]
                
                # Check if test passed
                if result["score"] >= test_case.min_score_threshold:
                    passed_tests += 1
                    status = "✅"
                elif result["score"] >= 0.4:
                    status = "⚠️"
                else:
                    status = "❌"
                
                print(f"   {status} Резултат: {result['score']:.1%} (праг: {test_case.min_score_threshold:.1%})")
                
            except Exception as e:
                print(f"   ❌ Грешка: {e}")
                test_results.append({
                    "question": test_case.question,
                    "category": test_case.category,
                    "error": str(e),
                    "score": 0,
                    "passed": False
                })
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # Calculate metrics
        avg_score = total_score / len(self.test_questions) if self.test_questions else 0
        pass_rate = passed_tests / len(self.test_questions) if self.test_questions else 0
        
        # Generate comprehensive report
        report = {
            "success": True,
            "test_summary": {
                "total_questions": len(self.test_questions),
                "passed_tests": passed_tests,
                "pass_rate": pass_rate,
                "average_score": avg_score,
                "duration_seconds": duration,
                "data_count": data_count,
                "timestamp": start_time.isoformat()
            },
            "detailed_results": test_results,
            "performance_analysis": self._analyze_performance(test_results),
            "recommendations": self._generate_recommendations(avg_score, pass_rate, test_results)
        }
        
        return report
    
    async def _get_data_count(self) -> int:
        """Get count of available data."""
        try:
            result = self.supabase.table('eu_funds_content').select("id", count="exact").execute()
            return result.count if hasattr(result, 'count') else len(result.data)
        except Exception as e:
            print(f"❌ Грешка при проверка на данните: {e}")
            return 0
    
    async def _test_single_question(self, test_case: TestQuestion) -> Dict[str, Any]:
        """Test a single question."""
        try:
            # Search for answer
            search_results = await self.search_engine.search(
                query=test_case.question,
                limit=5
            )
            
            if not search_results or not search_results.results:
                return {
                    "question": test_case.question,
                    "category": test_case.category,
                    "answer": "Няма намерени резултати",
                    "score": 0,
                    "passed": False,
                    "details": "no_results",
                    "similarity_scores": [],
                    "response_time": 0
                }
            
            # Evaluate answer quality
            score_details = self._evaluate_answer_quality(search_results, test_case)
            passed = score_details["total_score"] >= test_case.min_score_threshold
            
            return {
                "question": test_case.question,
                "category": test_case.category,
                "answer": search_results.results[0].content[:300] + "..." if len(search_results.results[0].content) > 300 else search_results.results[0].content,
                "score": score_details["total_score"],
                "passed": passed,
                "details": score_details["breakdown"],
                "similarity_scores": [r.similarity_score for r in search_results.results[:3]],
                "response_time": search_results.response_time,
                "num_results": len(search_results.results)
            }
            
        except Exception as e:
            return {
                "question": test_case.question,
                "category": test_case.category,
                "error": str(e),
                "score": 0,
                "passed": False
            }
    
    def _evaluate_answer_quality(self, search_results, test_case: TestQuestion) -> Dict[str, Any]:
        """Evaluate answer quality based on multiple criteria."""
        scores = {
            "relevance": 0,
            "keyword_match": 0,
            "similarity": 0,
            "completeness": 0
        }
        
        if not search_results.results:
            return {"total_score": 0, "breakdown": "no_results"}
        
        top_result = search_results.results[0]
        all_content = " ".join([r.content.lower() for r in search_results.results[:3]])
        
        # 1. Similarity score (30%)
        scores["similarity"] = min(top_result.similarity_score, 1.0) * 0.3
        
        # 2. Keyword matching (40%)
        expected_keywords = test_case.expected_keywords
        if expected_keywords:
            matched_keywords = sum(1 for keyword in expected_keywords if keyword.lower() in all_content)
            scores["keyword_match"] = (matched_keywords / len(expected_keywords)) * 0.4
        
        # 3. Category relevance (20%)
        category_relevance = self._check_category_relevance(all_content, test_case.category)
        scores["relevance"] = category_relevance * 0.2
        
        # 4. Content completeness (10%)
        content_length = len(all_content)
        scores["completeness"] = min(content_length / 1000, 1.0) * 0.1
        
        total_score = sum(scores.values())
        
        return {
            "total_score": total_score,
            "breakdown": f"sim:{scores['similarity']:.2f} kw:{scores['keyword_match']:.2f} rel:{scores['relevance']:.2f} comp:{scores['completeness']:.2f}"
        }
    
    def _check_category_relevance(self, content: str, category: str) -> float:
        """Check relevance to specific category."""
        category_keywords = {
            "funding_sme": ["МСП", "малки", "средни", "предприятия", "бизнес"],
            "opik_conditions": ["ОПИК", "условия", "критерии", "изисквания"],
            "innovation_funding": ["иновации", "технологии", "развитие", "модернизация"],
            "research_programs": ["изследвания", "наука", "ОПНОИР", "научни"],
            "application_process": ["кандидатстване", "процедура", "заявление"],
            "project_timelines": ["срокове", "време", "изпълнение", "график"],
            "digitalization": ["дигитализация", "цифрови", "технологии", "IT"],
            "required_documents": ["документи", "заявление", "приложения"],
            "regional_eligibility": ["регионално", "развитие", "общини", "територии"],
            "cofinancing_rates": ["съфинансиране", "процент", "национално"],
            "oprr_priorities": ["ОПРР", "приоритети", "регионално"],
            "application_submission": ["подаване", "заявление", "система"]
        }
        
        relevant_keywords = category_keywords.get(category, [])
        if not relevant_keywords:
            return 0.5  # Default relevance
        
        matches = sum(1 for keyword in relevant_keywords if keyword.lower() in content)
        return min(matches / len(relevant_keywords), 1.0)
    
    def _analyze_performance(self, results: List[Dict]) -> Dict[str, Any]:
        """Analyze performance patterns."""
        if not results:
            return {}
        
        # Group by category
        by_category = {}
        by_difficulty = {}
        
        for result in results:
            category = result.get("category", "unknown")
            if category not in by_category:
                by_category[category] = []
            by_category[category].append(result["score"])
            
            # Find difficulty from test questions
            difficulty = "unknown"
            for tq in self.test_questions:
                if tq.question == result.get("question"):
                    difficulty = tq.difficulty
                    break
            
            if difficulty not in by_difficulty:
                by_difficulty[difficulty] = []
            by_difficulty[difficulty].append(result["score"])
        
        # Calculate averages
        category_performance = {
            cat: sum(scores) / len(scores) for cat, scores in by_category.items()
        }
        
        difficulty_performance = {
            diff: sum(scores) / len(scores) for diff, scores in by_difficulty.items()
        }
        
        return {
            "category_performance": category_performance,
            "difficulty_performance": difficulty_performance,
            "best_category": max(category_performance.items(), key=lambda x: x[1]) if category_performance else None,
            "worst_category": min(category_performance.items(), key=lambda x: x[1]) if category_performance else None,
            "best_difficulty": max(difficulty_performance.items(), key=lambda x: x[1]) if difficulty_performance else None,
            "worst_difficulty": min(difficulty_performance.items(), key=lambda x: x[1]) if difficulty_performance else None
        }
    
    def _generate_recommendations(self, avg_score: float, pass_rate: float, results: List[Dict]) -> List[str]:
        """Generate recommendations based on test results."""
        recommendations = []
        
        # Overall performance
        if avg_score >= 0.85 and pass_rate >= 0.8:
            recommendations.append("🎉 Отлична производителност! Системата е готова за production")
        elif avg_score >= 0.70 and pass_rate >= 0.6:
            recommendations.append("✅ Добра производителност, подходяща за реално използване")
        elif avg_score >= 0.50 and pass_rate >= 0.4:
            recommendations.append("⚠️ Средна производителност, нужни подобрения")
        else:
            recommendations.append("❌ Ниска производителност, нужни значителни подобрения")
        
        # Specific issues
        failed_tests = [r for r in results if not r.get("passed", False)]
        if len(failed_tests) > len(results) * 0.5:
            recommendations.append("Много неуспешни тестове - проверете качеството и количеството на данните")
        
        no_results = [r for r in results if "no_results" in str(r.get("details", ""))]
        if no_results:
            recommendations.append(f"{len(no_results)} въпроса без резултати - нужно повече релевантни данни")
        
        # Pass rate specific
        if pass_rate < 0.5:
            recommendations.append("Ниска успеваемост - разгледайте увеличаване на данните или подобряване на chunking")
        
        return recommendations
    
    def print_comprehensive_report(self, report: Dict[str, Any]):
        """Print detailed test report."""
        if not report.get("success"):
            print(f"❌ ТЕСТ НЕУСПЕШЕН: {report.get('error', 'Unknown error')}")
            if "message" in report:
                print(f"   {report['message']}")
            return
        
        summary = report["test_summary"]
        
        print(f"\n🎯 ФИНАЛЕН РЕЗУЛТАТ")
        print("=" * 50)
        print(f"📊 Обща точност: {summary['average_score']:.1%}")
        print(f"✅ Успешни тестове: {summary['passed_tests']}/{summary['total_questions']} ({summary['pass_rate']:.1%})")
        print(f"⏱️ Общо време: {summary['duration_seconds']:.1f}s")
        print(f"💾 Използвани данни: {summary['data_count']} записа")
        
        # Performance analysis
        if "performance_analysis" in report:
            analysis = report["performance_analysis"]
            
            if "category_performance" in analysis:
                print(f"\n📋 РЕЗУЛТАТИ ПО КАТЕГОРИИ:")
                for category, score in analysis["category_performance"].items():
                    status = "✅" if score >= 0.7 else "⚠️" if score >= 0.5 else "❌"
                    print(f"   {status} {category}: {score:.1%}")
            
            if "difficulty_performance" in analysis:
                print(f"\n🎚️ РЕЗУЛТАТИ ПО ТРУДНОСТ:")
                for difficulty, score in analysis["difficulty_performance"].items():
                    status = "✅" if score >= 0.7 else "⚠️" if score >= 0.5 else "❌"
                    print(f"   {status} {difficulty}: {score:.1%}")
        
        # Recommendations
        if report.get("recommendations"):
            print(f"\n💡 ПРЕПОРЪКИ:")
            for rec in report["recommendations"]:
                print(f"   • {rec}")

async def main():
    """Main function."""
    tester = ComprehensiveRAGTester()
    report = await tester.run_comprehensive_test()
    tester.print_comprehensive_report(report)
    
    # Save detailed report
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    with open(f"comprehensive_rag_test_{timestamp}.json", "w", encoding="utf-8") as f:
        json.dump(report, f, ensure_ascii=False, indent=2, default=str)
    
    return report

if __name__ == "__main__":
    asyncio.run(main())
