#!/usr/bin/env python3
"""
Debug search functionality to understand why RAG test returns 0%.
"""

import asyncio
import sys
import os
from supabase import create_client, Client
from dotenv import load_dotenv

sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Load environment variables
load_dotenv()

async def debug_search():
    """Debug search functionality."""
    print("🔍 DEBUG SEARCH FUNCTIONALITY")
    print("=" * 50)
    
    # Initialize Supabase client
    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_SERVICE_KEY") or os.getenv("SUPABASE_KEY")
    supabase: Client = create_client(url, key)
    
    # 1. Check data in eu_funds_content
    print("📊 ПРОВЕРКА НА ДАННИТЕ В eu_funds_content:")
    try:
        result = supabase.table('eu_funds_content').select("*").limit(3).execute()
        print(f"   Записи: {len(result.data)}")
        
        for i, record in enumerate(result.data[:2]):
            print(f"\n   Запис {i+1}:")
            print(f"     ID: {record.get('id')}")
            print(f"     Title: {record.get('title', 'N/A')}")
            print(f"     Content length: {len(record.get('content', ''))} символа")
            print(f"     Source URL: {record.get('source_url', 'N/A')}")
            print(f"     Content preview: {record.get('content', '')[:100]}...")
            
    except Exception as e:
        print(f"   ❌ Грешка: {e}")
    
    # 2. Try to use hybrid search engine
    print(f"\n🔍 ТЕСТ НА HYBRID SEARCH ENGINE:")
    try:
        from src.core.hybrid_search import HybridSearchEngine
        
        search_engine = HybridSearchEngine()
        await search_engine.initialize()
        
        # Test simple search
        test_query = "програма"
        print(f"   Търсене за: '{test_query}'")
        
        results = await search_engine.search(query=test_query, limit=3)
        
        if results and results.results:
            print(f"   ✅ Намерени {len(results.results)} резултата")
            for i, result in enumerate(results.results[:2]):
                print(f"     Резултат {i+1}:")
                print(f"       Similarity: {result.similarity_score:.3f}")
                print(f"       Content: {result.content[:100]}...")
        else:
            print(f"   ❌ Няма намерени резултати")
            
    except Exception as e:
        print(f"   ❌ Грешка при hybrid search: {e}")
        import traceback
        traceback.print_exc()
    
    # 3. Try direct vector search
    print(f"\n🔍 ТЕСТ НА DIRECT VECTOR SEARCH:")
    try:
        from src.core.vector_store import VectorStore
        
        vector_store = VectorStore()
        await vector_store.initialize()
        
        # Test search
        test_query = "програма"
        print(f"   Търсене за: '{test_query}'")
        
        # Try to search directly
        search_results = await vector_store.search(query=test_query, limit=3)
        
        if search_results and search_results.results:
            print(f"   ✅ Намерени {len(search_results.results)} резултата")
            for i, result in enumerate(search_results.results[:2]):
                print(f"     Резултат {i+1}:")
                print(f"       Similarity: {result.similarity_score:.3f}")
                print(f"       Content: {result.content[:100]}...")
        else:
            print(f"   ❌ Няма намерени резултати от vector store")
            
    except Exception as e:
        print(f"   ❌ Грешка при vector search: {e}")
        import traceback
        traceback.print_exc()
    
    # 4. Check if embeddings exist
    print(f"\n🔍 ПРОВЕРКА НА EMBEDDINGS:")
    try:
        # Check if records have embeddings (this might not work if embeddings are in different table)
        result = supabase.table('eu_funds_content').select("id, metadata").limit(5).execute()
        
        print(f"   Проверка на {len(result.data)} записа за embeddings...")
        
        for record in result.data[:3]:
            metadata = record.get('metadata', {})
            print(f"     ID {record['id']}: metadata keys = {list(metadata.keys())}")
            
    except Exception as e:
        print(f"   ❌ Грешка при проверка на embeddings: {e}")
    
    # 5. Check table structure
    print(f"\n🔍 ПРОВЕРКА НА СТРУКТУРАТА НА ТАБЛИЦАТА:")
    try:
        # Get first record to see structure
        result = supabase.table('eu_funds_content').select("*").limit(1).execute()
        
        if result.data:
            record = result.data[0]
            print(f"   Колони в таблицата: {list(record.keys())}")
        else:
            print(f"   ❌ Няма данни в таблицата")
            
    except Exception as e:
        print(f"   ❌ Грешка при проверка на структурата: {e}")

if __name__ == "__main__":
    asyncio.run(debug_search())
