<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_db813965.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">47%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.2">coverage.py v7.9.2</a>,
            created at 2025-07-07 20:48 +0300
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6___init___py.html">src\__init__.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41___init___py.html">src\core\__init__.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_config_py.html#t13">src\core\config.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_config_py.html#t13"><data value='Settings'>Settings</data></a></td>
                <td>31</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="17 31">55%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_config_py.html#t161">src\core\config.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_config_py.html#t161"><data value='Config'>Settings.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_config_py.html">src\core\config.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>65</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="65 65">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_crawler_py.html#t25">src\core\crawler.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_crawler_py.html#t25"><data value='EUFundsCrawler'>EUFundsCrawler</data></a></td>
                <td>115</td>
                <td>115</td>
                <td>0</td>
                <td class="right" data-ratio="0 115">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_crawler_py.html">src\core\crawler.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_crawler_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_database_py.html#t24">src\core\database.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_database_py.html#t24"><data value='DatabaseManager'>DatabaseManager</data></a></td>
                <td>80</td>
                <td>73</td>
                <td>0</td>
                <td class="right" data-ratio="7 80">9%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_database_py.html">src\core\database.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_database_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>26</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="26 26">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_embeddings_py.html#t25">src\core\embeddings.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_embeddings_py.html#t25"><data value='EmbeddingProcessor'>EmbeddingProcessor</data></a></td>
                <td>178</td>
                <td>65</td>
                <td>0</td>
                <td class="right" data-ratio="113 178">63%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_embeddings_py.html">src\core\embeddings.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_embeddings_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_hybrid_search_py.html#t24">src\core\hybrid_search.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_hybrid_search_py.html#t24"><data value='HybridSearchEngine'>HybridSearchEngine</data></a></td>
                <td>129</td>
                <td>129</td>
                <td>0</td>
                <td class="right" data-ratio="0 129">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_hybrid_search_py.html">src\core\hybrid_search.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_hybrid_search_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t12">src\core\models.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t12"><data value='SearchRequest'>SearchRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t18">src\core\models.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t18"><data value='SearchResult'>SearchResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t26">src\core\models.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t26"><data value='FundingProgram'>FundingProgram</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t35">src\core\models.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t35"><data value='EligibilityAnalysis'>EligibilityAnalysis</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t46">src\core\models.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t46"><data value='DeadlineInfo'>DeadlineInfo</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t54">src\core\models.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t54"><data value='DataSource'>DataSource</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t63">src\core\models.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t63"><data value='HealthStatus'>HealthStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t70">src\core\models.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t70"><data value='ErrorResponse'>ErrorResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t75">src\core\models.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t75"><data value='CrawlResult'>CrawlResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t87">src\core\models.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t87"><data value='ContentMetadata'>ContentMetadata</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t101">src\core\models.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t101"><data value='ProcessedText'>ProcessedText</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t112">src\core\models.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t112"><data value='ProcessingResult'>ProcessingResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t122">src\core\models.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t122"><data value='LanguageMetrics'>LanguageMetrics</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t133">src\core\models.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t133"><data value='TextChunk'>TextChunk</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t145">src\core\models.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t145"><data value='CrawledContent'>CrawledContent</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t157">src\core\models.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t157"><data value='EmbeddingResult'>EmbeddingResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t170">src\core\models.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t170"><data value='BatchEmbeddingResult'>BatchEmbeddingResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t183">src\core\models.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t183"><data value='EmbeddingMetrics'>EmbeddingMetrics</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t195">src\core\models.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t195"><data value='VectorSearchResult'>VectorSearchResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t204">src\core\models.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t204"><data value='HybridSearchResult'>HybridSearchResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t214">src\core\models.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t214"><data value='SearchMetrics'>SearchMetrics</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t223">src\core\models.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t223"><data value='Document'>Document</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t232">src\core\models.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t232"><data value='EmbeddingVector'>EmbeddingVector</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t242">src\core\models.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t242"><data value='BulgarianTextMetadata'>BulgarianTextMetadata</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t251">src\core\models.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t251"><data value='EmbeddingMetadata'>EmbeddingMetadata</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t259">src\core\models.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t259"><data value='SearchMetrics'>SearchMetrics</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t268">src\core\models.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t268"><data value='CrawlMetadata'>CrawlMetadata</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t278">src\core\models.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html#t278"><data value='DatabaseMetrics'>DatabaseMetrics</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html">src\core\models.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>216</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="216 216">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_reranker_py.html#t19">src\core\reranker.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_reranker_py.html#t19"><data value='CrossEncoderReranker'>CrossEncoderReranker</data></a></td>
                <td>109</td>
                <td>109</td>
                <td>0</td>
                <td class="right" data-ratio="0 109">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_reranker_py.html">src\core\reranker.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_reranker_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_text_processor_py.html#t19">src\core\text_processor.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_text_processor_py.html#t19"><data value='BulgarianTextProcessor'>BulgarianTextProcessor</data></a></td>
                <td>142</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="108 142">76%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_text_processor_py.html">src\core\text_processor.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_text_processor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_vector_store_py.html#t27">src\core\vector_store.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_vector_store_py.html#t27"><data value='VectorStore'>VectorStore</data></a></td>
                <td>190</td>
                <td>69</td>
                <td>0</td>
                <td class="right" data-ratio="121 190">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_vector_store_py.html">src\core\vector_store.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_vector_store_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>29</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 29">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0a383f9d6d96e73___init___py.html">src\crawler\__init__.py</a></td>
                <td class="name left"><a href="z_c0a383f9d6d96e73___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0a383f9d6d96e73_eu_funds_crawler_py.html#t25">src\crawler\eu_funds_crawler.py</a></td>
                <td class="name left"><a href="z_c0a383f9d6d96e73_eu_funds_crawler_py.html#t25"><data value='EUFundsCrawler'>EUFundsCrawler</data></a></td>
                <td>121</td>
                <td>121</td>
                <td>0</td>
                <td class="right" data-ratio="0 121">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0a383f9d6d96e73_eu_funds_crawler_py.html">src\crawler\eu_funds_crawler.py</a></td>
                <td class="name left"><a href="z_c0a383f9d6d96e73_eu_funds_crawler_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html">src\main.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>65</td>
                <td>65</td>
                <td>4</td>
                <td class="right" data-ratio="0 65">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c58a7740ba00d4cd_tools_py.html#t18">src\mcp\tools.py</a></td>
                <td class="name left"><a href="z_c58a7740ba00d4cd_tools_py.html#t18"><data value='EUFundsMCPTools'>EUFundsMCPTools</data></a></td>
                <td>140</td>
                <td>140</td>
                <td>0</td>
                <td class="right" data-ratio="0 140">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c58a7740ba00d4cd_tools_py.html">src\mcp\tools.py</a></td>
                <td class="name left"><a href="z_c58a7740ba00d4cd_tools_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40___init___py.html">src\tools\__init__.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be___init___py.html">src\utils\__init__.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>1800</td>
                <td>958</td>
                <td>4</td>
                <td class="right" data-ratio="842 1800">47%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.2">coverage.py v7.9.2</a>,
            created at 2025-07-07 20:48 +0300
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
