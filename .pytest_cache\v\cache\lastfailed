{"tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_detect_language_bulgarian": true, "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_detect_language_english": true, "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_detect_language_mixed": true, "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_clean_text_special_chars": true, "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_remove_stopwords_bulgarian": true, "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_remove_stopwords_english": true, "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_calculate_quality_score_high": true, "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_calculate_quality_score_low": true, "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_semantic_chunking_basic": true, "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_semantic_chunking_headers": true, "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_semantic_chunking_max_length": true, "tests/integration/test_mcp_tools_integration.py::TestMCPToolsIntegration::test_mcp_tools_initialization": true, "tests/integration/test_mcp_tools_integration.py::TestMCPToolsIntegration::test_search_eu_funds_real_data": true, "tests/integration/test_mcp_tools_integration.py::TestMCPToolsIntegration::test_analyze_eligibility_integration": true, "tests/integration/test_mcp_tools_integration.py::TestMCPToolsIntegration::test_get_application_deadlines_integration": true, "tests/integration/test_mcp_tools_integration.py::TestMCPToolsIntegration::test_tool_performance_metrics": true, "tests/unit/test_embeddings.py::TestEmbeddingProcessor::test_funding_terms_recognition": true}