{"tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_detect_language_bulgarian": true, "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_detect_language_english": true, "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_detect_language_mixed": true, "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_clean_text_special_chars": true, "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_remove_stopwords_bulgarian": true, "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_remove_stopwords_english": true, "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_calculate_quality_score_high": true, "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_calculate_quality_score_low": true, "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_semantic_chunking_basic": true, "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_semantic_chunking_headers": true, "tests/unit/test_text_processor.py::TestBulgarianTextProcessor::test_semantic_chunking_max_length": true, "tests/integration/test_mcp_tools_integration.py::TestMCPToolsIntegration::test_mcp_tools_initialization": true, "tests/integration/test_mcp_tools_integration.py::TestMCPToolsIntegration::test_search_eu_funds_real_data": true, "tests/integration/test_mcp_tools_integration.py::TestMCPToolsIntegration::test_analyze_eligibility_integration": true, "tests/integration/test_mcp_tools_integration.py::TestMCPToolsIntegration::test_get_application_deadlines_integration": true, "tests/integration/test_mcp_tools_integration.py::TestMCPToolsIntegration::test_tool_performance_metrics": true, "tests/unit/test_embeddings.py::TestEmbeddingProcessor::test_funding_terms_recognition": true, "tests/unit/test_vector_store.py::TestVectorStore::test_initialization": true, "tests/unit/test_vector_store.py::TestVectorStore::test_store_content_basic": true, "tests/unit/test_vector_store.py::TestVectorStore::test_store_content_with_chunks": true, "tests/unit/test_vector_store.py::TestVectorStore::test_vector_search_basic": true, "tests/unit/test_vector_store.py::TestVectorStore::test_vector_search_with_filters": true, "tests/unit/test_vector_store.py::TestVectorStore::test_text_search_basic": true, "tests/unit/test_vector_store.py::TestVectorStore::test_hybrid_search": true, "tests/unit/test_vector_store.py::TestVectorStore::test_get_content_by_id": true, "tests/unit/test_vector_store.py::TestVectorStore::test_update_content": true, "tests/unit/test_vector_store.py::TestVectorStore::test_delete_content": true, "tests/unit/test_vector_store.py::TestVectorStore::test_get_statistics": true, "tests/unit/test_vector_store.py::TestVectorStore::test_error_handling_connection": true, "tests/unit/test_vector_store.py::TestVectorStore::test_error_handling_invalid_vector": true, "tests/unit/test_vector_store.py::TestVectorStore::test_batch_operations": true, "tests/unit/test_vector_store.py::TestVectorStore::test_search_performance": true, "tests/unit/test_vector_store.py::TestVectorStore::test_concurrent_operations": true}