"""
EU Funds MCP Server
FastMCP-based server implementing EU funding information access tools
"""

import logging
import asyncio
from typing import Dict, Any, Optional, List
import sys
import os

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from fastmcp import FastMCP
from src.mcp.tools import EUFundsMCPTools
from src.core.config import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize FastMCP server
mcp = FastMCP("EU Funds MCP Server")

# Global tools instance
eu_tools: Optional[EUFundsMCPTools] = None

@mcp.tool()
async def search_eu_funds(
    query: str,
    max_results: int = 10,
    use_reranking: bool = True
) -> Dict[str, Any]:
    """
    Search for EU funding information using advanced hybrid search.
    
    Args:
        query: Search query in Bulgarian or English
        max_results: Maximum number of results to return (default: 10)
        use_reranking: Whether to apply cross-encoder reranking (default: True)
    
    Returns:
        Dictionary containing search results with relevance scores and metadata
    """
    global eu_tools
    
    try:
        if not eu_tools:
            logger.info("🔧 Initializing EU Funds MCP Tools...")
            eu_tools = EUFundsMCPTools()
            await eu_tools.initialize()
        
        logger.info(f"🔍 MCP Tool: search_eu_funds - Query: '{query[:50]}...'")
        
        result = await eu_tools.search_eu_funds(
            query=query,
            max_results=max_results,
            use_reranking=use_reranking
        )
        
        logger.info(f"✅ Search completed: {result.get('total_found', 0)} results")
        return result
        
    except Exception as e:
        logger.error(f"❌ search_eu_funds failed: {e}")
        return {
            "results": [],
            "total_found": 0,
            "query": query,
            "error": str(e),
            "tool": "search_eu_funds"
        }

@mcp.tool()
async def get_funding_programs(
    sector: Optional[str] = None,
    target_group: Optional[str] = None,
    budget_range: Optional[str] = None,
    deadline_months: int = 12
) -> Dict[str, Any]:
    """
    Discover EU funding programs by specific criteria.
    
    Args:
        sector: Target sector (agriculture, research, SME, etc.)
        target_group: Target beneficiaries (farmers, researchers, SMEs, etc.)
        budget_range: Budget range (small, medium, large)
        deadline_months: Look for programs with deadlines in next N months (default: 12)
    
    Returns:
        Dictionary containing matching funding programs with details
    """
    global eu_tools
    
    try:
        if not eu_tools:
            logger.info("🔧 Initializing EU Funds MCP Tools...")
            eu_tools = EUFundsMCPTools()
            await eu_tools.initialize()
        
        logger.info(f"🎯 MCP Tool: get_funding_programs - Sector: {sector}, Target: {target_group}")
        
        result = await eu_tools.get_funding_programs(
            sector=sector,
            target_group=target_group,
            budget_range=budget_range,
            deadline_months=deadline_months
        )
        
        logger.info(f"✅ Found {result.get('total_found', 0)} funding programs")
        return result
        
    except Exception as e:
        logger.error(f"❌ get_funding_programs failed: {e}")
        return {
            "programs": [],
            "total_found": 0,
            "error": str(e),
            "tool": "get_funding_programs"
        }

@mcp.tool()
async def analyze_eligibility(
    organization_type: str,
    sector: str,
    project_description: str,
    budget_needed: Optional[str] = None
) -> Dict[str, Any]:
    """
    Analyze eligibility for EU funding based on organization and project criteria.
    
    Args:
        organization_type: Type of organization (SME, NGO, university, etc.)
        sector: Project sector (agriculture, research, innovation, etc.)
        project_description: Brief description of the project
        budget_needed: Estimated budget needed (optional)
    
    Returns:
        Dictionary containing eligibility analysis and recommendations
    """
    global eu_tools
    
    try:
        if not eu_tools:
            logger.info("🔧 Initializing EU Funds MCP Tools...")
            eu_tools = EUFundsMCPTools()
            await eu_tools.initialize()
        
        logger.info(f"📋 MCP Tool: analyze_eligibility - {organization_type} in {sector}")
        
        result = await eu_tools.analyze_eligibility(
            organization_type=organization_type,
            sector=sector,
            project_description=project_description,
            budget_needed=budget_needed
        )
        
        logger.info("✅ Eligibility analysis completed")
        return result
        
    except Exception as e:
        logger.error(f"❌ analyze_eligibility failed: {e}")
        return {
            "eligibility_analysis": {"error": str(e)},
            "tool": "analyze_eligibility"
        }

@mcp.tool()
async def get_application_deadlines(
    months_ahead: int = 6,
    sector_filter: Optional[str] = None,
    urgent_only: bool = False
) -> Dict[str, Any]:
    """
    Get upcoming application deadlines for EU funding programs.
    
    Args:
        months_ahead: Look for deadlines in next N months (default: 6)
        sector_filter: Filter by specific sector (optional)
        urgent_only: Show only urgent deadlines in next 30 days (default: False)
    
    Returns:
        Dictionary containing upcoming deadlines with urgency levels
    """
    global eu_tools
    
    try:
        if not eu_tools:
            logger.info("🔧 Initializing EU Funds MCP Tools...")
            eu_tools = EUFundsMCPTools()
            await eu_tools.initialize()
        
        logger.info(f"📅 MCP Tool: get_application_deadlines - {months_ahead} months ahead")
        
        result = await eu_tools.get_application_deadlines(
            months_ahead=months_ahead,
            sector_filter=sector_filter,
            urgent_only=urgent_only
        )
        
        logger.info(f"✅ Found {result.get('total_found', 0)} deadlines")
        return result
        
    except Exception as e:
        logger.error(f"❌ get_application_deadlines failed: {e}")
        return {
            "deadlines": [],
            "total_found": 0,
            "error": str(e),
            "tool": "get_application_deadlines"
        }

@mcp.tool()
async def get_server_stats() -> Dict[str, Any]:
    """
    Get EU Funds MCP Server performance statistics and status.
    
    Returns:
        Dictionary containing server statistics and tool performance metrics
    """
    global eu_tools
    
    try:
        if not eu_tools:
            return {
                "status": "not_initialized",
                "message": "EU Funds MCP Tools not yet initialized"
            }
        
        stats = eu_tools.get_tool_stats()
        
        # Add server-level information
        server_info = {
            "server_name": "EU Funds MCP Server",
            "version": "1.0.0",
            "embedding_model": settings.embedding_model,
            "reranker_model": "cross-encoder/ms-marco-MiniLM-L-6-v2",
            "status": "operational",
            **stats
        }
        
        logger.info("📊 Server statistics requested")
        return server_info
        
    except Exception as e:
        logger.error(f"❌ get_server_stats failed: {e}")
        return {
            "status": "error",
            "error": str(e)
        }

async def initialize_server():
    """Initialize the MCP server and tools."""
    global eu_tools
    
    try:
        logger.info("🚀 Starting EU Funds MCP Server...")
        logger.info(f"📋 Configuration: {settings.embedding_model}")
        
        # Pre-initialize tools for faster first requests
        logger.info("🔧 Pre-initializing EU Funds MCP Tools...")
        eu_tools = EUFundsMCPTools()
        await eu_tools.initialize()
        
        logger.info("✅ EU Funds MCP Server ready!")
        logger.info("🔧 Available tools:")
        logger.info("   - search_eu_funds: General EU funding search")
        logger.info("   - get_funding_programs: Program discovery by criteria")
        logger.info("   - analyze_eligibility: Eligibility analysis")
        logger.info("   - get_application_deadlines: Deadline information")
        logger.info("   - get_server_stats: Server performance statistics")
        
    except Exception as e:
        logger.error(f"❌ Server initialization failed: {e}")
        raise

async def cleanup_server():
    """Clean up server resources."""
    global eu_tools
    
    try:
        if eu_tools:
            await eu_tools.cleanup()
        logger.info("✅ EU Funds MCP Server cleanup complete")
        
    except Exception as e:
        logger.error(f"❌ Server cleanup failed: {e}")

def main():
    """Main entry point for the MCP server."""
    try:
        # Initialize server
        asyncio.run(initialize_server())
        
        # Run the FastMCP server
        mcp.run()
        
    except KeyboardInterrupt:
        logger.info("🛑 Server shutdown requested")
    except Exception as e:
        logger.error(f"❌ Server error: {e}")
    finally:
        # Cleanup
        try:
            asyncio.run(cleanup_server())
        except:
            pass

if __name__ == "__main__":
    main()
