<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_db813965.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">47%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.2">coverage.py v7.9.2</a>,
            created at 2025-07-07 20:48 +0300
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6___init___py.html">src\__init__.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41___init___py.html">src\core\__init__.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_config_py.html#t108">src\core\config.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_config_py.html#t108"><data value='validate_openai_key'>Settings.validate_openai_key</data></a></td>
                <td>5</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="3 5">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_config_py.html#t117">src\core\config.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_config_py.html#t117"><data value='validate_supabase_url'>Settings.validate_supabase_url</data></a></td>
                <td>5</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="3 5">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_config_py.html#t126">src\core\config.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_config_py.html#t126"><data value='validate_supabase_key'>Settings.validate_supabase_key</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_config_py.html#t133">src\core\config.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_config_py.html#t133"><data value='validate_similarity_threshold'>Settings.validate_similarity_threshold</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_config_py.html#t140">src\core\config.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_config_py.html#t140"><data value='validate_reranker_weights'>Settings.validate_reranker_weights</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_config_py.html#t147">src\core\config.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_config_py.html#t147"><data value='validate_port'>Settings.validate_port</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_config_py.html#t154">src\core\config.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_config_py.html#t154"><data value='validate_log_level'>Settings.validate_log_level</data></a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_config_py.html#t178">src\core\config.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_config_py.html#t178"><data value='get_database_url'>Settings.get_database_url</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_config_py.html#t182">src\core\config.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_config_py.html#t182"><data value='get_embedding_config'>Settings.get_embedding_config</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_config_py.html#t190">src\core\config.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_config_py.html#t190"><data value='get_search_config'>Settings.get_search_config</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_config_py.html#t203">src\core\config.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_config_py.html#t203"><data value='get_hnsw_config'>Settings.get_hnsw_config</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_config_py.html#t210">src\core\config.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_config_py.html#t210"><data value='get_bulgarian_config'>Settings.get_bulgarian_config</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_config_py.html">src\core\config.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>65</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="65 65">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_crawler_py.html#t37">src\core\crawler.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_crawler_py.html#t37"><data value='init__'>EUFundsCrawler.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_crawler_py.html#t105">src\core\crawler.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_crawler_py.html#t105"><data value='initialize'>EUFundsCrawler.initialize</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_crawler_py.html#t142">src\core\crawler.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_crawler_py.html#t142"><data value='crawl_eu_source'>EUFundsCrawler.crawl_eu_source</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_crawler_py.html#t252">src\core\crawler.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_crawler_py.html#t252"><data value='process_crawl_result'>EUFundsCrawler._process_crawl_result</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_crawler_py.html#t327">src\core\crawler.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_crawler_py.html#t327"><data value='detect_language'>EUFundsCrawler._detect_language</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_crawler_py.html#t341">src\core\crawler.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_crawler_py.html#t341"><data value='calculate_quality_score'>EUFundsCrawler._calculate_quality_score</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_crawler_py.html#t368">src\core\crawler.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_crawler_py.html#t368"><data value='crawl_url'>EUFundsCrawler.crawl_url</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_crawler_py.html#t403">src\core\crawler.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_crawler_py.html#t403"><data value='get_data_sources'>EUFundsCrawler.get_data_sources</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_crawler_py.html#t420">src\core\crawler.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_crawler_py.html#t420"><data value='cleanup'>EUFundsCrawler.cleanup</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_crawler_py.html">src\core\crawler.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_crawler_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_database_py.html#t32">src\core\database.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_database_py.html#t32"><data value='init__'>DatabaseManager.__init__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_database_py.html#t50">src\core\database.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_database_py.html#t50"><data value='extract_host_from_url'>DatabaseManager._extract_host_from_url</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_database_py.html#t56">src\core\database.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_database_py.html#t56"><data value='extract_password_from_service_key'>DatabaseManager._extract_password_from_service_key</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_database_py.html#t62">src\core\database.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_database_py.html#t62"><data value='initialize'>DatabaseManager.initialize</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_database_py.html#t97">src\core\database.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_database_py.html#t97"><data value='ensure_schema'>DatabaseManager._ensure_schema</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_database_py.html#t116">src\core\database.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_database_py.html#t116"><data value='health_check'>DatabaseManager.health_check</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_database_py.html#t138">src\core\database.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_database_py.html#t138"><data value='search_documents'>DatabaseManager.search_documents</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_database_py.html#t193">src\core\database.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_database_py.html#t193"><data value='get_funding_programs'>DatabaseManager.get_funding_programs</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_database_py.html#t239">src\core\database.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_database_py.html#t239"><data value='store_document'>DatabaseManager.store_document</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_database_py.html#t275">src\core\database.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_database_py.html#t275"><data value='get_data_sources'>DatabaseManager.get_data_sources</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_database_py.html#t309">src\core\database.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_database_py.html#t309"><data value='log_search_metrics'>DatabaseManager.log_search_metrics</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_database_py.html#t318">src\core\database.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_database_py.html#t318"><data value='cleanup'>DatabaseManager.cleanup</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_database_py.html">src\core\database.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_database_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>26</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="26 26">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_embeddings_py.html#t37">src\core\embeddings.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_embeddings_py.html#t37"><data value='init__'>EmbeddingProcessor.__init__</data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_embeddings_py.html#t67">src\core\embeddings.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_embeddings_py.html#t67"><data value='initialize'>EmbeddingProcessor.initialize</data></a></td>
                <td>19</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="14 19">74%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_embeddings_py.html#t110">src\core\embeddings.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_embeddings_py.html#t110"><data value='embed_text'>EmbeddingProcessor.embed_text</data></a></td>
                <td>24</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="16 24">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_embeddings_py.html#t208">src\core\embeddings.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_embeddings_py.html#t208"><data value='embed_batch'>EmbeddingProcessor.embed_batch</data></a></td>
                <td>35</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="29 35">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_embeddings_py.html#t313">src\core\embeddings.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_embeddings_py.html#t313"><data value='prepare_text_for_embedding'>EmbeddingProcessor._prepare_text_for_embedding</data></a></td>
                <td>10</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="9 10">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_embeddings_py.html#t339">src\core\embeddings.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_embeddings_py.html#t339"><data value='generate_embedding'>EmbeddingProcessor._generate_embedding</data></a></td>
                <td>11</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="4 11">36%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_embeddings_py.html#t366">src\core\embeddings.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_embeddings_py.html#t366"><data value='generate_batch_embeddings'>EmbeddingProcessor._generate_batch_embeddings</data></a></td>
                <td>11</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="4 11">36%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_embeddings_py.html#t395">src\core\embeddings.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_embeddings_py.html#t395"><data value='generate_openai_embedding'>EmbeddingProcessor._generate_openai_embedding</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_embeddings_py.html#t411">src\core\embeddings.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_embeddings_py.html#t411"><data value='detect_language'>EmbeddingProcessor._detect_language</data></a></td>
                <td>8</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="6 8">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_embeddings_py.html#t425">src\core\embeddings.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_embeddings_py.html#t425"><data value='update_cache'>EmbeddingProcessor._update_cache</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_embeddings_py.html#t434">src\core\embeddings.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_embeddings_py.html#t434"><data value='update_avg_processing_time'>EmbeddingProcessor._update_avg_processing_time</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_embeddings_py.html#t444">src\core\embeddings.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_embeddings_py.html#t444"><data value='store_embeddings'>EmbeddingProcessor.store_embeddings</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_embeddings_py.html#t479">src\core\embeddings.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_embeddings_py.html#t479"><data value='get_stats'>EmbeddingProcessor.get_stats</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_embeddings_py.html#t492">src\core\embeddings.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_embeddings_py.html#t492"><data value='calculate_similarity'>EmbeddingProcessor.calculate_similarity</data></a></td>
                <td>10</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="7 10">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_embeddings_py.html#t519">src\core\embeddings.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_embeddings_py.html#t519"><data value='get_model_info'>EmbeddingProcessor.get_model_info</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_embeddings_py.html#t537">src\core\embeddings.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_embeddings_py.html#t537"><data value='cleanup'>EmbeddingProcessor.cleanup</data></a></td>
                <td>11</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="8 11">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_embeddings_py.html">src\core\embeddings.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_embeddings_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_hybrid_search_py.html#t33">src\core\hybrid_search.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_hybrid_search_py.html#t33"><data value='init__'>HybridSearchEngine.__init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_hybrid_search_py.html#t50">src\core\hybrid_search.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_hybrid_search_py.html#t50"><data value='initialize'>HybridSearchEngine.initialize</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_hybrid_search_py.html#t75">src\core\hybrid_search.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_hybrid_search_py.html#t75"><data value='search'>HybridSearchEngine.search</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_hybrid_search_py.html#t117">src\core\hybrid_search.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_hybrid_search_py.html#t117"><data value='vector_only_search'>HybridSearchEngine._vector_only_search</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_hybrid_search_py.html#t160">src\core\hybrid_search.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_hybrid_search_py.html#t160"><data value='text_only_search'>HybridSearchEngine._text_only_search</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_hybrid_search_py.html#t207">src\core\hybrid_search.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_hybrid_search_py.html#t207"><data value='hybrid_search'>HybridSearchEngine._hybrid_search</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_hybrid_search_py.html#t250">src\core\hybrid_search.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_hybrid_search_py.html#t250"><data value='reciprocal_rank_fusion'>HybridSearchEngine._reciprocal_rank_fusion</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_hybrid_search_py.html#t307">src\core\hybrid_search.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_hybrid_search_py.html#t307"><data value='apply_content_boosts'>HybridSearchEngine._apply_content_boosts</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_hybrid_search_py.html#t359">src\core\hybrid_search.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_hybrid_search_py.html#t359"><data value='cleanup'>HybridSearchEngine.cleanup</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_hybrid_search_py.html">src\core\hybrid_search.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_hybrid_search_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html">src\core\models.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>216</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="216 216">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_reranker_py.html#t26">src\core\reranker.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_reranker_py.html#t26"><data value='init__'>CrossEncoderReranker.__init__</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_reranker_py.html#t45">src\core\reranker.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_reranker_py.html#t45"><data value='initialize'>CrossEncoderReranker.initialize</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_reranker_py.html#t78">src\core\reranker.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_reranker_py.html#t78"><data value='rerank_results'>CrossEncoderReranker.rerank_results</data></a></td>
                <td>41</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_reranker_py.html#t177">src\core\reranker.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_reranker_py.html#t177"><data value='rerank_hybrid_result'>CrossEncoderReranker.rerank_hybrid_result</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_reranker_py.html#t226">src\core\reranker.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_reranker_py.html#t226"><data value='get_rerank_stats'>CrossEncoderReranker.get_rerank_stats</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_reranker_py.html#t237">src\core\reranker.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_reranker_py.html#t237"><data value='validate_relevance'>CrossEncoderReranker.validate_relevance</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_reranker_py.html#t271">src\core\reranker.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_reranker_py.html#t271"><data value='batch_validate_relevance'>CrossEncoderReranker.batch_validate_relevance</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_reranker_py.html#t314">src\core\reranker.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_reranker_py.html#t314"><data value='cleanup'>CrossEncoderReranker.cleanup</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_reranker_py.html">src\core\reranker.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_reranker_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_text_processor_py.html#t31">src\core\text_processor.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_text_processor_py.html#t31"><data value='init__'>BulgarianTextProcessor.__init__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_text_processor_py.html#t98">src\core\text_processor.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_text_processor_py.html#t98"><data value='process_text'>BulgarianTextProcessor.process_text</data></a></td>
                <td>14</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="11 14">79%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_text_processor_py.html#t157">src\core\text_processor.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_text_processor_py.html#t157"><data value='normalize_cyrillic'>BulgarianTextProcessor._normalize_cyrillic</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_text_processor_py.html#t177">src\core\text_processor.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_text_processor_py.html#t177"><data value='clean_text'>BulgarianTextProcessor._clean_text</data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_text_processor_py.html#t204">src\core\text_processor.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_text_processor_py.html#t204"><data value='analyze_language'>BulgarianTextProcessor._analyze_language</data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_text_processor_py.html#t248">src\core\text_processor.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_text_processor_py.html#t248"><data value='extract_funding_info'>BulgarianTextProcessor._extract_funding_info</data></a></td>
                <td>28</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="20 28">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_text_processor_py.html#t303">src\core\text_processor.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_text_processor_py.html#t303"><data value='create_semantic_chunks'>BulgarianTextProcessor._create_semantic_chunks</data></a></td>
                <td>20</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="13 20">65%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_text_processor_py.html#t339">src\core\text_processor.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_text_processor_py.html#t339"><data value='split_long_section'>BulgarianTextProcessor._split_long_section</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_text_processor_py.html#t358">src\core\text_processor.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_text_processor_py.html#t358"><data value='create_chunk'>BulgarianTextProcessor._create_chunk</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_text_processor_py.html#t385">src\core\text_processor.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_text_processor_py.html#t385"><data value='calculate_quality_score'>BulgarianTextProcessor._calculate_quality_score</data></a></td>
                <td>20</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="16 20">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_text_processor_py.html">src\core\text_processor.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_text_processor_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_vector_store_py.html#t33">src\core\vector_store.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_vector_store_py.html#t33"><data value='init__'>VectorStore.__init__</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_vector_store_py.html#t41">src\core\vector_store.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_vector_store_py.html#t41"><data value='initialize'>VectorStore.initialize</data></a></td>
                <td>17</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="11 17">65%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_vector_store_py.html#t74">src\core\vector_store.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_vector_store_py.html#t74"><data value='init_pg_connection'>VectorStore._init_pg_connection</data></a></td>
                <td>9</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="7 9">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_vector_store_py.html#t98">src\core\vector_store.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_vector_store_py.html#t98"><data value='ensure_schema'>VectorStore._ensure_schema</data></a></td>
                <td>12</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="7 12">58%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_vector_store_py.html#t121">src\core\vector_store.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_vector_store_py.html#t121"><data value='store_content'>VectorStore.store_content</data></a></td>
                <td>17</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="15 17">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_vector_store_py.html#t176">src\core\vector_store.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_vector_store_py.html#t176"><data value='store_batch'>VectorStore.store_batch</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_vector_store_py.html#t226">src\core\vector_store.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_vector_store_py.html#t226"><data value='vector_search'>VectorStore.vector_search</data></a></td>
                <td>18</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="13 18">72%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_vector_store_py.html#t276">src\core\vector_store.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_vector_store_py.html#t276"><data value='store_content_chunks'>VectorStore.store_content_chunks</data></a></td>
                <td>13</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="10 13">77%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_vector_store_py.html#t306">src\core\vector_store.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_vector_store_py.html#t306"><data value='text_search'>VectorStore.text_search</data></a></td>
                <td>9</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="6 9">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_vector_store_py.html#t340">src\core\vector_store.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_vector_store_py.html#t340"><data value='hybrid_search'>VectorStore.hybrid_search</data></a></td>
                <td>20</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="15 20">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_vector_store_py.html#t414">src\core\vector_store.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_vector_store_py.html#t414"><data value='get_content_by_id'>VectorStore.get_content_by_id</data></a></td>
                <td>8</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="4 8">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_vector_store_py.html#t427">src\core\vector_store.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_vector_store_py.html#t427"><data value='update_content'>VectorStore.update_content</data></a></td>
                <td>6</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="3 6">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_vector_store_py.html#t437">src\core\vector_store.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_vector_store_py.html#t437"><data value='delete_content'>VectorStore.delete_content</data></a></td>
                <td>6</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="3 6">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_vector_store_py.html#t447">src\core\vector_store.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_vector_store_py.html#t447"><data value='get_statistics'>VectorStore.get_statistics</data></a></td>
                <td>16</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="13 16">81%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_vector_store_py.html#t477">src\core\vector_store.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_vector_store_py.html#t477"><data value='batch_store_content'>VectorStore.batch_store_content</data></a></td>
                <td>9</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="6 9">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_vector_store_py.html#t501">src\core\vector_store.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_vector_store_py.html#t501"><data value='cleanup'>VectorStore.cleanup</data></a></td>
                <td>7</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="3 7">43%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_vector_store_py.html">src\core\vector_store.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_vector_store_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>29</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 29">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0a383f9d6d96e73___init___py.html">src\crawler\__init__.py</a></td>
                <td class="name left"><a href="z_c0a383f9d6d96e73___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0a383f9d6d96e73_eu_funds_crawler_py.html#t31">src\crawler\eu_funds_crawler.py</a></td>
                <td class="name left"><a href="z_c0a383f9d6d96e73_eu_funds_crawler_py.html#t31"><data value='init__'>EUFundsCrawler.__init__</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0a383f9d6d96e73_eu_funds_crawler_py.html#t68">src\crawler\eu_funds_crawler.py</a></td>
                <td class="name left"><a href="z_c0a383f9d6d96e73_eu_funds_crawler_py.html#t68"><data value='initialize'>EUFundsCrawler.initialize</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0a383f9d6d96e73_eu_funds_crawler_py.html#t97">src\crawler\eu_funds_crawler.py</a></td>
                <td class="name left"><a href="z_c0a383f9d6d96e73_eu_funds_crawler_py.html#t97"><data value='crawl_test_sites'>EUFundsCrawler.crawl_test_sites</data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0a383f9d6d96e73_eu_funds_crawler_py.html#t163">src\crawler\eu_funds_crawler.py</a></td>
                <td class="name left"><a href="z_c0a383f9d6d96e73_eu_funds_crawler_py.html#t163"><data value='process_crawled_page'>EUFundsCrawler._process_crawled_page</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0a383f9d6d96e73_eu_funds_crawler_py.html#t209">src\crawler\eu_funds_crawler.py</a></td>
                <td class="name left"><a href="z_c0a383f9d6d96e73_eu_funds_crawler_py.html#t209"><data value='determine_content_type'>EUFundsCrawler._determine_content_type</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0a383f9d6d96e73_eu_funds_crawler_py.html#t229">src\crawler\eu_funds_crawler.py</a></td>
                <td class="name left"><a href="z_c0a383f9d6d96e73_eu_funds_crawler_py.html#t229"><data value='detect_language'>EUFundsCrawler._detect_language</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0a383f9d6d96e73_eu_funds_crawler_py.html#t240">src\crawler\eu_funds_crawler.py</a></td>
                <td class="name left"><a href="z_c0a383f9d6d96e73_eu_funds_crawler_py.html#t240"><data value='store_content'>EUFundsCrawler._store_content</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0a383f9d6d96e73_eu_funds_crawler_py.html#t283">src\crawler\eu_funds_crawler.py</a></td>
                <td class="name left"><a href="z_c0a383f9d6d96e73_eu_funds_crawler_py.html#t283"><data value='generate_crawl_report'>EUFundsCrawler._generate_crawl_report</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0a383f9d6d96e73_eu_funds_crawler_py.html#t328">src\crawler\eu_funds_crawler.py</a></td>
                <td class="name left"><a href="z_c0a383f9d6d96e73_eu_funds_crawler_py.html#t328"><data value='cleanup'>EUFundsCrawler.cleanup</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0a383f9d6d96e73_eu_funds_crawler_py.html">src\crawler\eu_funds_crawler.py</a></td>
                <td class="name left"><a href="z_c0a383f9d6d96e73_eu_funds_crawler_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t41">src\main.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t41"><data value='initialize_components'>initialize_components</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t73">src\main.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t73"><data value='search_eu_funds'>search_eu_funds</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t127">src\main.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t127"><data value='get_funding_programs'>get_funding_programs</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t193">src\main.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t193"><data value='analyze_eligibility'>analyze_eligibility</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t244">src\main.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t244"><data value='health_check'>health_check</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html">src\main.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>26</td>
                <td>26</td>
                <td>4</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c58a7740ba00d4cd_tools_py.html#t24">src\mcp\tools.py</a></td>
                <td class="name left"><a href="z_c58a7740ba00d4cd_tools_py.html#t24"><data value='init__'>EUFundsMCPTools.__init__</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c58a7740ba00d4cd_tools_py.html#t43">src\mcp\tools.py</a></td>
                <td class="name left"><a href="z_c58a7740ba00d4cd_tools_py.html#t43"><data value='initialize'>EUFundsMCPTools.initialize</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c58a7740ba00d4cd_tools_py.html#t63">src\mcp\tools.py</a></td>
                <td class="name left"><a href="z_c58a7740ba00d4cd_tools_py.html#t63"><data value='search_eu_funds'>EUFundsMCPTools.search_eu_funds</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c58a7740ba00d4cd_tools_py.html#t161">src\mcp\tools.py</a></td>
                <td class="name left"><a href="z_c58a7740ba00d4cd_tools_py.html#t161"><data value='get_funding_programs'>EUFundsMCPTools.get_funding_programs</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c58a7740ba00d4cd_tools_py.html#t258">src\mcp\tools.py</a></td>
                <td class="name left"><a href="z_c58a7740ba00d4cd_tools_py.html#t258"><data value='analyze_eligibility'>EUFundsMCPTools.analyze_eligibility</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c58a7740ba00d4cd_tools_py.html#t356">src\mcp\tools.py</a></td>
                <td class="name left"><a href="z_c58a7740ba00d4cd_tools_py.html#t356"><data value='get_application_deadlines'>EUFundsMCPTools.get_application_deadlines</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c58a7740ba00d4cd_tools_py.html#t459">src\mcp\tools.py</a></td>
                <td class="name left"><a href="z_c58a7740ba00d4cd_tools_py.html#t459"><data value='update_tool_stats'>EUFundsMCPTools._update_tool_stats</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c58a7740ba00d4cd_tools_py.html#t469">src\mcp\tools.py</a></td>
                <td class="name left"><a href="z_c58a7740ba00d4cd_tools_py.html#t469"><data value='get_tool_stats'>EUFundsMCPTools.get_tool_stats</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c58a7740ba00d4cd_tools_py.html#t477">src\mcp\tools.py</a></td>
                <td class="name left"><a href="z_c58a7740ba00d4cd_tools_py.html#t477"><data value='cleanup'>EUFundsMCPTools.cleanup</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c58a7740ba00d4cd_tools_py.html">src\mcp\tools.py</a></td>
                <td class="name left"><a href="z_c58a7740ba00d4cd_tools_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40___init___py.html">src\tools\__init__.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be___init___py.html">src\utils\__init__.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>1800</td>
                <td>958</td>
                <td>4</td>
                <td class="right" data-ratio="842 1800">47%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.2">coverage.py v7.9.2</a>,
            created at 2025-07-07 20:48 +0300
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
