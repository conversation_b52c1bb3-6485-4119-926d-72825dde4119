# Contributing to EU Funds MCP Server

Благодарим за интереса към проекта! Този проект постигна **86.7% точност** и е **production-ready**, но винаги приветстваме подобрения.

## 🎯 Как да Допринесеш

### 1. Типове Contributions
- 🐛 **Bug fixes** - Поправки на грешки
- ✨ **Features** - Нови функционалности
- 📚 **Documentation** - Подобрения в документацията
- 🧪 **Tests** - Добавяне на тестове
- 🌍 **Translations** - Превод на други езици
- 📊 **Data sources** - Нови източници на данни

### 2. Преди да Започнеш
1. Провери [Issues](https://github.com/your-repo/eu-funds-mcp-server/issues) за съществуващи задачи
2. Създай нов Issue ако не намериш подходящ
3. Обсъди идеята си преди да започнеш работа

### 3. Development Setup
```bash
# Fork и clone repository-то
git clone https://github.com/your-username/eu-funds-mcp-server.git
cd eu-funds-mcp-server

# Създай development branch
git checkout -b feature/your-feature-name

# Инсталирай dependencies
pip install -r requirements.txt

# Инсталирай development dependencies
pip install pytest black flake8 mypy
```

### 4. Code Standards
```bash
# Форматиране на кода
black src/ tests/

# Linting
flake8 src/ tests/

# Type checking
mypy src/

# Стартирай тестовете
pytest tests/
```

### 5. Commit Guidelines
```bash
# Използвай descriptive commit messages
git commit -m "✨ Add support for new EU program OPTIC"
git commit -m "🐛 Fix encoding issue in Bulgarian text processing"
git commit -m "📚 Update installation instructions"

# Emoji guide:
# ✨ New feature
# 🐛 Bug fix
# 📚 Documentation
# 🧪 Tests
# ⚡ Performance
# 🔧 Configuration
```

### 6. Pull Request Process
1. Убедете се, че всички тестове минават
2. Добавете тестове за новата функционалност
3. Актуализирайте документацията
4. Създайте Pull Request с ясно описание

## 🧪 Testing Requirements

### Минимални Изисквания
- Всички съществуващи тестове трябва да минават
- Новият код трябва да има >80% test coverage
- Accuracy тестът трябва да запази >85% точност

### Стартиране на Тестове
```bash
# Unit tests
pytest tests/unit/

# Integration tests
pytest tests/integration/

# Accuracy test
python accuracy_test.py

# Coverage report
pytest --cov=src tests/
```

## 📊 Performance Guidelines

### Accuracy Standards
- Общата точност не трябва да спада под 85%
- Response time трябва да остане под 500ms
- Memory usage трябва да остане минимален

### Benchmarking
```bash
# Тест на производителността
python -m src.core.performance

# Memory profiling
python -m memory_profiler accuracy_test.py
```

## 🌍 Internationalization

### Добавяне на Нов Език
1. Създайте нов файл в `src/core/languages/`
2. Добавете language-specific processing
3. Актуализирайте `text_processor.py`
4. Добавете тестове за новия език

### Пример:
```python
# src/core/languages/english.py
ENGLISH_STOPWORDS = ["the", "and", "or", ...]
ENGLISH_EU_TERMS = ["funding", "grant", "program", ...]
```

## 📚 Documentation

### Изисквания за Документация
- Всички нови функции трябва да имат docstrings
- README.md трябва да се актуализира при нови features
- Примери за използване в `examples/`

### Docstring Format
```python
def process_query(query: str, language: str = "bg") -> ProcessedQuery:
    """
    Process user query for EU funding information.
    
    Args:
        query: User's question in natural language
        language: Language code (bg, en, etc.)
        
    Returns:
        ProcessedQuery object with classified intent and entities
        
    Example:
        >>> result = process_query("Какво финансиране има за МСП?")
        >>> print(result.intent)  # "funding_inquiry"
    """
```

## 🚀 Release Process

### Version Numbering
- Major: Breaking changes (1.0.0 -> 2.0.0)
- Minor: New features (1.0.0 -> 1.1.0)
- Patch: Bug fixes (1.0.0 -> 1.0.1)

### Release Checklist
- [ ] Всички тестове минават
- [ ] Accuracy test показва >85%
- [ ] Documentation е актуализирана
- [ ] CHANGELOG.md е обновен
- [ ] Version number е увеличен

## 🤝 Community Guidelines

### Code of Conduct
- Бъдете уважителни и професионални
- Приветстваме всички нива на опит
- Фокусирайте се върху конструктивна критика
- Помагайте на новите contributors

### Communication
- Използвайте Issues за bug reports и feature requests
- Discussions за общи въпроси
- Pull Request comments за code review

## 🏆 Recognition

Contributors ще бъдат признати в:
- README.md Contributors section
- Release notes
- Project documentation

Благодарим за вашия принос към този **outstanding RAG system**! 🎉
