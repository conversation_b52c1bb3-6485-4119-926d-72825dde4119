#!/usr/bin/env python3
"""
Debug embedding storage to understand the format issue.
"""

import asyncio
import sys
import os
from supabase import create_client, Client
from dotenv import load_dotenv
import openai

# Load environment variables
load_dotenv()

async def debug_embedding_storage():
    """Debug how embeddings are stored."""
    print("🔍 DEBUG EMBEDDING STORAGE")
    print("=" * 50)
    
    # Initialize Supabase client
    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_SERVICE_KEY") or os.getenv("SUPABASE_KEY")
    supabase: Client = create_client(url, key)
    
    # Initialize OpenAI
    openai.api_key = os.getenv("OPENAI_API_KEY")
    
    # Create a test embedding
    print("🧪 Създаване на тестов embedding...")
    try:
        response = openai.embeddings.create(
            model="text-embedding-3-small",
            input="тест текст"
        )
        
        test_embedding = response.data[0].embedding
        print(f"   ✅ OpenAI embedding: {len(test_embedding)} dimensions")
        print(f"   📊 Тип: {type(test_embedding)}")
        print(f"   🔍 Първи 5 стойности: {test_embedding[:5]}")
        
    except Exception as e:
        print(f"   ❌ Грешка при OpenAI: {e}")
        return
    
    # Try to insert test record
    print(f"\n💾 Тест на записване в Supabase...")
    try:
        test_data = {
            "title": "Тестов запис",
            "content": "Тестово съдържание за проверка на embeddings",
            "source_url": "https://test.com",
            "content_type": "test",
            "language": "bg",
            "metadata": {"test": True},
            "quality_score": 0.8,
            "embedding": test_embedding
        }
        
        result = supabase.table('eu_funds_content').insert(test_data).execute()
        
        if result.data:
            inserted_id = result.data[0]['id']
            print(f"   ✅ Записан тестов запис с ID: {inserted_id}")
            
            # Retrieve and check the embedding
            retrieved = supabase.table('eu_funds_content').select("*").eq('id', inserted_id).execute()
            
            if retrieved.data:
                stored_embedding = retrieved.data[0]['embedding']
                print(f"   📊 Записан embedding: {len(stored_embedding)} dimensions")
                print(f"   🔍 Тип: {type(stored_embedding)}")
                print(f"   🔍 Първи 5 стойности: {stored_embedding[:5]}")
                
                # Compare
                if len(stored_embedding) == len(test_embedding):
                    print(f"   ✅ Размерите съвпадат!")
                else:
                    print(f"   ❌ Размерите НЕ съвпадат!")
                    print(f"      Оригинал: {len(test_embedding)}")
                    print(f"      Записан: {len(stored_embedding)}")
                
                # Clean up test record
                supabase.table('eu_funds_content').delete().eq('id', inserted_id).execute()
                print(f"   🧹 Изтрит тестов запис")
                
        else:
            print(f"   ❌ Неуспешно записване")
            
    except Exception as e:
        print(f"   ❌ Грешка при записване: {e}")
    
    # Check existing records format
    print(f"\n🔍 ПРОВЕРКА НА СЪЩЕСТВУВАЩИ ЗАПИСИ:")
    try:
        result = supabase.table('eu_funds_content').select("id, title, embedding").limit(3).execute()
        
        for i, record in enumerate(result.data):
            embedding = record.get('embedding')
            title = record.get('title', 'N/A')
            
            print(f"   Запис {i+1}: {title[:30]}...")
            print(f"     Embedding размер: {len(embedding) if embedding else 'None'}")
            print(f"     Embedding тип: {type(embedding)}")
            
            if embedding and len(embedding) > 0:
                print(f"     Първи 3 стойности: {embedding[:3]}")
                print(f"     Последни 3 стойности: {embedding[-3:]}")
                
                # Check if it's nested
                if isinstance(embedding[0], list):
                    print(f"     ⚠️ ПРОБЛЕМ: Nested array detected!")
                    print(f"     Вътрешен размер: {len(embedding[0])}")
                
    except Exception as e:
        print(f"   ❌ Грешка при проверка: {e}")

if __name__ == "__main__":
    asyncio.run(debug_embedding_storage())
