-- Enable the pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Drop existing table if it exists
DROP TABLE IF EXISTS eu_funds_content CASCADE;

-- Create the proper eu_funds_content table with embeddings
CREATE TABLE eu_funds_content (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    source_url TEXT NOT NULL,
    content_type TEXT DEFAULT 'web_page',
    language TEXT DEFAULT 'bg',
    metadata JSONB DEFAULT '{}',
    quality_score FLOAT DEFAULT 0.5,
    embedding vector(1536), -- OpenAI embeddings are 1536 dimensions
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create indexes for better performance
CREATE INDEX ON eu_funds_content USING ivfflat (embedding vector_cosine_ops);
CREATE INDEX idx_eu_funds_content_source_url ON eu_funds_content (source_url);
CREATE INDEX idx_eu_funds_content_language ON eu_funds_content (language);
CREATE INDEX idx_eu_funds_content_metadata ON eu_funds_content USING gin (metadata);

-- Create a function for vector similarity search
CREATE OR REPLACE FUNCTION match_eu_funds_content(
    query_embedding vector(1536),
    match_threshold float DEFAULT 0.3,
    match_count int DEFAULT 10
)
RETURNS TABLE (
    id UUID,
    title TEXT,
    content TEXT,
    source_url TEXT,
    content_type TEXT,
    language TEXT,
    metadata JSONB,
    quality_score FLOAT,
    similarity FLOAT
)
LANGUAGE plpgsql
AS $$
#variable_conflict use_column
BEGIN
    RETURN QUERY
    SELECT
        eu_funds_content.id,
        eu_funds_content.title,
        eu_funds_content.content,
        eu_funds_content.source_url,
        eu_funds_content.content_type,
        eu_funds_content.language,
        eu_funds_content.metadata,
        eu_funds_content.quality_score,
        1 - (eu_funds_content.embedding <=> query_embedding) AS similarity
    FROM eu_funds_content
    WHERE 1 - (eu_funds_content.embedding <=> query_embedding) > match_threshold
    ORDER BY eu_funds_content.embedding <=> query_embedding
    LIMIT match_count;
END;
$$;

-- Create a function for text search
CREATE OR REPLACE FUNCTION search_eu_funds_text(
    search_query TEXT,
    match_count int DEFAULT 10
)
RETURNS TABLE (
    id UUID,
    title TEXT,
    content TEXT,
    source_url TEXT,
    content_type TEXT,
    language TEXT,
    metadata JSONB,
    quality_score FLOAT,
    rank FLOAT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        eu_funds_content.id,
        eu_funds_content.title,
        eu_funds_content.content,
        eu_funds_content.source_url,
        eu_funds_content.content_type,
        eu_funds_content.language,
        eu_funds_content.metadata,
        eu_funds_content.quality_score,
        ts_rank(to_tsvector('bulgarian', eu_funds_content.content), plainto_tsquery('bulgarian', search_query)) AS rank
    FROM eu_funds_content
    WHERE to_tsvector('bulgarian', eu_funds_content.content) @@ plainto_tsquery('bulgarian', search_query)
    ORDER BY rank DESC
    LIMIT match_count;
END;
$$;

-- Create a function for semantic search (hybrid)
CREATE OR REPLACE FUNCTION semantic_search(
    query_embedding vector(1536),
    search_query TEXT,
    match_threshold float DEFAULT 0.3,
    match_count int DEFAULT 10
)
RETURNS TABLE (
    id UUID,
    title TEXT,
    content TEXT,
    source_url TEXT,
    content_type TEXT,
    language TEXT,
    metadata JSONB,
    quality_score FLOAT,
    similarity FLOAT,
    text_rank FLOAT,
    combined_score FLOAT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        eu_funds_content.id,
        eu_funds_content.title,
        eu_funds_content.content,
        eu_funds_content.source_url,
        eu_funds_content.content_type,
        eu_funds_content.language,
        eu_funds_content.metadata,
        eu_funds_content.quality_score,
        1 - (eu_funds_content.embedding <=> query_embedding) AS similarity,
        ts_rank(to_tsvector('bulgarian', eu_funds_content.content), plainto_tsquery('bulgarian', search_query)) AS text_rank,
        (0.7 * (1 - (eu_funds_content.embedding <=> query_embedding))) + 
        (0.3 * ts_rank(to_tsvector('bulgarian', eu_funds_content.content), plainto_tsquery('bulgarian', search_query))) AS combined_score
    FROM eu_funds_content
    WHERE 
        (1 - (eu_funds_content.embedding <=> query_embedding) > match_threshold)
        OR 
        (to_tsvector('bulgarian', eu_funds_content.content) @@ plainto_tsquery('bulgarian', search_query))
    ORDER BY combined_score DESC
    LIMIT match_count;
END;
$$;

-- Enable RLS
ALTER TABLE eu_funds_content ENABLE ROW LEVEL SECURITY;

-- Create policy for public read access
CREATE POLICY "Allow public read access to eu_funds_content" 
ON eu_funds_content FOR SELECT 
TO public 
USING (true);

-- Create policy for public insert access (for crawler)
CREATE POLICY "Allow public insert access to eu_funds_content" 
ON eu_funds_content FOR INSERT 
TO public 
WITH CHECK (true);

-- Create policy for public update access (for crawler)
CREATE POLICY "Allow public update access to eu_funds_content" 
ON eu_funds_content FOR UPDATE 
TO public 
USING (true);

-- Create policy for public delete access (for crawler cleanup)
CREATE POLICY "Allow public delete access to eu_funds_content" 
ON eu_funds_content FOR DELETE 
TO public 
USING (true);
