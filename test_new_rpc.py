#!/usr/bin/env python3
"""
Test the new RPC function for inserting with embeddings.
"""

import asyncio
import sys
import os
from supabase import create_client, Client
from dotenv import load_dotenv
import openai

# Load environment variables
load_dotenv()

async def test_new_rpc():
    """Test the new RPC function."""
    print("🧪 ТЕСТВАНЕ НА НОВАТА RPC ФУНКЦИЯ")
    print("=" * 50)
    
    # Initialize Supabase client
    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_SERVICE_KEY") or os.getenv("SUPABASE_KEY")
    supabase: Client = create_client(url, key)
    
    # Initialize OpenAI
    openai.api_key = os.getenv("OPENAI_API_KEY")
    
    # Create test embedding
    print("🔧 Създаване на тестов embedding...")
    try:
        response = openai.embeddings.create(
            model="text-embedding-3-small",
            input="тест за RPC функция"
        )
        
        original_embedding = response.data[0].embedding
        print(f"   ✅ Embedding: {len(original_embedding)} dimensions")
        
        # Test the RPC function
        print(f"\n🧪 Тестване на insert_with_embedding RPC...")
        try:
            result = supabase.rpc('insert_with_embedding', {
                'p_title': 'Test RPC Function',
                'p_content': 'Тестово съдържание за RPC функция',
                'p_source_url': 'https://test-rpc.com',
                'p_content_type': 'test',
                'p_language': 'bg',
                'p_metadata': {'test': True, 'method': 'rpc'},
                'p_quality_score': 0.9,
                'p_embedding_array': original_embedding
            }).execute()
            
            if result.data:
                inserted_id = result.data
                print(f"   ✅ RPC функция работи!")
                print(f"   📝 Записан с ID: {inserted_id}")
                
                # Check the stored data
                check_result = supabase.table('eu_funds_content').select("*").eq('id', inserted_id).execute()
                
                if check_result.data:
                    stored_data = check_result.data[0]
                    stored_embedding = stored_data['embedding']
                    
                    print(f"   📊 Проверка на записаните данни:")
                    print(f"     Title: {stored_data['title']}")
                    print(f"     Content length: {len(stored_data['content'])}")
                    print(f"     Embedding размер: {len(stored_embedding) if stored_embedding else 'None'}")
                    print(f"     Embedding тип: {type(stored_embedding)}")
                    
                    if isinstance(stored_embedding, list) and len(stored_embedding) == 1536:
                        print(f"   🎉 УСПЕХ! Правилен vector формат!")
                        
                        # Test similarity
                        first_values = stored_embedding[:5]
                        original_first = original_embedding[:5]
                        print(f"     Оригинал: {original_first}")
                        print(f"     Записан: {first_values}")
                        
                        # Check if values match
                        if abs(first_values[0] - original_first[0]) < 0.0001:
                            print(f"   ✅ Стойностите съвпадат!")
                        else:
                            print(f"   ⚠️ Стойностите не съвпадат")
                            
                    else:
                        print(f"   ❌ Неправилен формат")
                
                # Test vector search
                print(f"\n🔍 Тестване на vector search...")
                try:
                    search_result = supabase.rpc('match_eu_funds_content', {
                        'query_embedding': original_embedding,
                        'match_threshold': 0.1,
                        'match_count': 3
                    }).execute()
                    
                    if search_result.data:
                        print(f"   ✅ Vector search работи!")
                        print(f"   📊 Намерени резултати: {len(search_result.data)}")
                        
                        for i, item in enumerate(search_result.data[:2]):
                            print(f"     Резултат {i+1}:")
                            print(f"       Title: {item.get('title', 'N/A')}")
                            print(f"       Similarity: {item.get('similarity', 0):.3f}")
                    else:
                        print(f"   ❌ Vector search не връща резултати")
                        
                except Exception as e:
                    print(f"   ❌ Vector search грешка: {e}")
                
                # Clean up
                supabase.table('eu_funds_content').delete().eq('id', inserted_id).execute()
                print(f"   🧹 Изтрит тестов запис")
                
            else:
                print(f"   ❌ RPC функция не върна ID")
                
        except Exception as e:
            print(f"   ❌ RPC грешка: {e}")
            
    except Exception as e:
        print(f"❌ Грешка при OpenAI: {e}")

if __name__ == "__main__":
    asyncio.run(test_new_rpc())
