name: "MCP Сървър за ЕС Фондове: Интелигентна RAG система за европейски финансиращи програми"
description: |
  ## Purpose
  Създаване на специализиран MCP (Model Context Protocol) сървър, който предоставя точна, актуална информация за европейски финансиращи програми на LLM-и. Системата автоматизира процеса на събиране, обработка и предоставяне на информация за ЕС фондове в България.

  ## Core Principles
  1. **Open-Source First**: Минимизиране на оперативните разходи чрез безплатни технологии
  2. **Модулност**: Всеки компонент може да се развива независимо
  3. **Точност**: Интелигентно извличане само на съществена информация
  4. **Мултимодалност**: Обработка на текст, таблици и изображения
  5. **Български език**: Оптимизация за български език и специфики

---

## Goal
Създаване на production-ready MCP сървър, който:
- Автоматично обхожда официални сайтове (eufunds.bg и др.)
- Извлича и структурира нова информация за ЕС фондове
- Предоставя мултимодална RAG система за български език
- Служи като "инструмент за инструменти" за други LLM-и

## Why
- **Бизнес стойност**: Демократизира достъпа до информация за ЕС фондове
- **Проблем**: Сложен процес на търсене и следене на финансиращи програми
- **Решение**: Автоматизация и интелигентна обработка на информацията

## What
MCP сървър със следните компоненти:
- Интелигентен уеб краулер с GPT-4o-mini за извличане на съдържание
- Supabase PostgreSQL база данни с pgvector за векторно търсене
- Хибридна RAG система (семантично + keyword търсене)
- Open-source ембединг модели за български език

### Success Criteria
- [ ] Краулер успешно извлича нова информация от eufunds.bg
- [ ] RAG система отговаря точно на въпроси за ЕС фондове на български
- [ ] Хибридно търсене комбинира семантично и keyword търсене
- [ ] Мултимодална обработка на таблици и изображения
- [ ] MCP протокол правилно експозира функционалността
- [ ] Всички тестове преминават и кодът отговаря на стандартите

## All Needed Context

### Documentation & References
```yaml
# MUST READ - Include these in your context window
- url: https://spec.modelcontextprotocol.io/
  why: MCP протокол спецификация и имплементация
- url: https://supabase.com/docs/guides/ai/vector-embeddings
  why: pgvector setup и векторни операции
- url: https://docs.llamaindex.ai/en/stable/
  why: LlamaIndex за RAG система
- url: https://huggingface.co/intfloat/multilingual-e5-large
  why: Най-добър open-source ембединг модел за български
- url: https://scrapy.org/
  why: Scrapy за уеб краулинг
- url: https://playwright.dev/python/
  why: Playwright за динамично съдържание
- url: https://eufunds.bg/
  why: Основен източник на данни за ЕС фондове
```

### Current Codebase Structure
```bash
# Желана структура на проекта
eu-funds-mcp-server/
├── mcp_server/
│   ├── __init__.py
│   ├── server.py              # MCP сървър имплементация
│   ├── tools.py               # MCP tools за RAG операции
│   └── models.py              # Pydantic модели
├── crawler/
│   ├── __init__.py
│   ├── spider.py              # Scrapy spider за eufunds.bg
│   ├── processors.py          # GPT-4o-mini обработка на HTML
│   └── scheduler.py           # Cron jobs за периодично обхождане
├── rag/
│   ├── __init__.py
│   ├── embeddings.py          # Ембединг модели
│   ├── retrieval.py           # Хибридно търсене
│   ├── multimodal.py          # Обработка на таблици/изображения
│   └── pipeline.py            # RAG pipeline
├── database/
│   ├── __init__.py
│   ├── models.py              # SQLAlchemy модели
│   ├── migrations/            # Alembic миграции
│   └── setup.py               # Database setup
├── config/
│   ├── __init__.py
│   └── settings.py            # Конфигурация
├── tests/
│   ├── test_crawler.py
│   ├── test_rag.py
│   ├── test_mcp_server.py
│   └── test_integration.py
├── docker-compose.yml         # Development environment
├── requirements.txt
├── .env.example
└── README.md
```

### Known Gotchas & Library Quirks
```python
# CRITICAL: MCP сървър изисква async/await навсякъде
# CRITICAL: pgvector изисква специфични SQL заявки за векторно търсене
# CRITICAL: GPT-4o-mini има rate limits - 2000 req/min
# CRITICAL: Scrapy robots.txt compliance за eufunds.bg
# CRITICAL: Български текст изисква UTF-8 encoding навсякъде
# CRITICAL: LlamaIndex chunking стратегии за български език
# CRITICAL: Supabase connection pooling за concurrent requests
```

## Implementation Blueprint

### Data Models and Structure
```python
# models.py - Core data structures
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum

class DocumentType(str, Enum):
    PROGRAM = "program"
    CALL = "call"
    GUIDELINE = "guideline"
    NEWS = "news"

class FundingDocument(BaseModel):
    id: Optional[int] = None
    url: str = Field(..., description="Оригинален URL на документа")
    title: str = Field(..., min_length=1)
    content: str = Field(..., description="Извлечено съдържание")
    content_hash: str = Field(..., description="MD5 hash за проверка на промени")
    document_type: DocumentType
    metadata: Dict[str, Any] = Field(default_factory=dict)
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)

class FundingProgram(BaseModel):
    name: str = Field(..., description="Име на програмата")
    deadline: Optional[datetime] = None
    budget_min: Optional[float] = None
    budget_max: Optional[float] = None
    eligible_entities: List[str] = Field(default_factory=list)
    status: str = Field(default="active")
    description: str = ""

class SearchQuery(BaseModel):
    query: str = Field(..., min_length=1)
    limit: int = Field(10, ge=1, le=50)
    document_types: List[DocumentType] = Field(default_factory=list)
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None

class SearchResult(BaseModel):
    document: FundingDocument
    score: float = Field(ge=0.0, le=1.0)
    highlights: List[str] = Field(default_factory=list)
```

### List of Tasks to be Completed
```yaml
Task 1: Database Setup and Models
CREATE database/models.py:
  - SQLAlchemy модели за документи, програми, chunks
  - pgvector extension setup
  - Индекси за бързо търсене
CREATE database/setup.py:
  - Supabase connection setup
  - Таблици creation
  - Vector extension activation

Task 2: Intelligent Web Crawler
CREATE crawler/spider.py:
  - Scrapy spider за eufunds.bg
  - Robots.txt compliance
  - Rate limiting и error handling
CREATE crawler/processors.py:
  - GPT-4o-mini интеграция за HTML → Markdown
  - Content hash generation за change detection
  - Structured data extraction

Task 3: Embedding and RAG System
CREATE rag/embeddings.py:
  - multilingual-e5-large модел setup
  - Batch processing за ефективност
  - Caching механизъм
CREATE rag/retrieval.py:
  - Хибридно търсене (semantic + keyword)
  - PostgreSQL full-text search
  - Result ranking и fusion

Task 4: Multimodal Processing
CREATE rag/multimodal.py:
  - Table extraction с unstructured
  - OCR за изображения с easyocr
  - PDF processing
  - Structured data preservation

Task 5: MCP Server Implementation
CREATE mcp_server/server.py:
  - MCP протокол имплементация
  - Tool registration
  - Error handling и logging
CREATE mcp_server/tools.py:
  - search_funding_programs tool
  - get_program_details tool
  - semantic_search tool

Task 6: Configuration and Environment
CREATE config/settings.py:
  - Pydantic settings с environment variables
  - Database connection strings
  - API keys management
CREATE .env.example:
  - Всички необходими environment variables
  - Документация за setup

Task 7: Testing Suite
CREATE tests/:
  - Unit tests за всеки компонент
  - Integration tests за RAG pipeline
  - MCP server tests
  - Mock external dependencies

Task 8: Documentation and Deployment
CREATE README.md:
  - Setup инструкции
  - Architecture overview
  - Usage examples
CREATE docker-compose.yml:
  - Development environment
  - Supabase local setup
  - Volume mounts
```

### Per Task Pseudocode
```python
# Task 2: Intelligent Web Crawler
class EUFundsSpider(scrapy.Spider):
    name = 'eufunds'
    allowed_domains = ['eufunds.bg']

    async def parse(self, response):
        # PATTERN: Extract document links
        doc_links = response.css('a[href*="/program/"]::attr(href)').getall()

        for link in doc_links:
            # GOTCHA: Check content hash before processing
            content_hash = self.get_content_hash(response.url)
            if not self.is_content_changed(content_hash):
                continue

            # CRITICAL: Use GPT-4o-mini for intelligent extraction
            structured_content = await self.extract_with_llm(response.text)
            yield FundingDocument(**structured_content)

async def extract_with_llm(html_content: str) -> dict:
    """Extract structured data using GPT-4o-mini"""
    prompt = """
    Извлечи структурирана информация от този HTML за ЕС фонд:
    - Име на програмата
    - Краен срок за кандидатстване
    - Бюджет (мин/макс)
    - Допустими кандидати
    - Описание

    Върни JSON формат на български език.
    """
    # PATTERN: Use OpenAI client with rate limiting
    response = await openai_client.chat.completions.create(
        model="gpt-4o-mini",
        messages=[{"role": "user", "content": f"{prompt}\n\n{html_content}"}],
        temperature=0.1
    )
    return json.loads(response.choices[0].message.content)

# Task 3: Hybrid RAG Retrieval
async def hybrid_search(
    query: str,
    limit: int = 10,
    semantic_weight: float = 0.7
) -> List[SearchResult]:
    """Combine semantic and keyword search"""

    # PATTERN: Generate query embedding
    query_embedding = embedding_model.encode(query)

    # Semantic search with pgvector
    semantic_sql = """
    SELECT id, content, title,
           1 - (embedding <=> %s::vector) as semantic_score
    FROM documents
    ORDER BY embedding <=> %s::vector
    LIMIT %s
    """

    # Keyword search with PostgreSQL FTS
    keyword_sql = """
    SELECT id, content, title,
           ts_rank(ts_vector, plainto_tsquery('bulgarian', %s)) as keyword_score
    FROM documents
    WHERE ts_vector @@ plainto_tsquery('bulgarian', %s)
    ORDER BY keyword_score DESC
    LIMIT %s
    """

    # CRITICAL: Combine results using RRF (Reciprocal Rank Fusion)
    return combine_results_rrf(semantic_results, keyword_results, semantic_weight)

# Task 5: MCP Server Tools
@mcp_server.tool
async def search_funding_programs(
    query: str,
    limit: int = 10,
    document_types: List[str] = None
) -> List[Dict[str, Any]]:
    """Search for EU funding programs"""

    # PATTERN: Validate input with Pydantic
    search_request = SearchQuery(
        query=query,
        limit=limit,
        document_types=document_types or []
    )

    # CRITICAL: Use hybrid search
    results = await hybrid_search(
        query=search_request.query,
        limit=search_request.limit
    )

    return [result.dict() for result in results]

@mcp_server.tool
async def get_program_details(program_id: int) -> Dict[str, Any]:
    """Get detailed information about a specific program"""

    # PATTERN: Database query with error handling
    async with database.get_session() as session:
        program = await session.get(FundingProgram, program_id)
        if not program:
            raise ValueError(f"Program with ID {program_id} not found")

        return program.dict()
```

### Integration Points
```yaml
ENVIRONMENT:
  - add to: .env
  - vars: |
      # Database
      SUPABASE_URL=postgresql://...
      SUPABASE_ANON_KEY=...

      # LLM для crawler
      OPENAI_API_KEY=sk-...

      # Embedding model (local)
      EMBEDDING_MODEL_PATH=./models/multilingual-e5-large

      # Crawler settings
      CRAWL_FREQUENCY_HOURS=24
      MAX_CONCURRENT_REQUESTS=5

CONFIG:
  - pgvector: CREATE EXTENSION IF NOT EXISTS vector;
  - Bulgarian FTS: CREATE TEXT SEARCH CONFIGURATION bulgarian_config ...
  - Indexes: CREATE INDEX ON documents USING ivfflat (embedding vector_cosine_ops);

DEPENDENCIES:
  - Update requirements.txt with:
    - mcp
    - scrapy
    - playwright
    - llama-index
    - sentence-transformers
    - psycopg2-binary
    - sqlalchemy
    - alembic
    - unstructured
    - easyocr
```

## Validation Loop

### Level 1: Syntax & Style
```bash
# Run these FIRST - fix any errors before proceeding
ruff check . --fix          # Auto-fix style issues
mypy .                      # Type checking
pytest tests/unit/ -v       # Unit tests
# Expected: No errors. If errors, READ and fix.
```

### Level 2: Component Tests
```python
# test_crawler.py
async def test_spider_extracts_programs():
    """Test spider correctly extracts program information"""
    spider = EUFundsSpider()
    response = fake_response_from_file('eufunds_program_page.html')

    results = list(spider.parse(response))
    assert len(results) > 0
    assert all(isinstance(r, FundingDocument) for r in results)

# test_rag.py
async def test_hybrid_search_bulgarian():
    """Test hybrid search works with Bulgarian queries"""
    results = await hybrid_search("програми за иновации", limit=5)

    assert len(results) <= 5
    assert all(r.score > 0 for r in results)
    assert any("иновации" in r.document.content.lower() for r in results)

# test_mcp_server.py
async def test_mcp_search_tool():
    """Test MCP search tool returns valid results"""
    result = await search_funding_programs(
        query="дигитални технологии",
        limit=3
    )

    assert len(result) <= 3
    assert all("title" in item for item in result)
```

### Level 3: Integration Test
```bash
# Test full pipeline
python -m crawler.spider    # Should crawl and store documents
python -m rag.pipeline      # Should create embeddings
python -m mcp_server.server  # Should start MCP server

# Test MCP client interaction
echo '{"method": "tools/call", "params": {"name": "search_funding_programs", "arguments": {"query": "иновации"}}}' | nc localhost 8000

# Expected: JSON response with funding programs
```

## Final Validation Checklist
- [ ] All tests pass: `pytest tests/ -v`
- [ ] No linting errors: `ruff check .`
- [ ] No type errors: `mypy .`
- [ ] Crawler successfully extracts from eufunds.bg
- [ ] RAG system returns relevant results for Bulgarian queries
- [ ] MCP server responds to tool calls
- [ ] Database properly stores and retrieves documents
- [ ] Hybrid search combines semantic and keyword results
- [ ] Multimodal processing handles tables and images
- [ ] Error cases handled gracefully
- [ ] README includes clear setup instructions
- [ ] .env.example has all required variables

---

## Anti-Patterns to Avoid
- ❌ Don't hardcode API keys - use environment variables
- ❌ Don't ignore robots.txt for web crawling
- ❌ Don't skip content hash checking (will re-process same content)
- ❌ Don't use sync functions in async MCP context
- ❌ Don't forget UTF-8 encoding for Bulgarian text
- ❌ Don't skip pgvector index creation (slow queries)
- ❌ Don't commit large embedding models to git

## Confidence Score: 8/10
High confidence due to:
- Clear open-source technology stack
- Well-documented APIs and libraries
- Established patterns for RAG systems
- MCP protocol specification available

Minor uncertainty on:
- Bulgarian language optimization specifics
- eufunds.bg crawling policies and rate limits
- GPT-4o-mini cost optimization strategies
