# EU Funds MCP Server - Product Requirements Prompt (PRP)

## ГЛОБАЛЕН ЗАКОН
**ПРАВИШ ВСИЧКО КАТО ПРЕДИ ТОВА ПРОЧИТАШ ДОКУМЕНТА ЗА КОНТЕКСТ КОЙТО ТИ ИЗГРАДИ - ДВИЖИШ СЕ САМО ПО НЕГО - СЛЕД ВСЯКО НЕЩО КОЕТО ПРАВИШ ОТНОВО ГО ПРОЧИТАШ**

## Executive Summary

### Project Vision
Create an enterprise-grade MCP (Model Context Protocol) server that democratizes access to European Union funding programs information specifically for Bulgaria. The system serves as a "tool for tools" - enabling AI assistants to access accurate, verified, and current EU funds information through a standardized protocol.

### Key Success Metrics
- **Functionality**: 100% MCP v1.0 protocol compliance with all tools operational
- **Performance**: Sub-second response times for 95% of queries
- **Accuracy**: >90% relevance for Bulgarian domain-specific queries
- **Reliability**: 99.9% uptime with comprehensive error handling
- **Scalability**: Support for 1000+ concurrent requests

## Technical Architecture

### Core Technology Stack (2025 State-of-the-Art)
- **Protocol**: MCP v1.0 for seamless AI assistant integration
- **Web Scraping**: Crawl4AI v0.6.0 with browser pooling and world-aware crawling
- **Database**: Supabase (PostgreSQL) with pgvector 0.7.0+ and HNSW indexing
- **Embeddings**: multilingual-e5-large-instruct (1024 dimensions, Bulgarian-optimized)
- **Reranking**: cross-encoder/ms-marco-MiniLM-L-6-v2 for result optimization
- **Framework**: FastAPI with Pydantic v2 for high-performance async operations
- **Language**: Python 3.12+ with modern async patterns

### Production Configuration
```env
# Production credentials are stored in .env file - NEVER commit API keys to git!
OPENAI_API_KEY=sk-proj-YOUR_OPENAI_API_KEY_HERE
MODEL_CHOICE=gpt-4o-mini
SUPABASE_URL=https://YOUR_PROJECT_ID.supabase.co
SUPABASE_SERVICE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.YOUR_JWT_TOKEN_HERE
```

### Hybrid Search Optimization
- **Reranker Weights**: CE: 0.3, Hybrid: 0.7 (optimized for Bulgarian content)
- **HNSW Parameters**: m=16, ef_construction=64 for optimal vector search performance
- **Chunking Strategy**: Context 7-inspired semantic chunking by headers and content structure

## Implementation Plan

### Phase 1: Foundation Setup (Week 1)
**Objective**: Establish robust foundation with MCP protocol compliance

#### Critical Success Criteria:
1. ✅ Project structure follows examples/mcp_server_example.py pattern
2. ✅ Configuration management using examples/config_example.py with production API keys
3. ✅ MCP v1.0 protocol compliance with proper tool schemas
4. ✅ Structured logging with JSON format and correlation IDs
5. ✅ Health check endpoints operational

#### Implementation Steps:
1. **Project Structure Creation** (30 minutes)
   - Create directory structure: src/, tests/, examples/, docs/
   - Initialize pyproject.toml with exact dependencies from mcp-crawl4ai-rag
   - Setup .env with provided production credentials
   - Create .gitignore for Python projects

2. **Configuration Management** (45 minutes)
   - Implement Pydantic Settings following examples/config_example.py
   - Add validation for all API keys and URLs
   - Configure Bulgarian language settings and stopwords
   - Test configuration loading and validation

3. **MCP Server Foundation** (60 minutes)
   - Implement FastMCP server following examples/mcp_server_example.py
   - Add lifespan management for resource initialization
   - Create health check endpoint with component status
   - Implement structured logging with correlation IDs

4. **Database Connection** (45 minutes)
   - Setup Supabase client with connection pooling
   - Create database schema for EU funds content
   - Implement pgvector extension setup
   - Test database connectivity and operations

### Phase 2: Content Processing Pipeline (Week 2)
**Objective**: Implement Bulgarian-optimized content processing

#### Critical Success Criteria:
1. ✅ Crawl4AI integration with rate limiting and error handling
2. ✅ Bulgarian text processing with Cyrillic character support
3. ✅ multilingual-e5-large-instruct embedding generation
4. ✅ Content chunking with semantic awareness
5. ✅ Vector storage with HNSW indexing

#### Implementation Steps:
1. **Web Scraping Infrastructure** (90 minutes)
   - Integrate Crawl4AI v0.6.0 with browser pooling
   - Implement rate limiting and respectful crawling
   - Add error handling and retry mechanisms
   - Create content extraction and cleaning pipelines

2. **Bulgarian Language Processing** (75 minutes)
   - Implement Cyrillic text normalization
   - Add Bulgarian stopword removal and tokenization
   - Create morphological analysis for Bulgarian text
   - Implement semantic chunking by headers and content

3. **Embedding Processing** (60 minutes)
   - Setup multilingual-e5-large-instruct integration
   - Implement batch embedding processing
   - Add embedding quality validation for Bulgarian
   - Create vector storage with HNSW indexing

### Phase 3: Hybrid Search Implementation (Week 3)
**Objective**: Advanced RAG with Bulgarian optimization

#### Critical Success Criteria:
1. ✅ Vector similarity search with pgvector HNSW
2. ✅ Full-text search with Bulgarian language support
3. ✅ Hybrid search with weighted result fusion
4. ✅ Cross-encoder reranking for relevance optimization
5. ✅ Sub-second response times for 95% of queries

#### Implementation Steps:
1. **Search Infrastructure** (90 minutes)
   - Implement vector similarity search with HNSW
   - Add full-text search with Bulgarian language support
   - Create hybrid search algorithm with weighted fusion
   - Implement result ranking and relevance scoring

2. **Reranking System** (60 minutes)
   - Integrate cross-encoder/ms-marco-MiniLM-L-6-v2
   - Implement result reranking with CE: 0.3, Hybrid: 0.7 weights
   - Add performance optimization for reranking
   - Create A/B testing framework for search quality

3. **MCP Tools Implementation** (120 minutes)
   - Implement search_eu_funds tool with Bulgarian examples
   - Add get_funding_programs tool for program discovery
   - Create analyze_eligibility tool for criteria matching
   - Implement get_application_deadlines for time-sensitive info
   - Add get_funding_sources for data source management

### Phase 4: Production Readiness (Week 4)
**Objective**: Enterprise-grade reliability and performance

#### Critical Success Criteria:
1. ✅ Comprehensive test suite with 90%+ coverage
2. ✅ Performance optimization for sub-second responses
3. ✅ Monitoring and alerting infrastructure
4. ✅ Security hardening and API key protection
5. ✅ Complete documentation and deployment guides

#### Implementation Steps:
1. **Testing Infrastructure** (90 minutes)
   - Create unit tests with async support and 90%+ coverage
   - Implement integration tests with real Supabase
   - Add MCP protocol compliance tests
   - Create Bulgarian content quality validation tests

2. **Performance Optimization** (75 minutes)
   - Implement intelligent caching for frequent queries
   - Add connection pooling optimization
   - Create performance monitoring and metrics
   - Optimize database queries and indexing

3. **Production Deployment** (60 minutes)
   - Create Docker configuration for containerized deployment
   - Implement security hardening and API key protection
   - Add monitoring and alerting infrastructure
   - Create deployment guides and documentation

## Context Engineering Integration

### Self-Correcting Documentation
This PRP includes comprehensive validation steps and success criteria for each phase. All implementation decisions are documented to enable seamless project continuation across different sessions.

### Working Examples Integration
- **mcp_server_example.py**: Complete server structure template
- **config_example.py**: Configuration management patterns
- **database_example.py**: Supabase integration patterns
- **search_example.py**: Hybrid search implementation

### Professional Standards
- **Code Quality**: Enterprise-grade patterns with comprehensive error handling
- **Documentation**: Complete API documentation with Bulgarian examples
- **Testing**: Comprehensive test suite with performance validation
- **Security**: Secure API key management and access controls

## Bulgarian Language Optimization

### Text Processing Requirements
1. **Cyrillic Character Handling**: Proper UTF-8 encoding and normalization
2. **Morphological Analysis**: Bulgarian-specific tokenization and stemming
3. **Stopword Removal**: Comprehensive Bulgarian stopword list
4. **Search Optimization**: Full-text search with Bulgarian language support
5. **Embedding Validation**: Quality assurance for Bulgarian content embeddings

### Content Sources
- European Commission Funding & Tenders Portal
- Bulgaria EU Funds Portal (eufunds.bg)
- Operational Programmes Bulgaria
- Ministry of European Funds Bulgaria
- Regional development agencies

## Validation and Success Criteria

### Functional Validation
- [ ] All MCP tools respond correctly with proper schemas
- [ ] Bulgarian text processing handles Cyrillic characters correctly
- [ ] Search results show high relevance for Bulgarian queries
- [ ] Error handling provides meaningful messages
- [ ] Health checks report accurate system status

### Performance Validation
- [ ] 95% of queries respond in under 1 second
- [ ] System supports 1000+ concurrent requests
- [ ] Database operations complete within SLA
- [ ] Memory usage remains within acceptable limits
- [ ] CPU utilization optimized for efficiency

### Quality Validation
- [ ] Bulgarian content retrieval >90% relevance
- [ ] Embedding quality validated for Bulgarian text
- [ ] Search ranking optimized for domain-specific queries
- [ ] Content freshness maintained through automated updates
- [ ] Data accuracy verified against official sources

## Risk Mitigation

### Technical Risks
- **API Rate Limits**: Implement intelligent rate limiting and caching
- **Content Quality**: Multi-layer validation for Bulgarian content accuracy
- **Performance**: Continuous monitoring with automated optimization
- **Scalability**: Modular architecture supporting horizontal scaling

### Operational Risks
- **Security**: Secure API key management and access controls
- **Reliability**: Comprehensive error handling and graceful degradation
- **Maintenance**: Automated content updates and monitoring
- **Compliance**: GDPR compliance for data processing

This PRP follows Context Engineering methodology to ensure successful implementation with minimal iterations and maximum reliability. All patterns are based on proven implementations from Cole Medin's repositories and 2025 best practices.
