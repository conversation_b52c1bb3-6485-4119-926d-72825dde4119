name: "MCP Сървър за ЕС Фондове: Enterprise-Grade RAG система за европейски финансиращи програми"
description: |
  ## Purpose
  Създаване на production-ready MCP (Model Context Protocol) сървър, който предоставя точна, актуална информация за европейски финансиращи програми на LLM-и. Системата автоматизира процеса на събиране, обработка и предоставяне на информация за ЕС фондове в България с enterprise-grade качество и надеждност.

  ## Core Principles
  1. **Production-First**: Enterprise-grade архитектура с мониторинг, логове и error handling
  2. **Open-Source Excellence**: Най-добрите безплатни технологии за 2025 година
  3. **Модулност**: Микросервисна архитектура с независими компоненти
  4. **AI-Native**: Интелигентно извличане с LLM-friendly обработка
  5. **Мултимодалност**: Обработка на текст, таблици, PDF-и и изображения
  6. **Български език**: State-of-the-art оптимизация за български език

---

## Goal
Създаване на enterprise-ready MCP сървър, който:
- Автоматично обхожда официални сайтове (eufunds.bg, opic.bg, esif.bg и др.)
- Извлича и структурира нова информация с AI-powered content filtering
- Предоставя state-of-the-art мултимодална RAG система за български език
- Служи като надежден "инструмент за инструменти" за други LLM-и
- Поддържа real-time updates и change detection

## Why
- **Бизнес стойност**: Демократизира достъпа до информация за ЕС фондове
- **Проблем**: Сложен, скъп процес на търсене и следене на финансиращи програми
- **Решение**: AI-powered автоматизация с enterprise-grade надеждност
- **ROI**: Намалява нуждата от скъпи консултации и ръчно търсене

## What
Enterprise MCP сървър със следните компоненти:
- **Intelligent Web Crawler**: Crawl4AI-powered система с LLM content filtering
- **Advanced Database**: Supabase PostgreSQL с pgvector и hybrid search
- **State-of-the-art RAG**: multilingual-e5-large + BM25 hybrid система
- **Production Infrastructure**: Docker, monitoring, caching, error recovery

### Success Criteria
- [ ] Краулер успешно извлича нова информация от eufunds.bg
- [ ] RAG система отговаря точно на въпроси за ЕС фондове на български
- [ ] Хибридно търсене комбинира семантично и keyword търсене
- [ ] Мултимодална обработка на таблици и изображения
- [ ] MCP протокол правилно експозира функционалността
- [ ] Всички тестове преминават и кодът отговаря на стандартите

## Technical Architecture (2025 State-of-the-Art)

### Core Technology Stack

#### Web Crawling & Content Extraction
- **Crawl4AI v0.6.0**: Latest LLM-friendly web scraping framework
  - Browser pooling with pre-warmed instances for performance
  - World-aware crawling (geolocation, locale, timezone)
  - Network traffic capture and console logging
  - Table-to-DataFrame extraction for structured data
  - MHTML snapshots for debugging
  - Built-in content filtering with BM25 and pruning strategies

#### Database & Vector Search
- **Supabase PostgreSQL with pgvector 0.7.0+**:
  - HNSW indexing for faster vector search
  - Hybrid search combining semantic + full-text search
  - Real-time subscriptions for live updates
  - Row Level Security (RLS) for multi-tenant architecture
  - Point-in-time recovery and automated backups
  - Connection pooling with PgBouncer

#### Embedding & RAG System
- **multilingual-e5-large-instruct**: State-of-the-art multilingual embeddings
  - Proven superior performance on Bulgarian language tasks
  - 1024-dimensional vectors with instruction-following capabilities
  - Optimized for retrieval tasks with query-document asymmetry
- **Advanced Hybrid Search**:
  - Reciprocal Rank Fusion (RRF) for combining search results
  - BM25 + semantic search with adaptive weighting
  - Query expansion and rewriting for Bulgarian language
- **LlamaIndex 0.10+**: Production-ready RAG framework with advanced features

#### LLM Integration & Processing
- **GPT-4o-mini**: Cost-effective model for intelligent content extraction
- **Local LLM Support**: Ollama/vLLM for privacy and cost optimization
- **MCP Protocol v1.0**: Latest Model Context Protocol specification
- **Structured Output**: Pydantic v2 for robust data validation

#### Production Infrastructure
- **Docker Multi-stage**: Optimized containerization with security scanning
- **FastAPI**: High-performance async API framework
- **Redis**: Caching and session management
- **Prometheus + Grafana**: Monitoring and alerting
- **Structured Logging**: JSON logs with correlation IDs
- **Health Checks**: Comprehensive system monitoring

## All Needed Context

### Documentation & References
```yaml
# MUST READ - Include these in your context window
- url: https://spec.modelcontextprotocol.io/
  why: MCP протокол спецификация v1.0 и имплементация
- url: https://docs.crawl4ai.com/
  why: Crawl4AI v0.6.0 документация и best practices
- url: https://supabase.com/docs/guides/ai/vector-embeddings
  why: pgvector 0.7.0+ setup и HNSW индексиране
- url: https://docs.llamaindex.ai/en/stable/
  why: LlamaIndex 0.10+ за production RAG система
- url: https://huggingface.co/intfloat/multilingual-e5-large-instruct
  why: State-of-the-art ембединг модел за български език
- url: https://github.com/unclecode/crawl4ai
  why: Crawl4AI GitHub repo с примери и документация
- url: https://eufunds.bg/
  why: Основен източник на данни за ЕС фондове
- url: https://opic.bg/
  why: Оперативна програма "Иновации и конкурентоспособност"
- url: https://esif.bg/
  why: Европейски структурни и инвестиционни фондове
- url: https://fastapi.tiangolo.com/
  why: FastAPI за high-performance async API
- url: https://pydantic.dev/
  why: Pydantic v2 за data validation и settings
```

### Production-Ready Codebase Structure
```bash
# Enterprise-grade структура на проекта (2025 best practices)
eu-funds-mcp-server/
├── src/
│   ├── mcp_server/
│   │   ├── __init__.py
│   │   ├── server.py              # MCP сървър с FastAPI
│   │   ├── tools.py               # MCP tools за RAG операции
│   │   ├── models.py              # Pydantic v2 модели
│   │   └── middleware.py          # Auth, CORS, rate limiting
│   ├── crawler/
│   │   ├── __init__.py
│   │   ├── crawl4ai_spider.py     # Crawl4AI-based crawler
│   │   ├── content_processor.py   # GPT-4o-mini content filtering
│   │   ├── scheduler.py           # APScheduler за periodic crawling
│   │   └── change_detector.py     # Content change detection
│   ├── rag/
│   │   ├── __init__.py
│   │   ├── embeddings.py          # multilingual-e5-large-instruct
│   │   ├── hybrid_retriever.py    # RRF + BM25 + semantic search
│   │   ├── query_processor.py     # Bulgarian query expansion
│   │   ├── generator.py           # LlamaIndex 0.10+ RAG pipeline
│   │   └── chunking.py            # Intelligent text chunking
│   ├── database/
│   │   ├── __init__.py
│   │   ├── supabase_client.py     # Supabase connection & operations
│   │   ├── models.py              # Database models
│   │   ├── migrations/            # Supabase migrations
│   │   └── vector_operations.py   # pgvector HNSW operations
│   ├── core/
│   │   ├── __init__.py
│   │   ├── config.py              # Pydantic settings management
│   │   ├── logging.py             # Structured JSON logging
│   │   ├── monitoring.py          # Prometheus metrics
│   │   └── exceptions.py          # Custom exception handling
│   └── utils/
│       ├── __init__.py
│       ├── text_processing.py     # Bulgarian text utilities
│       ├── caching.py             # Redis caching layer
│       └── security.py           # JWT, rate limiting, validation
├── tests/
│   ├── unit/                      # Unit тестове с pytest
│   ├── integration/               # Integration тестове
│   ├── e2e/                       # End-to-end тестове
│   ├── performance/               # Load testing с locust
│   └── conftest.py                # Pytest configuration
├── docker/
│   ├── Dockerfile.multi-stage     # Multi-stage production build
│   ├── docker-compose.yml         # Development environment
│   ├── docker-compose.prod.yml    # Production environment
│   └── .dockerignore
├── k8s/                           # Kubernetes manifests
│   ├── deployment.yaml
│   ├── service.yaml
│   ├── configmap.yaml
│   └── ingress.yaml
├── monitoring/
│   ├── prometheus.yml             # Prometheus configuration
│   ├── grafana/                   # Grafana dashboards
│   └── alerts.yml                 # Alerting rules
├── docs/
│   ├── api.md                     # OpenAPI/Swagger documentation
│   ├── deployment.md              # Production deployment guide
│   ├── architecture.md            # System architecture
│   ├── performance.md             # Performance benchmarks
│   └── security.md                # Security considerations
├── scripts/
│   ├── setup.sh                   # Development setup
│   ├── deploy.sh                  # Deployment script
│   └── backup.sh                  # Database backup
├── .github/
│   ├── workflows/
│   │   ├── ci.yml                 # Continuous Integration
│   │   ├── cd.yml                 # Continuous Deployment
│   │   └── security.yml           # Security scanning
│   └── ISSUE_TEMPLATE/
├── pyproject.toml                 # Modern Python project config
├── requirements/
│   ├── base.txt                   # Core dependencies
│   ├── dev.txt                    # Development dependencies
│   └── prod.txt                   # Production dependencies
├── .env.example                   # Environment variables template
├── .pre-commit-config.yaml        # Pre-commit hooks
├── Makefile                       # Development commands
└── README.md                      # Comprehensive project overview
```

### Known Gotchas & Library Quirks
```python
# CRITICAL: MCP сървър изисква async/await навсякъде
# CRITICAL: pgvector 0.7.0+ изисква HNSW индекси за performance
# CRITICAL: GPT-4o-mini има rate limits - 2000 req/min
# CRITICAL: Crawl4AI browser pooling configuration за memory management
# CRITICAL: Български текст изисква UTF-8 encoding навсякъде
# CRITICAL: multilingual-e5-large-instruct специфични preprocessing steps
# CRITICAL: Supabase connection pooling за concurrent requests
# CRITICAL: FastAPI async context managers за proper resource cleanup
# CRITICAL: Redis TTL configuration за caching strategy
# CRITICAL: Prometheus metrics naming conventions
```

## Development Phases (Professional Implementation Strategy)

### Phase 1: Foundation & Infrastructure (Week 1-2)
**Цел**: Създаване на solid foundation с production-ready infrastructure

#### 1.1 Project Setup & DevOps
- [ ] **Repository Setup**: Git repo с branch protection rules
- [ ] **Development Environment**: Docker Compose за local development
- [ ] **CI/CD Pipeline**: GitHub Actions за automated testing и deployment
- [ ] **Code Quality**: Pre-commit hooks, linting, formatting
- [ ] **Documentation**: README, contributing guidelines, code of conduct

#### 1.2 Core Infrastructure
- [ ] **Database Setup**: Supabase project с pgvector extension
- [ ] **Environment Configuration**: Pydantic settings management
- [ ] **Logging System**: Structured JSON logging с correlation IDs
- [ ] **Monitoring Setup**: Prometheus metrics и health checks
- [ ] **Security Foundation**: JWT authentication, rate limiting

#### 1.3 Basic Project Structure
- [ ] **Package Structure**: Създаване на src/ layout
- [ ] **Core Modules**: config, logging, exceptions, utils
- [ ] **Testing Framework**: pytest setup с fixtures и mocks
- [ ] **Documentation**: API documentation с OpenAPI/Swagger

### Phase 2: Data Layer & Crawling (Week 3-4)
**Цел**: Robust data collection и storage система

#### 2.1 Database Models & Operations
- [ ] **Supabase Models**: Database schema за documents, embeddings
- [ ] **Vector Operations**: pgvector HNSW indexing setup
- [ ] **Migration System**: Database versioning и rollback capability
- [ ] **Connection Management**: Connection pooling и retry logic

#### 2.2 Web Crawling System
- [ ] **Crawl4AI Integration**: Browser pooling configuration
- [ ] **Content Processing**: GPT-4o-mini за intelligent extraction
- [ ] **Change Detection**: Content hashing за update detection
- [ ] **Scheduler**: APScheduler за periodic crawling
- [ ] **Error Handling**: Robust retry logic и failure recovery

#### 2.3 Data Processing Pipeline
- [ ] **Text Preprocessing**: Bulgarian language optimization
- [ ] **Content Filtering**: BM25 и pruning strategies
- [ ] **Chunking Strategy**: Intelligent text segmentation
- [ ] **Metadata Extraction**: Structured data from HTML

### Phase 3: RAG System & Embeddings (Week 5-6)
**Цел**: State-of-the-art RAG система за български език

#### 3.1 Embedding System
- [ ] **Model Integration**: multilingual-e5-large-instruct setup
- [ ] **Batch Processing**: Efficient embedding generation
- [ ] **Vector Storage**: Optimized pgvector operations
- [ ] **Embedding Cache**: Redis caching за performance

#### 3.2 Hybrid Search Implementation
- [ ] **Semantic Search**: Vector similarity с HNSW
- [ ] **Keyword Search**: PostgreSQL full-text search
- [ ] **Hybrid Fusion**: Reciprocal Rank Fusion (RRF)
- [ ] **Query Processing**: Bulgarian query expansion

#### 3.3 RAG Pipeline
- [ ] **LlamaIndex Integration**: Document-centric RAG
- [ ] **Context Management**: Intelligent context window usage
- [ ] **Response Generation**: Template-based responses
- [ ] **Quality Metrics**: Retrieval accuracy measurement

### Phase 4: MCP Server & API (Week 7-8)
**Цел**: Production-ready MCP server с comprehensive API

#### 4.1 MCP Server Implementation
- [ ] **FastAPI Server**: High-performance async API
- [ ] **MCP Protocol**: v1.0 specification compliance
- [ ] **Tool Definitions**: RAG operations като MCP tools
- [ ] **Authentication**: JWT-based security

#### 4.2 API Endpoints
- [ ] **Search Endpoints**: Hybrid search API
- [ ] **Document Management**: CRUD operations
- [ ] **Health Monitoring**: System status endpoints
- [ ] **Admin Interface**: Management operations

#### 4.3 Integration & Testing
- [ ] **Integration Tests**: End-to-end testing
- [ ] **Performance Testing**: Load testing с locust
- [ ] **Security Testing**: Vulnerability scanning
- [ ] **Documentation**: Complete API documentation

### Phase 5: Production Deployment & Optimization (Week 9-10)
**Цел**: Production deployment с monitoring и optimization

#### 5.1 Production Infrastructure
- [ ] **Docker Production**: Multi-stage optimized builds
- [ ] **Kubernetes**: Production-ready K8s manifests
- [ ] **Load Balancing**: Traffic distribution strategy
- [ ] **SSL/TLS**: Security certificates setup

#### 5.2 Monitoring & Observability
- [ ] **Prometheus Metrics**: Comprehensive system metrics
- [ ] **Grafana Dashboards**: Visual monitoring
- [ ] **Alerting Rules**: Proactive issue detection
- [ ] **Log Aggregation**: Centralized logging

#### 5.3 Performance Optimization
- [ ] **Caching Strategy**: Multi-layer caching
- [ ] **Database Optimization**: Query optimization и indexing
- [ ] **Resource Tuning**: Memory и CPU optimization
- [ ] **Scalability Testing**: Load testing и capacity planning

### Phase 6: Advanced Features & Maintenance (Week 11-12)
**Цел**: Advanced features и long-term maintenance strategy

#### 6.1 Advanced Features
- [ ] **Real-time Updates**: WebSocket notifications
- [ ] **Advanced Analytics**: Usage analytics и insights
- [ ] **Multi-language Support**: Expansion beyond Bulgarian
- [ ] **API Versioning**: Backward compatibility strategy

#### 6.2 Maintenance & Operations
- [ ] **Backup Strategy**: Automated backups и disaster recovery
- [ ] **Update Procedures**: Rolling updates и rollback procedures
- [ ] **Security Audits**: Regular security assessments
- [ ] **Performance Monitoring**: Continuous optimization

## Implementation Blueprint

### Data Models and Structure (Pydantic v2)
```python
# models.py - Production-ready data structures
from pydantic import BaseModel, Field, ConfigDict, field_validator
from typing import List, Optional, Dict, Any, Annotated
from datetime import datetime, timezone
from enum import Enum
import hashlib
from uuid import UUID, uuid4

class DocumentType(str, Enum):
    """Document type enumeration for EU funds content"""
    PROGRAM = "program"
    CALL = "call"
    GUIDELINE = "guideline"
    NEWS = "news"
    REGULATION = "regulation"
    REPORT = "report"

class ProcessingStatus(str, Enum):
    """Processing status for documents"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"

class FundingDocument(BaseModel):
    """Core document model with comprehensive metadata"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        validate_assignment=True,
        use_enum_values=True,
        frozen=False
    )

    id: Optional[UUID] = Field(default_factory=uuid4, description="Unique document identifier")
    url: Annotated[str, Field(min_length=1, max_length=2048, description="Original document URL")]
    title: Annotated[str, Field(min_length=1, max_length=500, description="Document title")]
    content: str = Field(description="Extracted content in markdown format")
    content_hash: str = Field(description="SHA-256 hash for change detection")
    document_type: DocumentType
    language: str = Field(default="bg", description="Document language (ISO 639-1)")

    # Metadata fields
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    source_domain: Optional[str] = Field(None, description="Source website domain")
    crawl_timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    last_modified: Optional[datetime] = None
    processing_status: ProcessingStatus = Field(default=ProcessingStatus.PENDING)

    # Vector embeddings
    embedding_vector: Optional[List[float]] = Field(None, description="Document embedding vector")
    embedding_model: Optional[str] = Field(None, description="Model used for embeddings")

    # Timestamps
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

    @field_validator('content_hash', mode='before')
    @classmethod
    def generate_content_hash(cls, v: str, info) -> str:
        """Generate SHA-256 hash if not provided"""
        if not v and 'content' in info.data:
            content = info.data['content']
            return hashlib.sha256(content.encode('utf-8')).hexdigest()
        return v

class FundingProgram(BaseModel):
    """Funding program with detailed information"""
    model_config = ConfigDict(str_strip_whitespace=True, validate_assignment=True)

    id: Optional[UUID] = Field(default_factory=uuid4)
    name: Annotated[str, Field(min_length=1, max_length=300, description="Program name")]
    code: Optional[str] = Field(None, description="Official program code")
    deadline: Optional[datetime] = None
    budget_min: Optional[float] = Field(None, ge=0, description="Minimum budget in BGN")
    budget_max: Optional[float] = Field(None, ge=0, description="Maximum budget in BGN")
    eligible_entities: List[str] = Field(default_factory=list)
    status: str = Field(default="active")
    description: str = Field(default="", description="Program description")
    priority_areas: List[str] = Field(default_factory=list)
    contact_info: Dict[str, str] = Field(default_factory=dict)

    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

class SearchQuery(BaseModel):
    """Search query with advanced filtering options"""
    model_config = ConfigDict(str_strip_whitespace=True)

    query: Annotated[str, Field(min_length=1, max_length=1000, description="Search query")]
    limit: Annotated[int, Field(ge=1, le=100, default=10, description="Number of results")]
    offset: Annotated[int, Field(ge=0, default=0, description="Results offset for pagination")]

    # Filtering options
    document_types: List[DocumentType] = Field(default_factory=list)
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    source_domains: List[str] = Field(default_factory=list)
    language: Optional[str] = Field(None, description="Filter by language")

    # Search configuration
    semantic_weight: Annotated[float, Field(ge=0.0, le=1.0, default=0.7)] = 0.7
    include_highlights: bool = Field(default=True)
    min_score: Annotated[float, Field(ge=0.0, le=1.0, default=0.0)] = 0.0

class SearchResult(BaseModel):
    """Search result with relevance scoring"""
    model_config = ConfigDict(frozen=True)

    document: FundingDocument
    relevance_score: Annotated[float, Field(ge=0.0, le=1.0, description="Combined relevance score")]
    semantic_score: Annotated[float, Field(ge=0.0, le=1.0, description="Semantic similarity score")]
    keyword_score: Annotated[float, Field(ge=0.0, le=1.0, description="Keyword matching score")]
    highlights: List[str] = Field(default_factory=list, description="Text highlights")
    explanation: Optional[str] = Field(None, description="Score explanation")

class CrawlJob(BaseModel):
    """Crawling job configuration"""
    model_config = ConfigDict(str_strip_whitespace=True)

    id: UUID = Field(default_factory=uuid4)
    urls: List[str] = Field(min_length=1, description="URLs to crawl")
    job_type: str = Field(default="scheduled", description="Job type")
    priority: Annotated[int, Field(ge=1, le=10, default=5)] = 5
    max_depth: Annotated[int, Field(ge=1, le=5, default=2)] = 2
    respect_robots_txt: bool = Field(default=True)

    # Status tracking
    status: ProcessingStatus = Field(default=ProcessingStatus.PENDING)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None

    # Results
    documents_found: int = Field(default=0, ge=0)
    documents_processed: int = Field(default=0, ge=0)
    documents_failed: int = Field(default=0, ge=0)

    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
```

### Critical Technical Implementation Details

#### Crawl4AI v0.6.0 Configuration
```python
# crawler/crawl4ai_spider.py - Production configuration
from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode
from crawl4ai.content_filter_strategy import PruningContentFilter, BM25ContentFilter
from crawl4ai.markdown_generation_strategy import DefaultMarkdownGenerator

async def create_production_crawler():
    """Production-ready Crawl4AI configuration"""
    browser_config = BrowserConfig(
        headless=True,
        browser_type="chromium",
        user_agent="EU-Funds-MCP-Bot/1.0 (+https://your-domain.com/bot)",
        viewport_width=1920,
        viewport_height=1080,
        # Browser pooling for performance
        use_persistent_context=True,
        user_data_dir="/tmp/crawl4ai_profiles",
        # Security and compliance
        accept_downloads=False,
        ignore_https_errors=False,
        # Performance optimization
        page_timeout=30000,
        request_timeout=10000,
    )

    run_config = CrawlerRunConfig(
        # World-aware crawling for Bulgarian sites
        locale="bg-BG",
        timezone_id="Europe/Sofia",
        # Content filtering with BM25
        markdown_generator=DefaultMarkdownGenerator(
            content_filter=BM25ContentFilter(
                user_query="европейски фондове програми финансиране",
                bm25_threshold=1.0
            )
        ),
        # Table extraction for structured data
        table_score_threshold=8,
        # Network monitoring
        capture_network=True,
        capture_console=True,
        # Caching strategy
        cache_mode=CacheMode.ENABLED,
        # Performance
        wait_for_images=False,
        process_iframes=True,
    )

    return AsyncWebCrawler(config=browser_config), run_config
```

#### Supabase pgvector Setup
```sql
-- database/migrations/001_initial_setup.sql
-- Enable pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Documents table with vector support
CREATE TABLE funding_documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    url TEXT NOT NULL UNIQUE,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    content_hash TEXT NOT NULL,
    document_type TEXT NOT NULL,
    language TEXT DEFAULT 'bg',
    metadata JSONB DEFAULT '{}',
    source_domain TEXT,
    crawl_timestamp TIMESTAMPTZ DEFAULT NOW(),
    processing_status TEXT DEFAULT 'pending',

    -- Vector embedding (1024 dimensions for multilingual-e5-large)
    embedding vector(1024),
    embedding_model TEXT,

    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- HNSW index for fast vector similarity search
CREATE INDEX ON funding_documents
USING hnsw (embedding vector_cosine_ops)
WITH (m = 16, ef_construction = 64);

-- Traditional indexes for hybrid search
CREATE INDEX idx_documents_content_fts ON funding_documents
USING gin(to_tsvector('bulgarian', content));
CREATE INDEX idx_documents_type ON funding_documents(document_type);
CREATE INDEX idx_documents_domain ON funding_documents(source_domain);
CREATE INDEX idx_documents_created ON funding_documents(created_at);

-- RLS policies for security
ALTER TABLE funding_documents ENABLE ROW LEVEL SECURITY;
```

#### Hybrid Search Implementation
```python
# rag/hybrid_retriever.py - Advanced hybrid search
import asyncio
from typing import List, Tuple
import numpy as np
from supabase import create_client
from sentence_transformers import SentenceTransformer

class HybridRetriever:
    def __init__(self, supabase_client, embedding_model):
        self.supabase = supabase_client
        self.embedding_model = embedding_model

    async def hybrid_search(
        self,
        query: str,
        limit: int = 10,
        semantic_weight: float = 0.7
    ) -> List[SearchResult]:
        """
        Advanced hybrid search with Reciprocal Rank Fusion (RRF)
        """
        # Generate query embedding
        query_embedding = self.embedding_model.encode(query).tolist()

        # Parallel execution of semantic and keyword search
        semantic_task = self._semantic_search(query_embedding, limit * 2)
        keyword_task = self._keyword_search(query, limit * 2)

        semantic_results, keyword_results = await asyncio.gather(
            semantic_task, keyword_task
        )

        # Apply Reciprocal Rank Fusion
        fused_results = self._reciprocal_rank_fusion(
            semantic_results, keyword_results, semantic_weight
        )

        return fused_results[:limit]

    async def _semantic_search(self, embedding: List[float], limit: int):
        """Vector similarity search using pgvector"""
        response = await self.supabase.rpc(
            'semantic_search',
            {
                'query_embedding': embedding,
                'match_threshold': 0.3,
                'match_count': limit
            }
        ).execute()
        return response.data

    async def _keyword_search(self, query: str, limit: int):
        """Full-text search using PostgreSQL"""
        response = await self.supabase.rpc(
            'keyword_search',
            {
                'search_query': query,
                'match_count': limit
            }
        ).execute()
        return response.data

    def _reciprocal_rank_fusion(
        self,
        semantic_results: List,
        keyword_results: List,
        semantic_weight: float,
        k: int = 60
    ) -> List:
        """
        Combine results using Reciprocal Rank Fusion
        RRF score = semantic_weight * (1/(k + rank_semantic)) +
                   (1 - semantic_weight) * (1/(k + rank_keyword))
        """
        # Implementation details...
        pass
```

### Production-Ready Implementation Tasks (2025)
```yaml
Task 1: Foundation & Infrastructure Setup
CREATE src/core/config.py:
  - Pydantic v2 settings management
  - Environment-based configuration
  - Secrets management integration
  - Validation и type safety
CREATE src/core/logging.py:
  - Structured JSON logging
  - Correlation IDs за request tracing
  - Log levels и filtering
  - Integration с monitoring systems
CREATE docker/Dockerfile.multi-stage:
  - Multi-stage production build
  - Security scanning integration
  - Optimized layer caching
  - Non-root user setup

Task 2: Advanced Database Layer
CREATE src/database/supabase_client.py:
  - Async Supabase client setup
  - Connection pooling с PgBouncer
  - Retry logic и circuit breaker
  - Health checks и monitoring
CREATE src/database/vector_operations.py:
  - pgvector 0.7.0+ HNSW operations
  - Batch vector insertions
  - Similarity search optimization
  - Index management utilities
CREATE src/database/migrations/:
  - Supabase migration scripts
  - Vector extension setup
  - RLS policies configuration
  - Performance indexes

Task 3: Crawl4AI v0.6.0 Integration
CREATE src/crawler/crawl4ai_spider.py:
  - Browser pooling configuration
  - World-aware crawling setup
  - Network traffic capture
  - Table-to-DataFrame extraction
CREATE src/crawler/content_processor.py:
  - GPT-4o-mini intelligent filtering
  - BM25 content scoring
  - Markdown generation optimization
  - Change detection algorithms
CREATE src/crawler/scheduler.py:
  - APScheduler integration
  - Distributed job processing
  - Error recovery mechanisms
  - Performance monitoring

Task 4: State-of-the-Art RAG System
CREATE src/rag/embeddings.py:
  - multilingual-e5-large-instruct setup
  - Batch processing optimization
  - Redis caching layer
  - Model versioning support
CREATE src/rag/hybrid_retriever.py:
  - Reciprocal Rank Fusion (RRF)
  - Adaptive semantic/keyword weighting
  - Query expansion за български
  - Result explanation generation
CREATE src/rag/query_processor.py:
  - Bulgarian language preprocessing
  - Query intent classification
  - Synonym expansion
  - Context-aware rewriting

Task 5: Production MCP Server
CREATE src/mcp_server/server.py:
  - FastAPI async server
  - MCP Protocol v1.0 compliance
  - JWT authentication
  - Rate limiting и CORS
CREATE src/mcp_server/tools.py:
  - Advanced search tools
  - Document management tools
  - Analytics и reporting tools
  - Admin operations tools
CREATE src/mcp_server/middleware.py:
  - Request/response logging
  - Error handling middleware
  - Security headers
  - Performance monitoring

Task 6: Monitoring & Observability
CREATE src/core/monitoring.py:
  - Prometheus metrics collection
  - Custom business metrics
  - Performance tracking
  - Health check endpoints
CREATE monitoring/prometheus.yml:
  - Prometheus configuration
  - Scraping targets setup
  - Alerting rules definition
CREATE monitoring/grafana/:
  - Pre-built dashboards
  - System metrics visualization
  - Business KPIs tracking

Task 7: Production Testing Suite
CREATE tests/unit/:
  - Pytest-based unit tests
  - Mock external dependencies
  - Coverage reporting
  - Property-based testing
CREATE tests/integration/:
  - End-to-end RAG pipeline tests
  - Database integration tests
  - MCP protocol compliance tests
CREATE tests/performance/:
  - Locust load testing
  - Memory usage profiling
  - Response time benchmarks

Task 8: Security & Compliance
CREATE src/utils/security.py:
  - JWT token management
  - Rate limiting implementation
  - Input validation utilities
  - Security headers middleware
CREATE .github/workflows/security.yml:
  - Dependency vulnerability scanning
  - SAST code analysis
  - Container security scanning

Task 9: Production Deployment
CREATE k8s/:
  - Kubernetes deployment manifests
  - Service definitions
  - ConfigMaps и Secrets
  - Ingress configuration
CREATE scripts/:
  - Automated deployment scripts
  - Database backup utilities
  - Health check scripts
  - Performance monitoring setup

Task 10: Documentation & Maintenance
CREATE docs/:
  - Comprehensive API documentation
  - Architecture decision records
  - Deployment guides
  - Troubleshooting guides
CREATE .github/workflows/:
  - CI/CD pipeline setup
  - Automated testing
  - Security scanning
  - Documentation generation
```

### Per Task Pseudocode
```python
# Task 2: Intelligent Web Crawler
class EUFundsSpider(scrapy.Spider):
    name = 'eufunds'
    allowed_domains = ['eufunds.bg']

    async def parse(self, response):
        # PATTERN: Extract document links
        doc_links = response.css('a[href*="/program/"]::attr(href)').getall()

        for link in doc_links:
            # GOTCHA: Check content hash before processing
            content_hash = self.get_content_hash(response.url)
            if not self.is_content_changed(content_hash):
                continue

            # CRITICAL: Use GPT-4o-mini for intelligent extraction
            structured_content = await self.extract_with_llm(response.text)
            yield FundingDocument(**structured_content)

async def extract_with_llm(html_content: str) -> dict:
    """Extract structured data using GPT-4o-mini"""
    prompt = """
    Извлечи структурирана информация от този HTML за ЕС фонд:
    - Име на програмата
    - Краен срок за кандидатстване
    - Бюджет (мин/макс)
    - Допустими кандидати
    - Описание

    Върни JSON формат на български език.
    """
    # PATTERN: Use OpenAI client with rate limiting
    response = await openai_client.chat.completions.create(
        model="gpt-4o-mini",
        messages=[{"role": "user", "content": f"{prompt}\n\n{html_content}"}],
        temperature=0.1
    )
    return json.loads(response.choices[0].message.content)

# Task 3: Hybrid RAG Retrieval
async def hybrid_search(
    query: str,
    limit: int = 10,
    semantic_weight: float = 0.7
) -> List[SearchResult]:
    """Combine semantic and keyword search"""

    # PATTERN: Generate query embedding
    query_embedding = embedding_model.encode(query)

    # Semantic search with pgvector
    semantic_sql = """
    SELECT id, content, title,
           1 - (embedding <=> %s::vector) as semantic_score
    FROM documents
    ORDER BY embedding <=> %s::vector
    LIMIT %s
    """

    # Keyword search with PostgreSQL FTS
    keyword_sql = """
    SELECT id, content, title,
           ts_rank(ts_vector, plainto_tsquery('bulgarian', %s)) as keyword_score
    FROM documents
    WHERE ts_vector @@ plainto_tsquery('bulgarian', %s)
    ORDER BY keyword_score DESC
    LIMIT %s
    """

    # CRITICAL: Combine results using RRF (Reciprocal Rank Fusion)
    return combine_results_rrf(semantic_results, keyword_results, semantic_weight)

# Task 5: MCP Server Tools
@mcp_server.tool
async def search_funding_programs(
    query: str,
    limit: int = 10,
    document_types: List[str] = None
) -> List[Dict[str, Any]]:
    """Search for EU funding programs"""

    # PATTERN: Validate input with Pydantic
    search_request = SearchQuery(
        query=query,
        limit=limit,
        document_types=document_types or []
    )

    # CRITICAL: Use hybrid search
    results = await hybrid_search(
        query=search_request.query,
        limit=search_request.limit
    )

    return [result.dict() for result in results]

@mcp_server.tool
async def get_program_details(program_id: int) -> Dict[str, Any]:
    """Get detailed information about a specific program"""

    # PATTERN: Database query with error handling
    async with database.get_session() as session:
        program = await session.get(FundingProgram, program_id)
        if not program:
            raise ValueError(f"Program with ID {program_id} not found")

        return program.dict()
```

### Integration Points
```yaml
ENVIRONMENT:
  - add to: .env
  - vars: |
      # Database
      SUPABASE_URL=postgresql://...
      SUPABASE_ANON_KEY=...

      # LLM для crawler
      OPENAI_API_KEY=sk-...

      # Embedding model (local)
      EMBEDDING_MODEL_PATH=./models/multilingual-e5-large

      # Crawler settings
      CRAWL_FREQUENCY_HOURS=24
      MAX_CONCURRENT_REQUESTS=5

CONFIG:
  - pgvector: CREATE EXTENSION IF NOT EXISTS vector;
  - Bulgarian FTS: CREATE TEXT SEARCH CONFIGURATION bulgarian_config ...
  - Indexes: CREATE INDEX ON documents USING ivfflat (embedding vector_cosine_ops);

DEPENDENCIES:
  - Update requirements.txt with:
    - mcp
    - scrapy
    - playwright
    - llama-index
    - sentence-transformers
    - psycopg2-binary
    - sqlalchemy
    - alembic
    - unstructured
    - easyocr
```

## Validation Loop

### Level 1: Syntax & Style
```bash
# Run these FIRST - fix any errors before proceeding
ruff check . --fix          # Auto-fix style issues
mypy .                      # Type checking
pytest tests/unit/ -v       # Unit tests
# Expected: No errors. If errors, READ and fix.
```

### Level 2: Component Tests
```python
# test_crawler.py
async def test_spider_extracts_programs():
    """Test spider correctly extracts program information"""
    spider = EUFundsSpider()
    response = fake_response_from_file('eufunds_program_page.html')

    results = list(spider.parse(response))
    assert len(results) > 0
    assert all(isinstance(r, FundingDocument) for r in results)

# test_rag.py
async def test_hybrid_search_bulgarian():
    """Test hybrid search works with Bulgarian queries"""
    results = await hybrid_search("програми за иновации", limit=5)

    assert len(results) <= 5
    assert all(r.score > 0 for r in results)
    assert any("иновации" in r.document.content.lower() for r in results)

# test_mcp_server.py
async def test_mcp_search_tool():
    """Test MCP search tool returns valid results"""
    result = await search_funding_programs(
        query="дигитални технологии",
        limit=3
    )

    assert len(result) <= 3
    assert all("title" in item for item in result)
```

### Level 3: Integration Test
```bash
# Test full pipeline
python -m crawler.spider    # Should crawl and store documents
python -m rag.pipeline      # Should create embeddings
python -m mcp_server.server  # Should start MCP server

# Test MCP client interaction
echo '{"method": "tools/call", "params": {"name": "search_funding_programs", "arguments": {"query": "иновации"}}}' | nc localhost 8000

# Expected: JSON response with funding programs
```

## Context Validation & Completeness Check

### ✅ Complete Context Coverage
Този документ съдържа всичко необходимо за професионална разработка на MCP Server for EU Funds:

1. **📋 Project Vision & Requirements**: Ясно дефинирани цели и изисквания
2. **🏗️ Enterprise Architecture**: Production-ready техническа архитектура с 2025 best practices
3. **📁 Project Structure**: Comprehensive file organization с modern Python practices
4. **⚙️ Technology Stack**: State-of-the-art технологии (Crawl4AI v0.6.0, pgvector 0.7.0+, multilingual-e5-large-instruct)
5. **📊 Data Models**: Production-ready Pydantic v2 models с validation
6. **🔧 Implementation Details**: Конкретни code examples и configuration
7. **📈 Development Phases**: Structured 12-week implementation plan
8. **✅ Task Breakdown**: Detailed tasks с specific deliverables
9. **🧪 Testing Strategy**: Comprehensive testing approach
10. **🚀 Deployment Plan**: Production deployment с monitoring

### 🎯 Ready for Development
Този документ служи като:
- **Complete Development Guide**: Всички технически детайли за implementation
- **Context Source**: Пълен контекст за AI assistants и developers
- **Quality Assurance**: Best practices и validation criteria
- **Project Roadmap**: Clear phases и milestones

### 🔄 Continuous Context Updates
Документът ще се обновява с:
- Implementation progress tracking
- Technical decisions и changes
- Performance metrics и optimizations
- User feedback integration

**Статус**: ✅ ГОТОВ ЗА РАЗРАБОТКА - Документът съдържа пълния контекст необходим за професионално изграждане на enterprise-grade MCP сървър за европейски фондове.
```

## Final Validation Checklist
- [ ] All tests pass: `pytest tests/ -v`
- [ ] No linting errors: `ruff check .`
- [ ] No type errors: `mypy .`
- [ ] Crawler successfully extracts from eufunds.bg
- [ ] RAG system returns relevant results for Bulgarian queries
- [ ] MCP server responds to tool calls
- [ ] Database properly stores and retrieves documents
- [ ] Hybrid search combines semantic and keyword results
- [ ] Multimodal processing handles tables and images
- [ ] Error cases handled gracefully
- [ ] README includes clear setup instructions
- [ ] .env.example has all required variables

---

## Anti-Patterns to Avoid
- ❌ Don't hardcode API keys - use environment variables
- ❌ Don't ignore robots.txt for web crawling
- ❌ Don't skip content hash checking (will re-process same content)
- ❌ Don't use sync functions in async MCP context
- ❌ Don't forget UTF-8 encoding for Bulgarian text
- ❌ Don't skip pgvector index creation (slow queries)
- ❌ Don't commit large embedding models to git

## Confidence Score: 8/10
High confidence due to:
- Clear open-source technology stack
- Well-documented APIs and libraries
- Established patterns for RAG systems
- MCP protocol specification available

Minor uncertainty on:
- Bulgarian language optimization specifics
- eufunds.bg crawling policies and rate limits
- GPT-4o-mini cost optimization strategies
