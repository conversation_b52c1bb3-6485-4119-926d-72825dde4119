#!/usr/bin/env python3
"""
Проверка на данните след почистването
"""
import httpx
import json
import sys
import os

# Добавяне на src директорията към Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from core.config import Settings

# Зареждане на конфигурацията
settings = Settings()

def check_data():
    headers = {
        'apikey': settings.supabase_service_key,
        'Authorization': f'Bearer {settings.supabase_service_key}',
        'Content-Type': 'application/json'
    }

    # Взимане на първите 3 записа
    response = httpx.get(
        f'{settings.supabase_url}/rest/v1/eu_funds_content?select=id,title,content&limit=3',
        headers=headers
    )

    if response.status_code == 200:
        records = response.json()
        for i, record in enumerate(records, 1):
            print(f'=== ЗАПИС {i} ===')
            print(f'ID: {record["id"]}')
            print(f'Title: {record["title"]}')
            print(f'Content length: {len(record["content"])} символа')
            print(f'Content preview: {record["content"][:300]}...')
            print()
    else:
        print(f'Грешка: {response.status_code} - {response.text}')

if __name__ == "__main__":
    check_data()
