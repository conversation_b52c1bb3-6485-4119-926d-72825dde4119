# Examples - Working Code Patterns for EU Funds MCP Server

This folder contains working code patterns and templates that demonstrate the correct implementation approach for the EU Funds MCP Server. These examples are based on proven patterns from <PERSON>'s repositories and 2025 best practices.

## File Structure

### Core Implementation Patterns
- `mcp_server_example.py` - Complete MCP server structure with tool definitions
- `config_example.py` - Configuration management with Pydantic Settings
- `database_example.py` - Supabase integration with pgvector operations
- `embedding_example.py` - Bulgarian-optimized embedding processing
- `search_example.py` - Hybrid search implementation with reranking

### Testing Patterns
- `test_mcp_tools.py` - MCP tool testing with async support
- `test_bulgarian_content.py` - Bulgarian language processing validation
- `test_performance.py` - Performance benchmarking and optimization

### Configuration Examples
- `pyproject_example.toml` - Modern Python packaging configuration
- `env_example.env` - Environment variables with production settings
- `docker_example.dockerfile` - Container deployment configuration

## Key Patterns Demonstrated

### 1. MCP Tool Structure
All tools follow the standard pattern:
```python
@mcp.tool()
async def tool_name(parameter: Type) -> ReturnType:
    """Tool description with Bulgarian examples."""
    # Implementation with proper error handling
```

### 2. Async Operations
Modern async/await patterns throughout:
- Database operations with connection pooling
- Concurrent web scraping with rate limiting
- Parallel embedding processing

### 3. Error Handling
Comprehensive error handling with structured logging:
- Try/catch blocks with specific exception types
- Structured JSON logging with correlation IDs
- Graceful degradation for external service failures

### 4. Bulgarian Language Support
Specialized handling for Bulgarian content:
- Cyrillic character normalization
- Bulgarian-specific tokenization
- Morphological analysis and stopword removal

### 5. Performance Optimization
Enterprise-grade performance patterns:
- Connection pooling for database operations
- Intelligent caching for frequent queries
- Batch processing for embedding operations

## Usage Guidelines

1. **Copy Patterns**: Use these examples as templates for implementation
2. **Adapt for Bulgarian**: Modify text processing for Bulgarian language specifics
3. **Maintain Structure**: Keep the same error handling and logging patterns
4. **Test Thoroughly**: Use the testing patterns for validation
5. **Follow Standards**: Maintain the same code quality and documentation standards

## Context Engineering Integration

These examples follow Context Engineering principles:
- **Self-Documenting**: Each example includes comprehensive documentation
- **Working Code**: All examples are tested and functional
- **Professional Standards**: Enterprise-grade code quality
- **Bulgarian Optimization**: Specific adaptations for Bulgarian language processing
- **Validation Ready**: Includes success criteria and testing patterns

## Production Readiness

All examples are designed for production deployment:
- Secure API key management
- Comprehensive error handling
- Performance optimization
- Monitoring and logging
- Scalability considerations

Use these patterns as the foundation for building the EU Funds MCP Server with confidence in their reliability and performance.
