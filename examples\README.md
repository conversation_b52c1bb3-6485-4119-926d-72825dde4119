# Examples - EU Funds MCP Server Code Patterns

## 🎯 Purpose
These examples demonstrate the EXACT patterns and conventions for building the EU Funds MCP Server. AI assistants should follow these patterns EXACTLY when implementing features.

## 📁 Key Examples

### Core Patterns
- **`mcp_server_example.py`** - MCP server structure with FastAPI and health checks
- **`rag_example.py`** - Bulgarian-optimized RAG implementation with hybrid search
- **`crawler_example.py`** - Crawl4AI patterns for EU funds sites
- **`config_example.py`** - Pydantic Settings configuration patterns
- **`database_example.py`** - Supabase client and pgvector operations

### Testing Patterns  
- **`tests/`** - Testing patterns for all components
- **`test_mcp_example.py`** - MCP server testing
- **`test_rag_example.py`** - RAG system testing
- **`test_crawler_example.py`** - Crawler testing

## 🔧 Usage Instructions

### For AI Assistants
1. **Read the example** relevant to your current task
2. **Follow the pattern EXACTLY** - no modifications
3. **Use the same imports, structure, and conventions**
4. **Adapt only the specific business logic**

### For Developers
1. **Study the patterns** before implementing new features
2. **Copy the structure** and modify for your use case
3. **Run the examples** to understand the expected behavior
4. **Use as reference** for troubleshooting

## 🚨 Critical Rules

### Code Patterns
- **Always use async/await** for MCP compatibility
- **Always use Pydantic v2** for data validation
- **Always use structured logging** with correlation IDs
- **Always handle errors** with proper exception types

### Bulgarian Language
- **Always use UTF-8 encoding** for Bulgarian text
- **Always use Bulgarian FTS** for keyword search
- **Always optimize** for Bulgarian EU funds terminology
- **Always test** with real Bulgarian content

### Performance
- **Always use connection pooling** for database
- **Always use caching** for expensive operations
- **Always use batch processing** for embeddings
- **Always monitor** performance metrics

## 📊 Example Categories

### 1. Configuration Examples
- Environment variable handling
- Pydantic Settings patterns
- Secret management
- Multi-environment setup

### 2. Database Examples
- Supabase client setup
- pgvector operations
- Migration patterns
- Connection management

### 3. RAG Examples
- Embedding generation
- Hybrid search implementation
- Query processing
- Result ranking

### 4. Crawler Examples
- Crawl4AI configuration
- Content extraction
- Change detection
- Error handling

### 5. MCP Examples
- Tool definitions
- Server setup
- Client communication
- Protocol compliance

### 6. Testing Examples
- Unit test patterns
- Integration test setup
- Mock configurations
- Performance testing

## 🎯 Success Criteria

When using these examples, you should achieve:
- ✅ **Zero configuration errors** - all settings load correctly
- ✅ **Proper async patterns** - no blocking operations
- ✅ **Bulgarian language support** - correct text processing
- ✅ **Production readiness** - error handling and monitoring
- ✅ **MCP compliance** - proper protocol implementation

## 🔄 Continuous Updates

These examples are updated with:
- New patterns discovered during development
- Performance optimizations
- Bug fixes and improvements
- Best practices evolution

**Always use the latest version** of these examples for new implementations.
