"""
Database setup script for EU Funds MCP Server
Creates necessary tables and functions in Supabase
"""

import asyncio
import logging
from src.core.config import settings
from supabase import create_client, Client

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# SQL for creating tables and functions
CREATE_TABLES_SQL = """
-- Enable pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- EU Funds Content table
CREATE TABLE IF NOT EXISTS eu_funds_content (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    source_url TEXT NOT NULL,
    content_type TEXT DEFAULT 'general_info',
    language TEXT DEFAULT 'bg',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- EU Funds Embeddings table
CREATE TABLE IF NOT EXISTS eu_funds_embeddings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    content_id UUID REFERENCES eu_funds_content(id) ON DELETE CASCADE,
    embedding vector(384),
    model_name TEXT DEFAULT 'paraphrase-multilingual-MiniLM-L12-v2',
    chunk_index INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Search logs table
CREATE TABLE IF NOT EXISTS eu_funds_search_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    query TEXT NOT NULL,
    results_count INTEGER DEFAULT 0,
    search_time_ms INTEGER DEFAULT 0,
    search_type TEXT DEFAULT 'hybrid',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_eu_funds_content_source ON eu_funds_content(source_url);
CREATE INDEX IF NOT EXISTS idx_eu_funds_content_type ON eu_funds_content(content_type);
CREATE INDEX IF NOT EXISTS idx_eu_funds_content_language ON eu_funds_content(language);
CREATE INDEX IF NOT EXISTS idx_eu_funds_content_created ON eu_funds_content(created_at);

-- Vector similarity search index (HNSW)
CREATE INDEX IF NOT EXISTS idx_eu_funds_embeddings_vector 
ON eu_funds_embeddings USING hnsw (embedding vector_cosine_ops);

CREATE INDEX IF NOT EXISTS idx_eu_funds_embeddings_content ON eu_funds_embeddings(content_id);

-- Full-text search index
CREATE INDEX IF NOT EXISTS idx_eu_funds_content_fts 
ON eu_funds_content USING gin(to_tsvector('bulgarian', title || ' ' || content));
"""

CREATE_FUNCTIONS_SQL = """
-- Vector similarity search function
CREATE OR REPLACE FUNCTION match_eu_funds_content(
    query_embedding vector(384),
    match_threshold float DEFAULT 0.5,
    match_count int DEFAULT 10
)
RETURNS TABLE (
    id uuid,
    title text,
    content text,
    source_url text,
    content_type text,
    language text,
    metadata jsonb,
    similarity float
)
LANGUAGE sql STABLE
AS $$
    SELECT 
        c.id,
        c.title,
        c.content,
        c.source_url,
        c.content_type,
        c.language,
        c.metadata,
        1 - (e.embedding <=> query_embedding) as similarity
    FROM eu_funds_content c
    JOIN eu_funds_embeddings e ON c.id = e.content_id
    WHERE 1 - (e.embedding <=> query_embedding) > match_threshold
    ORDER BY e.embedding <=> query_embedding
    LIMIT match_count;
$$;

-- Full-text search function
CREATE OR REPLACE FUNCTION search_eu_funds_text(
    search_query text,
    match_count int DEFAULT 10
)
RETURNS TABLE (
    id uuid,
    title text,
    content text,
    source_url text,
    content_type text,
    language text,
    metadata jsonb,
    rank float
)
LANGUAGE sql STABLE
AS $$
    SELECT 
        id,
        title,
        content,
        source_url,
        content_type,
        language,
        metadata,
        ts_rank(to_tsvector('bulgarian', title || ' ' || content), plainto_tsquery('bulgarian', search_query)) as rank
    FROM eu_funds_content
    WHERE to_tsvector('bulgarian', title || ' ' || content) @@ plainto_tsquery('bulgarian', search_query)
    ORDER BY rank DESC
    LIMIT match_count;
$$;

-- Combined hybrid search function
CREATE OR REPLACE FUNCTION hybrid_search_eu_funds(
    search_query text,
    query_embedding vector(384),
    text_weight float DEFAULT 0.7,
    vector_weight float DEFAULT 0.3,
    match_threshold float DEFAULT 0.5,
    match_count int DEFAULT 10
)
RETURNS TABLE (
    id uuid,
    title text,
    content text,
    source_url text,
    content_type text,
    language text,
    metadata jsonb,
    combined_score float
)
LANGUAGE sql STABLE
AS $$
    WITH text_search AS (
        SELECT 
            id,
            title,
            content,
            source_url,
            content_type,
            language,
            metadata,
            ts_rank(to_tsvector('bulgarian', title || ' ' || content), plainto_tsquery('bulgarian', search_query)) as text_score
        FROM eu_funds_content
        WHERE to_tsvector('bulgarian', title || ' ' || content) @@ plainto_tsquery('bulgarian', search_query)
    ),
    vector_search AS (
        SELECT 
            c.id,
            c.title,
            c.content,
            c.source_url,
            c.content_type,
            c.language,
            c.metadata,
            1 - (e.embedding <=> query_embedding) as vector_score
        FROM eu_funds_content c
        JOIN eu_funds_embeddings e ON c.id = e.content_id
        WHERE 1 - (e.embedding <=> query_embedding) > match_threshold
    )
    SELECT 
        COALESCE(t.id, v.id) as id,
        COALESCE(t.title, v.title) as title,
        COALESCE(t.content, v.content) as content,
        COALESCE(t.source_url, v.source_url) as source_url,
        COALESCE(t.content_type, v.content_type) as content_type,
        COALESCE(t.language, v.language) as language,
        COALESCE(t.metadata, v.metadata) as metadata,
        (COALESCE(t.text_score, 0) * text_weight + COALESCE(v.vector_score, 0) * vector_weight) as combined_score
    FROM text_search t
    FULL OUTER JOIN vector_search v ON t.id = v.id
    ORDER BY combined_score DESC
    LIMIT match_count;
$$;
"""

async def setup_database():
    """Setup database tables and functions."""
    try:
        logger.info("🗄️ Setting up EU Funds database...")
        
        # Create Supabase client
        supabase: Client = create_client(settings.supabase_url, settings.supabase_service_key)
        
        logger.info("📋 Creating tables...")
        
        # Execute table creation SQL
        result = supabase.rpc('exec_sql', {'sql': CREATE_TABLES_SQL}).execute()
        if result.data:
            logger.info("✅ Tables created successfully")
        else:
            logger.warning("⚠️ Table creation may have failed - continuing...")
        
        logger.info("🔧 Creating functions...")
        
        # Execute function creation SQL
        result = supabase.rpc('exec_sql', {'sql': CREATE_FUNCTIONS_SQL}).execute()
        if result.data:
            logger.info("✅ Functions created successfully")
        else:
            logger.warning("⚠️ Function creation may have failed - continuing...")
        
        # Test basic connectivity
        logger.info("🧪 Testing database connectivity...")
        
        # Try to insert a test record
        test_data = {
            'title': 'Test EU Funding Program',
            'content': 'This is a test entry for EU funding information. Тест за българско съдържание.',
            'source_url': 'https://test.example.com',
            'content_type': 'test',
            'language': 'bg',
            'metadata': {'test': True}
        }
        
        result = supabase.table('eu_funds_content').insert(test_data).execute()
        if result.data:
            test_id = result.data[0]['id']
            logger.info(f"✅ Test record created: {test_id}")
            
            # Clean up test record
            supabase.table('eu_funds_content').delete().eq('id', test_id).execute()
            logger.info("🧹 Test record cleaned up")
        
        logger.info("✅ Database setup completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Database setup failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(setup_database())
    if success:
        print("✅ Database setup completed!")
    else:
        print("❌ Database setup failed!")
        exit(1)
