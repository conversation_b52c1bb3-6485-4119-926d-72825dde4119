-- =============================================================================
-- EU FUNDS MCP SERVER - INITIAL DATABASE SETUP
-- Migration: 001_initial_setup.sql
-- Description: Create initial schema with pgvector support for EU funds data
-- =============================================================================

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS vector;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Drop existing tables if they exist (fresh start)
DROP TABLE IF EXISTS funding_documents CASCADE;
DROP TABLE IF EXISTS crawl_sessions CASCADE;
DROP TABLE IF EXISTS search_queries CASCADE;

-- =============================================================================
-- MAIN DOCUMENTS TABLE
-- =============================================================================

-- Documents table with vector support
CREATE TABLE funding_documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    url TEXT NOT NULL UNIQUE,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    content_hash TEXT NOT NULL,
    
    -- Metadata
    source_domain TEXT,
    document_type TEXT, -- 'program', 'call', 'news', 'guide'
    language TEXT DEFAULT 'bg',
    processing_status TEXT DEFAULT 'pending',
    
    -- Vector embedding (1024 dimensions for multilingual-e5-large)
    embedding vector(1024),
    embedding_model TEXT,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    last_crawled_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================================================
-- CRAWL SESSIONS TABLE
-- =============================================================================

-- Track crawling sessions and statistics
CREATE TABLE crawl_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_name TEXT NOT NULL,
    start_time TIMESTAMPTZ DEFAULT NOW(),
    end_time TIMESTAMPTZ,
    status TEXT DEFAULT 'running', -- 'running', 'completed', 'failed'
    
    -- Statistics
    total_urls_discovered INTEGER DEFAULT 0,
    total_urls_processed INTEGER DEFAULT 0,
    total_documents_created INTEGER DEFAULT 0,
    total_documents_updated INTEGER DEFAULT 0,
    total_errors INTEGER DEFAULT 0,
    
    -- Configuration
    domains_crawled TEXT[],
    crawl_config JSONB,
    
    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================================================
-- SEARCH QUERIES TABLE
-- =============================================================================

-- Track search queries for analytics and optimization
CREATE TABLE search_queries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    query_text TEXT NOT NULL,
    query_embedding vector(1024),
    
    -- Search parameters
    search_type TEXT, -- 'semantic', 'keyword', 'hybrid'
    filters JSONB,
    
    -- Results
    results_count INTEGER,
    response_time_ms INTEGER,
    
    -- User context
    user_id TEXT,
    session_id TEXT,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================================================
-- INDEXES FOR PERFORMANCE
-- =============================================================================

-- HNSW index for fast vector similarity search on documents
CREATE INDEX idx_documents_embedding_hnsw ON funding_documents
USING hnsw (embedding vector_cosine_ops)
WITH (m = 16, ef_construction = 64);

-- Traditional indexes for hybrid search
CREATE INDEX idx_documents_content_fts ON funding_documents
USING gin(to_tsvector('bulgarian', content));

CREATE INDEX idx_documents_title_fts ON funding_documents
USING gin(to_tsvector('bulgarian', title));

-- Regular indexes for filtering and sorting
CREATE INDEX idx_documents_source_domain ON funding_documents(source_domain);
CREATE INDEX idx_documents_document_type ON funding_documents(document_type);
CREATE INDEX idx_documents_created_at ON funding_documents(created_at);
CREATE INDEX idx_documents_updated_at ON funding_documents(updated_at);
CREATE INDEX idx_documents_processing_status ON funding_documents(processing_status);

-- Crawl sessions indexes
CREATE INDEX idx_crawl_sessions_status ON crawl_sessions(status);
CREATE INDEX idx_crawl_sessions_start_time ON crawl_sessions(start_time);

-- Search queries indexes
CREATE INDEX idx_search_queries_created_at ON search_queries(created_at);
CREATE INDEX idx_search_queries_search_type ON search_queries(search_type);

-- HNSW index for search query embeddings
CREATE INDEX idx_search_queries_embedding_hnsw ON search_queries
USING hnsw (query_embedding vector_cosine_ops)
WITH (m = 16, ef_construction = 64);

-- =============================================================================
-- FUNCTIONS FOR SEARCH
-- =============================================================================

-- Semantic search function using pgvector
CREATE OR REPLACE FUNCTION semantic_search(
    query_embedding vector(1024),
    match_threshold float DEFAULT 0.3,
    match_count int DEFAULT 10
)
RETURNS TABLE (
    id uuid,
    title text,
    content text,
    url text,
    similarity float
)
LANGUAGE sql
AS $$
    SELECT 
        fd.id,
        fd.title,
        fd.content,
        fd.url,
        1 - (fd.embedding <=> query_embedding) as similarity
    FROM funding_documents fd
    WHERE fd.embedding IS NOT NULL
        AND 1 - (fd.embedding <=> query_embedding) > match_threshold
    ORDER BY fd.embedding <=> query_embedding
    LIMIT match_count;
$$;

-- Hybrid search function combining semantic and keyword search
CREATE OR REPLACE FUNCTION hybrid_search(
    query_text text,
    query_embedding vector(1024),
    semantic_weight float DEFAULT 0.7,
    keyword_weight float DEFAULT 0.3,
    match_count int DEFAULT 10
)
RETURNS TABLE (
    id uuid,
    title text,
    content text,
    url text,
    semantic_score float,
    keyword_score float,
    combined_score float
)
LANGUAGE sql
AS $$
    WITH semantic_results AS (
        SELECT 
            fd.id,
            fd.title,
            fd.content,
            fd.url,
            1 - (fd.embedding <=> query_embedding) as semantic_score
        FROM funding_documents fd
        WHERE fd.embedding IS NOT NULL
    ),
    keyword_results AS (
        SELECT 
            fd.id,
            fd.title,
            fd.content,
            fd.url,
            ts_rank(to_tsvector('bulgarian', fd.content), plainto_tsquery('bulgarian', query_text)) as keyword_score
        FROM funding_documents fd
        WHERE to_tsvector('bulgarian', fd.content) @@ plainto_tsquery('bulgarian', query_text)
    )
    SELECT 
        COALESCE(s.id, k.id) as id,
        COALESCE(s.title, k.title) as title,
        COALESCE(s.content, k.content) as content,
        COALESCE(s.url, k.url) as url,
        COALESCE(s.semantic_score, 0.0) as semantic_score,
        COALESCE(k.keyword_score, 0.0) as keyword_score,
        (COALESCE(s.semantic_score, 0.0) * semantic_weight + 
         COALESCE(k.keyword_score, 0.0) * keyword_weight) as combined_score
    FROM semantic_results s
    FULL OUTER JOIN keyword_results k ON s.id = k.id
    ORDER BY combined_score DESC
    LIMIT match_count;
$$;

-- =============================================================================
-- TRIGGERS FOR AUTOMATIC TIMESTAMPS
-- =============================================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for automatic timestamp updates
CREATE TRIGGER update_funding_documents_updated_at 
    BEFORE UPDATE ON funding_documents 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_crawl_sessions_updated_at 
    BEFORE UPDATE ON crawl_sessions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =============================================================================
-- INITIAL DATA AND CONFIGURATION
-- =============================================================================

-- Create Bulgarian text search configuration if not exists
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_ts_config WHERE cfgname = 'bulgarian') THEN
        CREATE TEXT SEARCH CONFIGURATION bulgarian (COPY = simple);
    END IF;
END
$$;

-- Insert initial crawl session
INSERT INTO crawl_sessions (session_name, status, domains_crawled) 
VALUES ('initial_setup', 'completed', ARRAY['eufunds.bg', 'opic.bg', 'esif.bg']);

-- =============================================================================
-- COMMENTS FOR DOCUMENTATION
-- =============================================================================

COMMENT ON TABLE funding_documents IS 'Main table storing EU funding documents with vector embeddings';
COMMENT ON COLUMN funding_documents.embedding IS 'Vector embedding using multilingual-e5-large-instruct (1024 dimensions)';
COMMENT ON COLUMN funding_documents.content_hash IS 'SHA-256 hash of content for change detection';

COMMENT ON TABLE crawl_sessions IS 'Tracking table for web crawling sessions and statistics';
COMMENT ON TABLE search_queries IS 'Analytics table for search queries and performance monitoring';

COMMENT ON FUNCTION semantic_search IS 'Semantic search using pgvector cosine similarity';
COMMENT ON FUNCTION hybrid_search IS 'Hybrid search combining semantic and keyword search with weighted scoring';
