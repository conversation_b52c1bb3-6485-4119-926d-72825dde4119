#!/usr/bin/env python3
"""
Health check script for Docker containers.
Lightweight script to verify server is responding.
"""

import sys
import asyncio
import httpx
import os


async def check_health():
    """Check if the server is healthy."""
    host = os.getenv("MCP_HOST", "localhost")
    port = os.getenv("MCP_PORT", "8051")
    timeout = 10
    
    url = f"http://{host}:{port}/health/live"
    
    try:
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.get(url)
            
            if response.status_code == 200:
                print("✅ Health check passed")
                return True
            else:
                print(f"❌ Health check failed: HTTP {response.status_code}")
                return False
                
    except httpx.TimeoutException:
        print(f"❌ Health check timeout after {timeout}s")
        return False
    except httpx.ConnectError:
        print("❌ Health check failed: Connection refused")
        return False
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False


def main():
    """Main entry point."""
    try:
        is_healthy = asyncio.run(check_health())
        sys.exit(0 if is_healthy else 1)
    except KeyboardInterrupt:
        print("❌ Health check interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Health check error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
