# Multi-stage Dockerfile optimized for low RAM usage
# EU Funds MCP Server - Production Build

# =============================================================================
# Stage 1: Base Python Image (Lightweight)
# =============================================================================
FROM python:3.12-slim as base

# Set environment variables for optimization
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    DEBIAN_FRONTEND=noninteractive

# Install system dependencies (minimal)
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# =============================================================================
# Stage 2: Dependencies Installation
# =============================================================================
FROM base as dependencies

# Install build dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Create app user (security)
RUN groupadd --gid 1000 appuser && \
    useradd --uid 1000 --gid appuser --shell /bin/bash --create-home appuser

# Set working directory
WORKDIR /app

# Copy dependency files
COPY pyproject.toml ./

# Install Python dependencies (optimized)
RUN pip install --no-cache-dir --upgrade pip setuptools wheel && \
    pip install --no-cache-dir -e . && \
    pip install --no-cache-dir ".[monitoring]" && \
    # Clean up build dependencies to save space
    apt-get purge -y build-essential gcc g++ && \
    apt-get autoremove -y && \
    rm -rf /var/lib/apt/lists/* && \
    # Clean pip cache
    pip cache purge

# =============================================================================
# Stage 3: Browser Dependencies (for Crawl4AI)
# =============================================================================
FROM dependencies as browser-deps

# Install minimal browser dependencies for Crawl4AI
RUN apt-get update && apt-get install -y --no-install-recommends \
    chromium \
    chromium-driver \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Set Chrome/Chromium path for Crawl4AI
ENV CHROME_BIN=/usr/bin/chromium \
    CHROMEDRIVER_PATH=/usr/bin/chromedriver

# =============================================================================
# Stage 4: Production Image
# =============================================================================
FROM browser-deps as production

# Copy application code
COPY src/ ./src/
COPY scripts/ ./scripts/
COPY docs/ ./docs/

# Create necessary directories
RUN mkdir -p logs data temp && \
    chown -R appuser:appuser /app

# Switch to non-root user
USER appuser

# Health check script
COPY --chown=appuser:appuser scripts/health_check.py ./health_check.py
RUN chmod +x ./health_check.py

# Expose ports
EXPOSE 8051 9090

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python health_check.py || exit 1

# Default command
CMD ["python", "-m", "uvicorn", "src.mcp_server.main:app", "--host", "0.0.0.0", "--port", "8051"]

# =============================================================================
# Stage 5: Development Image (Optional)
# =============================================================================
FROM production as development

# Switch back to root for development tools
USER root

# Install development dependencies
RUN pip install --no-cache-dir -e ".[dev,test]"

# Install additional development tools
RUN apt-get update && apt-get install -y --no-install-recommends \
    git \
    vim \
    htop \
    && rm -rf /var/lib/apt/lists/*

# Switch back to app user
USER appuser

# Development command (with auto-reload)
CMD ["python", "-m", "uvicorn", "src.mcp_server.main:app", "--host", "0.0.0.0", "--port", "8051", "--reload"]

# =============================================================================
# Build Arguments and Labels
# =============================================================================
ARG BUILD_DATE
ARG VERSION=0.1.0
ARG VCS_REF

LABEL maintainer="EU Funds MCP Team <<EMAIL>>" \
      org.label-schema.build-date=$BUILD_DATE \
      org.label-schema.name="eu-funds-mcp-server" \
      org.label-schema.description="Enterprise-Grade MCP Server for EU Funding Programs" \
      org.label-schema.url="https://github.com/eu-funds/mcp-server" \
      org.label-schema.vcs-ref=$VCS_REF \
      org.label-schema.version=$VERSION \
      org.label-schema.schema-version="1.0"

# =============================================================================
# Memory Optimization Notes:
# =============================================================================
# 1. Multi-stage build reduces final image size
# 2. Minimal base image (python:3.12-slim)
# 3. Cleaned package caches and build dependencies
# 4. Single RUN commands to reduce layers
# 5. Non-root user for security
# 6. Optimized for containers with limited RAM (512MB-1GB)
# =============================================================================
