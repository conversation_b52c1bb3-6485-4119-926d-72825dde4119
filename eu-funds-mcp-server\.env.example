# =============================================================================
# EU FUNDS MCP SERVER - ENVIRONMENT CONFIGURATION
# =============================================================================

# -----------------------------------------------------------------------------
# DATABASE CONFIGURATION (Supabase)
# -----------------------------------------------------------------------------
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_KEY=your-service-key-here
SUPABASE_ANON_KEY=your-anon-key-here

# -----------------------------------------------------------------------------
# AI SERVICES
# -----------------------------------------------------------------------------
# OpenAI for content processing
OPENAI_API_KEY=sk-your-openai-key-here

# Cohere for reranking
COHERE_API_KEY=your-cohere-key-here

# -----------------------------------------------------------------------------
# EMBEDDING CONFIGURATION
# -----------------------------------------------------------------------------
EMBEDDING_MODEL_NAME=intfloat/multilingual-e5-large-instruct
EMBEDDING_DIMENSION=1024
EMBEDDING_BATCH_SIZE=32

# -----------------------------------------------------------------------------
# SEARCH CONFIGURATION
# -----------------------------------------------------------------------------
# Hybrid search weights (must sum to 1.0)
RERANK_WEIGHT_CE=0.3
RERANK_WEIGHT_HYBRID=0.7

# Search limits
MAX_SEARCH_RESULTS=100
DEFAULT_SEARCH_LIMIT=10

# -----------------------------------------------------------------------------
# CRAWLER CONFIGURATION
# -----------------------------------------------------------------------------
# Target domains (comma-separated)
CRAWL_DOMAINS=eufunds.bg,opic.bg,esif.bg

# Crawling frequency
CRAWL_FREQUENCY_HOURS=24
CRAWL_MAX_DEPTH=3
CRAWL_MAX_PAGES=1000

# Browser configuration
BROWSER_POOL_SIZE=3
MAX_CONCURRENT_REQUESTS=5
REQUEST_TIMEOUT_SECONDS=30

# -----------------------------------------------------------------------------
# MCP SERVER CONFIGURATION
# -----------------------------------------------------------------------------
MCP_HOST=0.0.0.0
MCP_PORT=8051
MCP_TRANSPORT=stdio
MCP_CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# -----------------------------------------------------------------------------
# REDIS CONFIGURATION
# -----------------------------------------------------------------------------
REDIS_URL=redis://localhost:6379/0
REDIS_TTL_SECONDS=3600

# -----------------------------------------------------------------------------
# MONITORING & LOGGING
# -----------------------------------------------------------------------------
LOG_LEVEL=INFO
LOG_FORMAT=json
ENABLE_METRICS=true
METRICS_PORT=9090

# Environment
ENVIRONMENT=development
DEBUG=true

# -----------------------------------------------------------------------------
# SECURITY
# -----------------------------------------------------------------------------
JWT_SECRET_KEY=your-super-secret-jwt-key-here
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# Rate limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW_MINUTES=1

# -----------------------------------------------------------------------------
# FEATURE FLAGS
# -----------------------------------------------------------------------------
ENABLE_MULTIMODAL=true
ENABLE_OCR=true
ENABLE_TABLE_EXTRACTION=true
ENABLE_BULGARIAN_FTS=true
ENABLE_REAL_TIME_UPDATES=false
