# =============================================================================
# EU FUNDS MCP SERVER - ENVIRONMENT CONFIGURATION
# =============================================================================

# -----------------------------------------------------------------------------
# MCP Server Configuration
# -----------------------------------------------------------------------------
TRANSPORT=sse
HOST=0.0.0.0
PORT=8051

# -----------------------------------------------------------------------------
# Database Configuration (Supabase)
# -----------------------------------------------------------------------------
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_KEY=your_service_role_key_here
SUPABASE_ANON_KEY=your_anon_key_here

# -----------------------------------------------------------------------------
# AI/LLM Configuration
# -----------------------------------------------------------------------------
# OpenAI API for content processing
OPENAI_API_KEY=sk-your_openai_api_key_here
MODEL_CHOICE=gpt-4o-mini

# Cohere API for reranking
COHERE_API_KEY=your_cohere_api_key_here

# Cross-encoder model for reranking
CROSS_ENCODER_MODEL_API=cross-encoder/ms-marco-MiniLM-L-6-v2

# Reranking weights
RERANK_WEIGHT_CE=0.3
RERANK_WEIGHT_HYBRID=0.7

# -----------------------------------------------------------------------------
# Embedding Configuration
# -----------------------------------------------------------------------------
EMBEDDING_MODEL_NAME=intfloat/multilingual-e5-large-instruct
EMBEDDING_MODEL_PATH=./models/multilingual-e5-large
EMBEDDING_DIMENSION=1024

# -----------------------------------------------------------------------------
# Crawler Configuration
# -----------------------------------------------------------------------------
CRAWL_FREQUENCY_HOURS=24
MAX_CONCURRENT_REQUESTS=5
CRAWL_DELAY_SECONDS=1
USER_AGENT=EU-Funds-MCP-Bot/1.0 (+https://your-domain.com/bot)

# Target websites
CRAWL_DOMAINS=eufunds.bg,opic.bg,esif.bg

# -----------------------------------------------------------------------------
# Redis Configuration (Optional - for caching)
# -----------------------------------------------------------------------------
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# -----------------------------------------------------------------------------
# Security Configuration
# -----------------------------------------------------------------------------
SECRET_KEY=your_secret_key_for_jwt_signing
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS settings
CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# -----------------------------------------------------------------------------
# Monitoring & Logging
# -----------------------------------------------------------------------------
LOG_LEVEL=INFO
LOG_FORMAT=json
ENABLE_METRICS=true
METRICS_PORT=9090

# Health check settings
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10

# -----------------------------------------------------------------------------
# Performance Configuration
# -----------------------------------------------------------------------------
# Rate limiting
RATE_LIMIT_PER_MINUTE=100
RATE_LIMIT_BURST=20

# Database connection pool
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30

# Browser settings for Crawl4AI
BROWSER_POOL_SIZE=3
BROWSER_TIMEOUT=30
HEADLESS_BROWSER=true

# -----------------------------------------------------------------------------
# Development Settings
# -----------------------------------------------------------------------------
DEBUG=false
ENVIRONMENT=production

# Testing
TEST_DATABASE_URL=postgresql://test_user:test_pass@localhost:5432/test_db

# -----------------------------------------------------------------------------
# Feature Flags
# -----------------------------------------------------------------------------
ENABLE_MULTIMODAL=true
ENABLE_OCR=true
ENABLE_TABLE_EXTRACTION=true
ENABLE_CHANGE_DETECTION=true

# -----------------------------------------------------------------------------
# Bulgarian Language Specific
# -----------------------------------------------------------------------------
LANGUAGE=bg
TIMEZONE=Europe/Sofia
CURRENCY=BGN

# Text search configuration
ENABLE_BULGARIAN_FTS=true
BULGARIAN_STEMMER=true

# -----------------------------------------------------------------------------
# File Storage
# -----------------------------------------------------------------------------
UPLOAD_DIR=./uploads
MAX_FILE_SIZE_MB=50
ALLOWED_FILE_TYPES=pdf,doc,docx,txt,html

# -----------------------------------------------------------------------------
# Backup & Recovery
# -----------------------------------------------------------------------------
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
