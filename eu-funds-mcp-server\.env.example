# EU Funds MCP Server Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# ENVIRONMENT SETTINGS
# =============================================================================
ENVIRONMENT=development
DEBUG=true

# =============================================================================
# DATABASE SETTINGS (Supabase)
# =============================================================================
DB_SUPABASE_URL=https://your-project.supabase.co
DB_SUPABASE_SERVICE_KEY=your-service-role-key-here
DB_SUPABASE_ANON_KEY=your-anon-key-here

# Database Connection Pool (optimized for low RAM)
DB_MAX_CONNECTIONS=5
DB_MIN_CONNECTIONS=1
DB_CONNECTION_TIMEOUT=30

# =============================================================================
# RAG SYSTEM SETTINGS
# =============================================================================
RAG_OPENAI_API_KEY=sk-your-openai-api-key-here
RAG_OPENAI_MODEL=gpt-4o-mini
RAG_EMBEDDING_MODEL=sentence-transformers/multilingual-e5-large-instruct
RAG_EMBEDDING_DIMENSION=1024

# Search Configuration
RAG_MAX_SEARCH_RESULTS=10
RAG_SIMILARITY_THRESHOLD=0.7
RAG_USE_HYBRID_SEARCH=true
RAG_BM25_WEIGHT=0.3
RAG_SEMANTIC_WEIGHT=0.7
RAG_CACHE_TTL=3600

# =============================================================================
# WEB CRAWLER SETTINGS
# =============================================================================
# Memory optimized settings
CRAWLER_MAX_CONCURRENT_CRAWLS=2
CRAWLER_CRAWL_TIMEOUT=60
CRAWLER_MAX_PAGES_PER_SITE=100

# Browser Settings (lightweight)
CRAWLER_HEADLESS=true
CRAWLER_BROWSER_POOL_SIZE=1
CRAWLER_PAGE_LOAD_TIMEOUT=30

# Content Processing
CRAWLER_CHUNK_SIZE=1000
CRAWLER_CHUNK_OVERLAP=200

# Target Sites (comma-separated)
CRAWLER_TARGET_SITES=https://eufunds.bg,https://opic.bg,https://esif.bg,https://strategy.bg

# =============================================================================
# MCP SERVER SETTINGS
# =============================================================================
MCP_HOST=0.0.0.0
MCP_PORT=8051
MCP_TRANSPORT=sse

# Security
MCP_CORS_ORIGINS=http://localhost:3000,http://localhost:8080
MCP_RATE_LIMIT=100

# Health Checks
MCP_HEALTH_CHECK_INTERVAL=30

# =============================================================================
# REDIS CACHE SETTINGS
# =============================================================================
REDIS_URL=redis://localhost:6379
REDIS_DB=0
REDIS_PASSWORD=
REDIS_MAX_CONNECTIONS=5

# =============================================================================
# MONITORING & LOGGING
# =============================================================================
MONITORING_LOG_LEVEL=INFO
MONITORING_LOG_FORMAT=json
MONITORING_LOG_FILE=logs/eu-funds-mcp.log
MONITORING_ENABLE_METRICS=true
MONITORING_METRICS_PORT=9090
MONITORING_ENABLE_HEALTH_CHECKS=true

# =============================================================================
# OPTIONAL: DIRECT POSTGRESQL CONNECTION (fallback)
# =============================================================================
# DB_POSTGRES_HOST=localhost
# DB_POSTGRES_PORT=5432
# DB_POSTGRES_DB=eu_funds
# DB_POSTGRES_USER=postgres
# DB_POSTGRES_PASSWORD=your-postgres-password

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
# Uncomment for development
# DEBUG=true
# MONITORING_LOG_LEVEL=DEBUG
# CRAWLER_MAX_CONCURRENT_CRAWLS=1
# DB_MAX_CONNECTIONS=2

# =============================================================================
# PRODUCTION SETTINGS
# =============================================================================
# Uncomment for production
# ENVIRONMENT=production
# DEBUG=false
# MONITORING_LOG_LEVEL=INFO
# MONITORING_LOG_FORMAT=json
# CRAWLER_MAX_CONCURRENT_CRAWLS=5
# DB_MAX_CONNECTIONS=10
