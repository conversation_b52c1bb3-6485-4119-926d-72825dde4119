"""
Integration tests for MCP Tools
Tests real MCP protocol compliance and tool functionality with actual data
"""

import pytest
import asyncio
import json
from unittest.mock import patch
from src.mcp.tools import EUFundsMCPTools

@pytest.mark.integration
@pytest.mark.slow
class TestMCPToolsIntegration:
    """Integration test suite for MCP tools with real data."""
    
    @pytest.mark.asyncio
    async def test_mcp_tools_initialization(self, mcp_tools):
        """Test MCP tools initialization with real components."""
        assert mcp_tools is not None
        assert hasattr(mcp_tools, 'vector_store')
        assert hasattr(mcp_tools, 'hybrid_search')
        assert hasattr(mcp_tools, 'reranker')
        
        # Verify all components are initialized
        assert mcp_tools.vector_store is not None
        assert mcp_tools.hybrid_search is not None
        assert mcp_tools.reranker is not None
    
    @pytest.mark.asyncio
    async def test_search_eu_funds_real_data(self, mcp_tools):
        """Test search_eu_funds tool with real database data."""
        # Test Bulgarian query
        result = await mcp_tools.search_eu_funds(
            query="европейски фондове България иновации",
            max_results=5
        )
        
        assert isinstance(result, dict)
        assert "results" in result
        assert "total_found" in result
        assert "search_time" in result
        assert "query_info" in result
        
        # Should find some results if data exists
        results = result["results"]
        assert isinstance(results, list)
        
        if len(results) > 0:
            # Verify result structure
            first_result = results[0]
            assert "content" in first_result
            assert "title" in first_result
            assert "source_url" in first_result
            assert "relevance_score" in first_result
            assert "language" in first_result
            
            # Verify relevance scores are sorted
            scores = [r.get("relevance_score", 0) for r in results]
            assert scores == sorted(scores, reverse=True)
    
    @pytest.mark.asyncio
    async def test_search_eu_funds_english_query(self, mcp_tools):
        """Test search_eu_funds tool with English query."""
        result = await mcp_tools.search_eu_funds(
            query="European funding programs SME innovation",
            max_results=3
        )
        
        assert isinstance(result, dict)
        assert "results" in result
        
        # Should handle English queries properly
        results = result["results"]
        assert isinstance(results, list)
    
    @pytest.mark.asyncio
    async def test_get_funding_programs_integration(self, mcp_tools):
        """Test get_funding_programs tool with real data."""
        result = await mcp_tools.get_funding_programs(
            sector="технологии",
            target_group="МСП",
            deadline_months=12
        )
        
        assert isinstance(result, dict)
        assert "programs" in result
        assert "total_found" in result
        assert "search_criteria" in result
        
        programs = result["programs"]
        assert isinstance(programs, list)
        
        if len(programs) > 0:
            # Verify program structure
            first_program = programs[0]
            assert "program_name" in first_program
            assert "description" in first_program
            assert "eligibility" in first_program
            assert "deadline_info" in first_program
            assert "relevance_score" in first_program
    
    @pytest.mark.asyncio
    async def test_analyze_eligibility_integration(self, mcp_tools):
        """Test analyze_eligibility tool with real data."""
        result = await mcp_tools.analyze_eligibility(
            organization_type="МСП",
            sector="технологии",
            project_description="Иновативен софтуерен проект за дигитализация на бизнес процеси"
        )
        
        assert isinstance(result, dict)
        assert "eligible_programs" in result
        assert "eligibility_analysis" in result
        assert "recommendations" in result
        assert "match_score" in result
        
        eligible_programs = result["eligible_programs"]
        assert isinstance(eligible_programs, list)
        
        # Verify analysis structure
        analysis = result["eligibility_analysis"]
        assert isinstance(analysis, dict)
        assert "organization_match" in analysis
        assert "sector_match" in analysis
        assert "project_match" in analysis
    
    @pytest.mark.asyncio
    async def test_get_application_deadlines_integration(self, mcp_tools):
        """Test get_application_deadlines tool with real data."""
        result = await mcp_tools.get_application_deadlines(
            months_ahead=6,
            sector_filter="всички",
            urgent_only=False
        )
        
        assert isinstance(result, dict)
        assert "deadlines" in result
        assert "total_found" in result
        assert "time_range" in result
        
        deadlines = result["deadlines"]
        assert isinstance(deadlines, list)
        
        if len(deadlines) > 0:
            # Verify deadline structure
            first_deadline = deadlines[0]
            assert "program_name" in first_deadline
            assert "deadline_date" in first_deadline
            assert "days_remaining" in first_deadline
            assert "urgency_level" in first_deadline
            assert "application_info" in first_deadline
    
    @pytest.mark.asyncio
    async def test_tool_performance_metrics(self, mcp_tools):
        """Test tool performance tracking and metrics."""
        # Execute multiple tool calls
        await mcp_tools.search_eu_funds("тест запитване", max_results=3)
        await mcp_tools.get_funding_programs("технологии", deadline_months=6)
        await mcp_tools.analyze_eligibility("МСП", "иновации", "тест проект")
        
        # Get performance statistics
        stats = mcp_tools.get_tool_stats()
        
        assert isinstance(stats, dict)
        assert "tool_statistics" in stats
        assert "total_calls" in stats
        assert "average_response_time" in stats
        
        tool_stats = stats["tool_statistics"]
        assert isinstance(tool_stats, dict)
        
        # Verify each tool has statistics
        expected_tools = ["search_eu_funds", "get_funding_programs", "analyze_eligibility"]
        for tool_name in expected_tools:
            if tool_name in tool_stats:
                tool_stat = tool_stats[tool_name]
                assert "calls" in tool_stat
                assert "avg_time" in tool_stat
                assert "success_rate" in tool_stat
    
    @pytest.mark.asyncio
    async def test_error_handling_integration(self, mcp_tools):
        """Test error handling in real integration scenarios."""
        # Test with empty query
        result = await mcp_tools.search_eu_funds("", max_results=5)
        assert isinstance(result, dict)
        assert "error" in result or "results" in result
        
        # Test with invalid parameters
        result = await mcp_tools.get_funding_programs(
            sector="",
            deadline_months=-1
        )
        assert isinstance(result, dict)
        
        # Test with very long input
        long_description = "Много дълго описание на проект " * 100
        result = await mcp_tools.analyze_eligibility(
            "МСП", 
            "технологии", 
            long_description
        )
        assert isinstance(result, dict)
    
    @pytest.mark.asyncio
    async def test_bulgarian_language_processing(self, mcp_tools):
        """Test Bulgarian language processing in integration."""
        bulgarian_queries = [
            "програми за финансиране на малки предприятия",
            "европейски фондове за иновации и развитие",
            "ОПИК процедури за технологични проекти",
            "безвъзмездна помощ за МСП в България"
        ]
        
        for query in bulgarian_queries:
            result = await mcp_tools.search_eu_funds(query, max_results=3)
            
            assert isinstance(result, dict)
            assert "results" in result
            
            # Verify Bulgarian content is properly handled
            results = result["results"]
            if len(results) > 0:
                # Should find relevant Bulgarian content
                bulgarian_results = [
                    r for r in results 
                    if r.get("language") == "bg"
                ]
                # At least some results should be in Bulgarian if available
                assert len(bulgarian_results) >= 0
    
    @pytest.mark.asyncio
    async def test_cross_tool_consistency(self, mcp_tools):
        """Test consistency between different tools."""
        # Search for programs about innovations
        search_result = await mcp_tools.search_eu_funds(
            "иновации технологии МСП",
            max_results=5
        )
        
        # Get funding programs for the same topic
        programs_result = await mcp_tools.get_funding_programs(
            sector="технологии",
            target_group="МСП",
            deadline_months=12
        )
        
        # Analyze eligibility for similar project
        eligibility_result = await mcp_tools.analyze_eligibility(
            organization_type="МСП",
            sector="технологии",
            project_description="Иновативен технологичен проект"
        )
        
        # All tools should return valid results
        assert isinstance(search_result, dict)
        assert isinstance(programs_result, dict)
        assert isinstance(eligibility_result, dict)
        
        # Results should be related (if data exists)
        search_results = search_result.get("results", [])
        programs = programs_result.get("programs", [])
        eligible_programs = eligibility_result.get("eligible_programs", [])
        
        # At least one tool should find relevant content
        total_results = len(search_results) + len(programs) + len(eligible_programs)
        assert total_results >= 0  # Should not fail completely
    
    @pytest.mark.asyncio
    async def test_response_time_requirements(self, mcp_tools):
        """Test that tools meet sub-second response time requirements."""
        import time
        
        test_cases = [
            ("search_eu_funds", {"query": "европейски фондове", "max_results": 5}),
            ("get_funding_programs", {"sector": "технологии", "deadline_months": 6}),
            ("analyze_eligibility", {"organization_type": "МСП", "sector": "иновации", "project_description": "тест проект"}),
            ("get_application_deadlines", {"months_ahead": 3})
        ]
        
        for tool_name, kwargs in test_cases:
            start_time = time.time()
            
            tool_method = getattr(mcp_tools, tool_name)
            result = await tool_method(**kwargs)
            
            end_time = time.time()
            response_time = end_time - start_time
            
            # Should meet sub-second requirement (allowing some margin for real operations)
            assert response_time < 5.0, f"{tool_name} took {response_time:.2f}s (too slow)"
            assert isinstance(result, dict)
    
    @pytest.mark.asyncio
    async def test_concurrent_tool_usage(self, mcp_tools):
        """Test concurrent usage of multiple tools."""
        async def run_search():
            return await mcp_tools.search_eu_funds("тест", max_results=3)
        
        async def run_programs():
            return await mcp_tools.get_funding_programs("всички", deadline_months=6)
        
        async def run_eligibility():
            return await mcp_tools.analyze_eligibility("МСП", "всички", "тест проект")
        
        # Run tools concurrently
        results = await asyncio.gather(
            run_search(),
            run_programs(), 
            run_eligibility(),
            return_exceptions=True
        )
        
        # All should complete successfully
        assert len(results) == 3
        assert all(isinstance(result, dict) for result in results)
        assert not any(isinstance(result, Exception) for result in results)
    
    @pytest.mark.asyncio
    async def test_data_quality_validation(self, mcp_tools):
        """Test data quality in tool responses."""
        result = await mcp_tools.search_eu_funds(
            "европейски фондове България",
            max_results=10
        )
        
        results = result.get("results", [])
        
        for search_result in results:
            # Verify required fields
            assert "content" in search_result
            assert "title" in search_result
            assert "source_url" in search_result
            
            # Verify data quality
            content = search_result["content"]
            assert isinstance(content, str)
            assert len(content.strip()) > 0
            
            # Verify URL format
            source_url = search_result["source_url"]
            assert isinstance(source_url, str)
            assert source_url.startswith("http")
            
            # Verify relevance score
            if "relevance_score" in search_result:
                score = search_result["relevance_score"]
                assert isinstance(score, (int, float))
                assert 0 <= score <= 1
