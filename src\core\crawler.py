"""
Web Scraping Infrastructure for EU Funds MCP Server

This module provides Crawl4AI integration with browser pooling,
EU funding sources mapping, and Bulgarian language optimization.
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional, Set
from datetime import datetime, timedelta
from urllib.parse import urljoin, urlparse
import re

from crawl4ai import Async<PERSON>eb<PERSON>rawler, CrawlerRunConfig
from crawl4ai.extraction_strategy import LLMExtractionStrategy
from crawl4ai.chunking_strategy import RegexChunking
from crawl4ai.cache_context import CacheMode

from src.core.config import settings
from src.core.models import CrawlResult, DataSource, ContentMetadata

logger = logging.getLogger(__name__)

class EUFundsCrawler:
    """
    Advanced web crawler for EU funding information with Bulgarian optimization.
    
    Features:
    - Browser pooling for performance
    - Respectful crawling with rate limiting
    - Bulgarian content detection and processing
    - Content quality validation
    - Automatic retry mechanisms
    """
    
    def __init__(self):
        """Initialize crawler with optimized configuration."""
        self.crawler: Optional[AsyncWebCrawler] = None
        self.initialized = False
        
        # EU funding sources configuration
        self.eu_sources = {
            "european_commission": {
                "base_url": "https://ec.europa.eu",
                "funding_paths": [
                    "/info/funding-tenders/opportunities/portal/screen/programmes",
                    "/info/research-and-innovation/funding/funding-opportunities",
                    "/regional_policy/funding/available-budget_en"
                ],
                "language": "en",
                "priority": 1,
                "crawl_frequency": "daily"
            },
            "eu_funds_bulgaria": {
                "base_url": "https://www.eufunds.bg",
                "funding_paths": [
                    "/bg/operational-programmes",
                    "/bg/calls-for-proposals",
                    "/bg/news-and-events"
                ],
                "language": "bg",
                "priority": 1,
                "crawl_frequency": "daily"
            },
            "horizon_europe": {
                "base_url": "https://ec.europa.eu/info/research-and-innovation/funding/funding-opportunities/funding-programmes-and-open-calls/horizon-europe_en",
                "funding_paths": [
                    "/work-programme",
                    "/calls-for-proposals",
                    "/participant-portal"
                ],
                "language": "en",
                "priority": 2,
                "crawl_frequency": "weekly"
            },
            "cohesion_policy": {
                "base_url": "https://cohesiondata.ec.europa.eu",
                "funding_paths": [
                    "/countries/BG",
                    "/funds/ERDF",
                    "/funds/ESF"
                ],
                "language": "en",
                "priority": 2,
                "crawl_frequency": "weekly"
            }
        }
        
        # Bulgarian content patterns
        self.bulgarian_patterns = {
            "cyrillic": re.compile(r'[а-яё]', re.IGNORECASE),
            "funding_keywords": [
                "финансиране", "фондове", "програма", "проект", "кандидатстване",
                "безвъзмездна помощ", "европейски фондове", "оперативна програма",
                "покана", "конкурс", "срок", "критерии", "допустимост"
            ],
            "deadline_patterns": [
                r'срок.*?(\d{1,2}[\.\/]\d{1,2}[\.\/]\d{4})',
                r'до.*?(\d{1,2}[\.\/]\d{1,2}[\.\/]\d{4})',
                r'крайна дата.*?(\d{1,2}[\.\/]\d{1,2}[\.\/]\d{4})'
            ]
        }
    
    async def initialize(self) -> None:
        """Initialize crawler with browser pooling."""
        try:
            logger.info("🕷️ Initializing Crawl4AI crawler...")
            
            # Configure crawler with optimized settings
            self.crawler = AsyncWebCrawler(
                # Browser configuration
                headless=True,
                browser_type="chromium",
                viewport_width=1920,
                viewport_height=1080,
                
                # Performance optimization
                max_concurrent_sessions=5,
                semaphore_count=3,
                
                # Caching configuration
                always_by_pass_cache=False,
                base_directory="./cache",
                
                # Request configuration
                delay_before_return_html=2.0,
                timeout=30000,
                
                # User agent for respectful crawling
                user_agent="EU-Funds-MCP-Server/1.0 (+https://github.com/eu-funds-mcp)"
            )
            
            await self.crawler.start()
            self.initialized = True
            logger.info("✅ Crawl4AI crawler initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize crawler: {e}")
            raise
    
    async def crawl_eu_source(
        self,
        source_name: str,
        max_pages: int = 50,
        include_subpages: bool = True
    ) -> List[CrawlResult]:
        """
        Crawl specific EU funding source.
        
        Args:
            source_name: Name of the source from eu_sources
            max_pages: Maximum pages to crawl
            include_subpages: Whether to crawl linked subpages
            
        Returns:
            List of crawl results with extracted content
        """
        if not self.initialized:
            await self.initialize()
        
        if source_name not in self.eu_sources:
            raise ValueError(f"Unknown source: {source_name}")
        
        source_config = self.eu_sources[source_name]
        results = []
        
        try:
            logger.info(f"🔍 Crawling {source_name}...")
            
            # Crawl main pages
            for path in source_config["funding_paths"]:
                url = urljoin(source_config["base_url"], path)
                
                # Configure extraction strategy for EU funding content
                extraction_strategy = LLMExtractionStrategy(
                    provider="openai",
                    api_token=settings.openai_api_key,
                    schema={
                        "type": "object",
                        "properties": {
                            "title": {"type": "string"},
                            "content": {"type": "string"},
                            "funding_programs": {
                                "type": "array",
                                "items": {"type": "string"}
                            },
                            "deadlines": {
                                "type": "array", 
                                "items": {"type": "string"}
                            },
                            "eligibility_criteria": {
                                "type": "array",
                                "items": {"type": "string"}
                            },
                            "budget_info": {"type": "string"},
                            "contact_info": {"type": "string"},
                            "language": {"type": "string"}
                        }
                    },
                    instruction="""Extract EU funding information from this page. 
                    Focus on funding programs, application deadlines, eligibility criteria, 
                    budget information, and contact details. If content is in Bulgarian, 
                    preserve the original Bulgarian text."""
                )
                
                # Configure chunking for better processing
                chunking_strategy = RegexChunking(
                    patterns=[
                        r'\n\n',  # Paragraph breaks
                        r'\n(?=[A-Z])',  # New sections
                        r'(?<=\.)\s+(?=[A-Z])'  # Sentence breaks
                    ]
                )
                
                # Crawl configuration
                config = CrawlerRunConfig(
                    cache_mode=CacheMode.ENABLED,
                    extraction_strategy=extraction_strategy,
                    chunking_strategy=chunking_strategy,
                    css_selector="main, article, .content, .funding-info",
                    exclude_external_links=True,
                    exclude_social_media_links=True,
                    word_count_threshold=50,
                    only_text=False
                )
                
                # Execute crawl
                result = await self.crawler.arun(url=url, config=config)
                
                if result.success:
                    # Process and validate content
                    crawl_result = await self._process_crawl_result(
                        result, source_name, source_config["language"]
                    )
                    if crawl_result:
                        results.append(crawl_result)
                        logger.info(f"✅ Successfully crawled: {url}")
                else:
                    logger.warning(f"⚠️ Failed to crawl: {url} - {result.error_message}")
                
                # Respectful crawling delay
                await asyncio.sleep(2)
            
            logger.info(f"🎉 Completed crawling {source_name}: {len(results)} pages")
            return results
            
        except Exception as e:
            logger.error(f"❌ Error crawling {source_name}: {e}")
            raise
    
    async def _process_crawl_result(
        self,
        result: Any,
        source_name: str,
        expected_language: str
    ) -> Optional[CrawlResult]:
        """Process and validate crawl result."""
        try:
            # Extract content and metadata
            content = result.cleaned_html or result.markdown or ""
            if not content or len(content.strip()) < 100:
                logger.warning("⚠️ Content too short, skipping")
                return None
            
            # Detect language
            detected_language = self._detect_language(content)
            
            # Extract structured data if available
            extracted_data = {}
            if result.extracted_content:
                try:
                    extracted_data = result.extracted_content
                except:
                    logger.warning("⚠️ Failed to parse extracted content")
            
            # Create metadata
            metadata = ContentMetadata(
                source=source_name,
                url=result.url,
                title=extracted_data.get("title", ""),
                language=detected_language,
                crawl_timestamp=datetime.now().isoformat(),
                content_type="funding_information",
                word_count=len(content.split()),
                has_deadlines=bool(extracted_data.get("deadlines")),
                has_eligibility=bool(extracted_data.get("eligibility_criteria")),
                quality_score=self._calculate_quality_score(content, extracted_data)
            )
            
            # Extract links and images as strings
            links = []
            if result.links and "internal" in result.links:
                for link in result.links["internal"]:
                    if isinstance(link, dict) and "href" in link:
                        links.append(link["href"])
                    elif isinstance(link, str):
                        links.append(link)

            images = []
            if result.media and "images" in result.media:
                for image in result.media["images"]:
                    if isinstance(image, dict) and "src" in image:
                        images.append(image["src"])
                    elif isinstance(image, str):
                        images.append(image)

            # Create crawl result
            crawl_result = CrawlResult(
                url=result.url,
                title=extracted_data.get("title", ""),
                content=content,
                extracted_data=extracted_data,
                metadata=metadata,
                links=links,
                images=images,
                success=True,
                error_message=None
            )
            
            return crawl_result
            
        except Exception as e:
            logger.error(f"❌ Error processing crawl result: {e}")
            return None
    
    def _detect_language(self, content: str) -> str:
        """Detect content language (Bulgarian vs English)."""
        # Count Cyrillic characters
        cyrillic_matches = len(self.bulgarian_patterns["cyrillic"].findall(content))
        total_chars = len(re.findall(r'[a-zA-Zа-яё]', content))
        
        if total_chars == 0:
            return "unknown"
        
        cyrillic_ratio = cyrillic_matches / total_chars
        
        # If more than 30% Cyrillic, consider it Bulgarian
        return "bg" if cyrillic_ratio > 0.3 else "en"
    
    def _calculate_quality_score(self, content: str, extracted_data: Dict) -> float:
        """Calculate content quality score (0.0 to 1.0)."""
        score = 0.0
        
        # Base score for content length
        if len(content) > 500:
            score += 0.3
        elif len(content) > 200:
            score += 0.2
        
        # Bonus for structured data
        if extracted_data.get("funding_programs"):
            score += 0.2
        if extracted_data.get("deadlines"):
            score += 0.2
        if extracted_data.get("eligibility_criteria"):
            score += 0.2
        
        # Bonus for Bulgarian funding keywords
        bulgarian_keywords_found = sum(
            1 for keyword in self.bulgarian_patterns["funding_keywords"]
            if keyword.lower() in content.lower()
        )
        score += min(0.1, bulgarian_keywords_found * 0.02)
        
        return min(1.0, score)
    
    async def crawl_url(self, url: str) -> Optional[CrawlResult]:
        """
        Crawl a single URL for testing purposes.

        Args:
            url: URL to crawl

        Returns:
            CrawlResult with content and metadata
        """
        if not self.initialized:
            await self.initialize()

        try:
            logger.info(f"🌐 Crawling URL: {url}")

            # Configure crawling strategy - simplified for testing
            config = CrawlerRunConfig(
                cache_mode=CacheMode.BYPASS,
                word_count_threshold=50
            )

            # Perform crawl
            result = await self.crawler.arun(url=url, config=config)

            if result.success:
                return await self._process_crawl_result(result, "test_url", "bg")
            else:
                logger.error(f"❌ Failed to crawl {url}: {result.error_message}")
                return None

        except Exception as e:
            logger.error(f"❌ Error crawling URL {url}: {e}")
            return None

    async def get_data_sources(self) -> List[DataSource]:
        """Get all configured data sources with status."""
        sources = []
        
        for name, config in self.eu_sources.items():
            source = DataSource(
                name=name,
                url=config["base_url"],
                reliability_score=0.95 if config["priority"] == 1 else 0.85,
                last_updated=datetime.now().isoformat(),
                content_type="funding_information",
                language=config["language"]
            )
            sources.append(source)
        
        return sources
    
    async def cleanup(self) -> None:
        """Cleanup crawler resources."""
        try:
            if self.crawler and self.initialized:
                await self.crawler.close()
                logger.info("✅ Crawler cleanup complete")
        except Exception as e:
            logger.error(f"❌ Crawler cleanup failed: {e}")

# Global crawler instance
crawler = EUFundsCrawler()
