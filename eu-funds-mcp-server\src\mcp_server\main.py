"""
Main entry point for EU Funds MCP Server.

This module provides the FastAPI application with health checks and basic
MCP server functionality for testing the foundation setup.
"""

import asyncio
from contextlib import asynccontextmanager

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

from src.core.config import get_settings
from src.core.logging import structured_logger, set_correlation_id
from src.core.monitoring import health_checker, create_health_endpoints, record_request, record_request_duration
import time


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    structured_logger.log_performance_metric("server_startup", 1, "event")
    
    # Initialize health checker
    await health_checker.initialize()
    
    yield
    
    # Shutdown
    structured_logger.log_performance_metric("server_shutdown", 1, "event")


def create_app() -> FastAPI:
    """Create and configure FastAPI application."""
    settings = get_settings()
    
    app = FastAPI(
        title="EU Funds MCP Server",
        description="Enterprise-Grade MCP Server for EU Funding Programs",
        version="0.1.0",
        docs_url="/docs",
        redoc_url="/redoc",
        lifespan=lifespan
    )
    
    # CORS middleware
    cors_origins = settings.mcp.cors_origins.split(',')
    app.add_middleware(
        CORSMiddleware,
        allow_origins=cors_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Request logging middleware
    @app.middleware("http")
    async def logging_middleware(request: Request, call_next):
        # Set correlation ID
        correlation_id = set_correlation_id()
        
        # Record request start
        start_time = time.time()
        method = request.method
        path = request.url.path
        
        structured_logger.log_request(method, path, correlation_id=correlation_id)
        record_request(method, path)
        
        # Process request
        response = await call_next(request)
        
        # Record response
        duration_ms = (time.time() - start_time) * 1000
        structured_logger.log_response(response.status_code, duration_ms)
        record_request_duration(duration_ms / 1000)  # Convert to seconds for Prometheus
        
        return response
    
    # Add health endpoints
    create_health_endpoints(app)
    
    # Root endpoint
    @app.get("/")
    async def root():
        """Root endpoint with server information."""
        return {
            "name": "EU Funds MCP Server",
            "version": "0.1.0",
            "status": "running",
            "description": "Enterprise-Grade MCP Server for EU Funding Programs",
            "endpoints": {
                "health": "/health",
                "docs": "/docs",
                "metrics": "/metrics"
            }
        }
    
    # MCP protocol endpoint (placeholder)
    @app.post("/mcp")
    async def mcp_endpoint(request: Request):
        """MCP protocol endpoint (placeholder for future implementation)."""
        return {
            "jsonrpc": "2.0",
            "result": {
                "message": "MCP Server is running",
                "capabilities": ["search", "crawl", "embed"],
                "status": "ready"
            }
        }
    
    # Test database connection endpoint
    @app.get("/test/database")
    async def test_database():
        """Test database connection and basic operations."""
        try:
            health_status = await health_checker.check_database_health()
            return health_status
        except Exception as e:
            structured_logger.log_error(e, "database_test_endpoint")
            return JSONResponse(
                content={"error": str(e)},
                status_code=500
            )
    
    # Configuration info endpoint
    @app.get("/info/config")
    async def config_info():
        """Get non-sensitive configuration information."""
        settings = get_settings()
        return {
            "environment": settings.environment,
            "debug": settings.debug,
            "mcp": {
                "host": settings.mcp.host,
                "port": settings.mcp.port,
                "transport": settings.mcp.transport
            },
            "crawler": {
                "domains": settings.crawler.crawl_domains.split(','),
                "frequency_hours": settings.crawler.crawl_frequency_hours,
                "max_concurrent": settings.crawler.max_concurrent_requests
            },
            "features": {
                "multimodal": settings.features.enable_multimodal,
                "ocr": settings.features.enable_ocr,
                "table_extraction": settings.features.enable_table_extraction,
                "bulgarian_fts": settings.features.enable_bulgarian_fts
            }
        }
    
    return app


def main():
    """Main entry point for running the server."""
    settings = get_settings()
    
    structured_logger.log_performance_metric("server_start", 1, "event")
    
    uvicorn.run(
        "src.mcp_server.main:create_app",
        factory=True,
        host=settings.mcp.host,
        port=settings.mcp.port,
        reload=settings.debug,
        log_config=None,  # Use our custom logging
        access_log=False,  # Disable uvicorn access logs (we handle this)
    )


if __name__ == "__main__":
    main()
