"""
Main FastAPI application for EU Funds MCP Server.
Optimized for low RAM usage and production deployment.
"""

import asyncio
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

from ..core.config import get_settings
from ..core.logging import get_logger, setup_logging
from ..core.monitoring import get_health, get_readiness, get_liveness

# Setup logging first
setup_logging()
logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting EU Funds MCP Server...")
    settings = get_settings()
    
    # Initialize components here
    logger.info(f"Environment: {settings.environment}")
    logger.info(f"Debug mode: {settings.debug}")
    
    # Test database connection
    try:
        from ..core.monitoring import health_checker
        health_status = await health_checker.get_health_status(force_refresh=True)
        if health_status["status"] == "healthy":
            logger.info("All health checks passed - server ready")
        else:
            logger.warning(f"Some health checks failed: {health_status.get('failed_checks', [])}")
    except Exception as e:
        logger.error(f"Health check failed during startup: {e}")
    
    yield
    
    # Shutdown
    logger.info("Shutting down EU Funds MCP Server...")


def create_app() -> FastAPI:
    """Create and configure FastAPI application."""
    settings = get_settings()
    
    app = FastAPI(
        title="EU Funds MCP Server",
        description="Enterprise-Grade MCP Server for EU Funding Programs in Bulgaria",
        version="0.1.0",
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
        lifespan=lifespan
    )
    
    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.mcp.cors_origins,
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE"],
        allow_headers=["*"],
    )
    
    # Health check endpoints
    @app.get("/health")
    async def health_check():
        """Comprehensive health check."""
        try:
            health_status = await get_health()
            status_code = 200 if health_status["status"] == "healthy" else 503
            return JSONResponse(content=health_status, status_code=status_code)
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return JSONResponse(
                content={
                    "status": "unhealthy",
                    "error": str(e),
                    "message": "Health check failed"
                },
                status_code=503
            )
    
    @app.get("/health/ready")
    async def readiness_check():
        """Readiness check for Kubernetes."""
        try:
            readiness_status = await get_readiness()
            status_code = 200 if readiness_status["ready"] else 503
            return JSONResponse(content=readiness_status, status_code=status_code)
        except Exception as e:
            logger.error(f"Readiness check failed: {e}")
            return JSONResponse(
                content={
                    "ready": False,
                    "error": str(e),
                    "message": "Readiness check failed"
                },
                status_code=503
            )
    
    @app.get("/health/live")
    async def liveness_check():
        """Liveness check for Kubernetes."""
        try:
            liveness_status = await get_liveness()
            return JSONResponse(content=liveness_status, status_code=200)
        except Exception as e:
            logger.error(f"Liveness check failed: {e}")
            return JSONResponse(
                content={
                    "alive": False,
                    "error": str(e),
                    "message": "Liveness check failed"
                },
                status_code=503
            )
    
    @app.get("/")
    async def root():
        """Root endpoint with basic info."""
        settings = get_settings()
        return {
            "name": "EU Funds MCP Server",
            "version": "0.1.0",
            "environment": settings.environment,
            "status": "running",
            "endpoints": {
                "health": "/health",
                "readiness": "/health/ready", 
                "liveness": "/health/live",
                "docs": "/docs" if settings.debug else "disabled",
                "mcp": "/mcp" if settings.mcp.transport == "sse" else "stdio"
            }
        }
    
    # MCP endpoint (placeholder for now)
    @app.get("/mcp")
    async def mcp_info():
        """MCP server information."""
        return {
            "protocol": "Model Context Protocol",
            "version": "1.0",
            "transport": settings.mcp.transport,
            "status": "ready",
            "capabilities": [
                "web_crawling",
                "document_storage", 
                "vector_search",
                "hybrid_search",
                "eu_funds_data"
            ]
        }
    
    # Error handlers
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        """Handle HTTP exceptions."""
        logger.warning(f"HTTP {exc.status_code}: {exc.detail}")
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": exc.detail,
                "status_code": exc.status_code,
                "path": str(request.url.path)
            }
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """Handle general exceptions."""
        logger.error(f"Unhandled exception: {exc}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={
                "error": "Internal server error",
                "message": str(exc) if settings.debug else "An unexpected error occurred",
                "path": str(request.url.path)
            }
        )
    
    return app


# Create the app instance
app = create_app()


def main():
    """Main entry point for running the server."""
    settings = get_settings()
    
    logger.info(f"Starting server on {settings.mcp.host}:{settings.mcp.port}")
    
    uvicorn.run(
        "src.mcp_server.main:app",
        host=settings.mcp.host,
        port=settings.mcp.port,
        reload=settings.debug,
        log_level=settings.monitoring.log_level.lower(),
        access_log=settings.debug,
        workers=1,  # Single worker for low RAM usage
    )


if __name__ == "__main__":
    main()
