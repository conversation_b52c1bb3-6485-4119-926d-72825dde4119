"""
РЕАЛЕН ТЕСТ НА EU FUNDS MCP SERVER
Тест с истински данни от Supabase за валидиране на RAG системата
"""

import asyncio
import sys
sys.path.append('.')

from src.core.vector_store import VectorStore
from src.core.embeddings import EmbeddingProcessor
from src.mcp.tools import EUFundsMCPTools

async def real_test():
    print('🧪 ЗАПОЧВАМ РЕАЛЕН ТЕСТ С ИСТИНСКИ ДАННИ ОТ SUPABASE')
    print('=' * 60)
    
    # 1. Проверка на данните в Supabase
    print('📊 1. ПРОВЕРКА НА ДАННИТЕ В SUPABASE:')
    vector_store = VectorStore()
    await vector_store.initialize()
    
    # Проверка колко записа имаме
    try:
        response = vector_store.supabase.table('eu_funds_content').select('id, title, content, source_url').limit(5).execute()
        if response.data:
            print(f'✅ Намерени {len(response.data)} записа в базата данни')
            for i, item in enumerate(response.data[:3], 1):
                title = item.get('title', 'Без заглавие')[:50]
                content_preview = item.get('content', '')[:100]
                print(f'   {i}. {title}... ({len(item.get("content", ""))} символа)')
                print(f'      Съдържание: {content_preview}...')
        else:
            print('❌ Няма данни в базата!')
            return
    except Exception as e:
        print(f'❌ Грешка при проверка на данните: {e}')
        return
    
    print()
    
    # 2. Тест на embedding генериране
    print('🔤 2. ТЕСТ НА EMBEDDING ГЕНЕРИРАНЕ:')
    embedding_processor = EmbeddingProcessor()
    await embedding_processor.initialize()
    
    test_query = 'европейски фондове за малки и средни предприятия'
    print(f'   Заявка: "{test_query}"')
    
    try:
        embedding_result = await embedding_processor.embed_text(test_query)
        if embedding_result.success:
            print(f'✅ Embedding генериран успешно: {len(embedding_result.vector)} измерения')
            print(f'   Модел: {embedding_result.model_name}')
        else:
            print(f'❌ Грешка при embedding: {embedding_result.error}')
            return
    except Exception as e:
        print(f'❌ Грешка при embedding: {e}')
        return
    
    print()
    
    # 3. Тест на vector search
    print('🔍 3. ТЕСТ НА VECTOR SEARCH:')
    try:
        search_results = await vector_store.vector_search(
            query_embedding=embedding_result.vector,
            limit=5,
            similarity_threshold=0.1
        )
        
        if search_results:
            print(f'✅ Намерени {len(search_results)} резултата от vector search')
            for i, result in enumerate(search_results[:3], 1):
                print(f'   {i}. {result.title[:50]}... (score: {result.relevance_score:.3f})')
                print(f'      Съдържание: {result.content[:80]}...')
        else:
            print('❌ Няма резултати от vector search')
            return
    except Exception as e:
        print(f'❌ Грешка при vector search: {e}')
        return
    
    print()
    
    # 4. Тест на MCP Tools
    print('🛠️ 4. ТЕСТ НА MCP TOOLS:')
    mcp_tools = EUFundsMCPTools()
    await mcp_tools.initialize()
    
    # Тест на search_eu_funds
    try:
        search_response = await mcp_tools.search_eu_funds(
            query=test_query,
            max_results=3
        )
        
        if search_response.get('results'):
            print(f'✅ MCP search_eu_funds: {len(search_response["results"])} резултата')
            print(f'   Време за обработка: {search_response.get("performance", {}).get("response_time_ms", "N/A")} ms')
            
            for i, result in enumerate(search_response['results'][:2], 1):
                print(f'   {i}. {result["title"][:50]}...')
                print(f'      Релевантност: {result["relevance_score"]:.3f}')
        else:
            print(f'❌ MCP search не върна резултати: {search_response.get("error", "Неизвестна грешка")}')
            return
    except Exception as e:
        print(f'❌ Грешка при MCP search: {e}')
        return
    
    print()
    
    # 5. Тест на различни заявки
    print('🎯 5. ТЕСТ НА РАЗЛИЧНИ ЗАЯВКИ:')
    test_queries = [
        'програми за иновации',
        'финансиране за стартъпи', 
        'европейски средства за образование'
    ]
    
    for query in test_queries:
        try:
            response = await mcp_tools.search_eu_funds(query=query, max_results=2)
            results_count = len(response.get('results', []))
            response_time = response.get('performance', {}).get('response_time_ms', 'N/A')
            print(f'   "{query}": {results_count} резултата ({response_time} ms)')
        except Exception as e:
            print(f'   "{query}": Грешка - {e}')
    
    print()
    
    # 6. Performance метрики
    print('📈 6. PERFORMANCE МЕТРИКИ:')
    try:
        perf_metrics = await mcp_tools.get_performance_metrics()
        
        if 'performance' in perf_metrics:
            perf = perf_metrics['performance']
            print(f'   Средно време за отговор: {perf.get("avg_response_time", 0):.3f}s')
            print(f'   Общо заявки: {perf.get("total_requests", 0)}')
            print(f'   Успешност: {perf.get("success_rate", 0):.1%}')
        
        if 'cache' in perf_metrics:
            cache = perf_metrics['cache']
            print(f'   Cache hit rate: {cache.get("hit_rate", 0):.1%}')
            print(f'   Cache размер: {cache.get("cache_size", 0)} записа')
        
        if 'system' in perf_metrics:
            system = perf_metrics['system']
            print(f'   CPU: {system.get("cpu_usage", 0):.1f}%')
            print(f'   Memory: {system.get("memory_usage_mb", 0):.1f} MB')
            
    except Exception as e:
        print(f'❌ Грешка при performance метрики: {e}')
    
    print()
    
    # 7. Генериране на въпроси от реални данни
    print('❓ 7. ГЕНЕРИРАНЕ НА ВЪПРОСИ ОТ РЕАЛНИ ДАННИ:')
    try:
        # Вземаме първите 3 записа и генерираме въпроси
        response = vector_store.supabase.table('eu_funds_content').select('title, content').limit(3).execute()
        
        if response.data:
            generated_questions = []
            for item in response.data:
                title = item.get('title', '')
                content = item.get('content', '')[:500]  # Първите 500 символа
                
                # Генерираме въпроси базирани на съдържанието
                if 'програм' in content.lower():
                    generated_questions.append(f'Какви програми се предлагат за {title.lower()}?')
                if 'финансиране' in content.lower():
                    generated_questions.append(f'Как мога да получа финансиране за {title.lower()}?')
                if 'условия' in content.lower():
                    generated_questions.append(f'Какви са условията за {title.lower()}?')
            
            print(f'✅ Генерирани {len(generated_questions)} въпроса от реални данни:')
            
            # Тестваме генерираните въпроси
            for i, question in enumerate(generated_questions[:3], 1):
                print(f'   {i}. Въпрос: "{question}"')
                try:
                    response = await mcp_tools.search_eu_funds(query=question, max_results=1)
                    if response.get('results'):
                        result = response['results'][0]
                        print(f'      ✅ Отговор намерен: {result["title"][:40]}... (score: {result["relevance_score"]:.3f})')
                    else:
                        print(f'      ❌ Няма отговор')
                except Exception as e:
                    print(f'      ❌ Грешка: {e}')
        
    except Exception as e:
        print(f'❌ Грешка при генериране на въпроси: {e}')
    
    print()
    print('🎉 РЕАЛЕН ТЕСТ ЗАВЪРШЕН!')
    print('=' * 60)
    print()
    print('📋 ЗАКЛЮЧЕНИЕ:')
    print('Този тест използва истински данни от Supabase базата данни')
    print('и проверява дали RAG системата може да отговаря на реални въпроси')
    print('с актуална информация за европейски фондове.')

if __name__ == "__main__":
    # Стартиране на теста
    asyncio.run(real_test())
