{"success": true, "test_summary": {"total_questions": 12, "passed_tests": 0, "pass_rate": 0.0, "average_score": 0.0, "duration_seconds": 10.612447, "data_count": 61, "timestamp": "2025-07-08T11:01:05.234283"}, "detailed_results": [{"question": "Какво финансиране има за малки и средни предприятия?", "category": "funding_sme", "error": "{'code': '22000', 'details': None, 'hint': None, 'message': 'different vector dimensions 1536 and 384'}", "score": 0, "passed": false}, {"question": "Какви са условията за кандидатстване по ОПИК?", "category": "opik_conditions", "error": "{'code': '22000', 'details': None, 'hint': None, 'message': 'different vector dimensions 1536 and 384'}", "score": 0, "passed": false}, {"question": "Колко е максималният размер на проекта за иновации?", "category": "innovation_funding", "error": "{'code': '22000', 'details': None, 'hint': None, 'message': 'different vector dimensions 1536 and 384'}", "score": 0, "passed": false}, {"question": "Кои програми финансират научни изследвания?", "category": "research_programs", "error": "{'code': '22000', 'details': None, 'hint': None, 'message': 'different vector dimensions 1536 and 384'}", "score": 0, "passed": false}, {"question": "Как да кандидатствам за европейско финансиране?", "category": "application_process", "error": "{'code': '22000', 'details': None, 'hint': None, 'message': 'different vector dimensions 1536 and 384'}", "score": 0, "passed": false}, {"question": "Какви са сроковете за изпълнение на проектите?", "category": "project_timelines", "error": "{'code': '22000', 'details': None, 'hint': None, 'message': 'different vector dimensions 1536 and 384'}", "score": 0, "passed": false}, {"question": "Има ли финансиране за дигитализация на МСП?", "category": "digitalization", "error": "{'code': '22000', 'details': None, 'hint': None, 'message': 'different vector dimensions 1536 and 384'}", "score": 0, "passed": false}, {"question": "Какви документи са нужни за кандидатстване?", "category": "required_documents", "error": "{'code': '22000', 'details': None, 'hint': None, 'message': 'different vector dimensions 1536 and 384'}", "score": 0, "passed": false}, {"question": "Кой може да кандидатства по програмите за регионално развитие?", "category": "regional_eligibility", "error": "{'code': '22000', 'details': None, 'hint': None, 'message': 'different vector dimensions 1536 and 384'}", "score": 0, "passed": false}, {"question": "Какъв е процентът на съфинансиране от ЕС?", "category": "cofinancing_rates", "error": "{'code': '22000', 'details': None, 'hint': None, 'message': 'different vector dimensions 1536 and 384'}", "score": 0, "passed": false}, {"question": "Какви са приоритетите на ОПРР?", "category": "oprr_priorities", "error": "{'code': '22000', 'details': None, 'hint': None, 'message': 'different vector dimensions 1536 and 384'}", "score": 0, "passed": false}, {"question": "Как се подава заявление за финансиране?", "category": "application_submission", "error": "{'code': '22000', 'details': None, 'hint': None, 'message': 'different vector dimensions 1536 and 384'}", "score": 0, "passed": false}], "performance_analysis": {"category_performance": {"funding_sme": 0.0, "opik_conditions": 0.0, "innovation_funding": 0.0, "research_programs": 0.0, "application_process": 0.0, "project_timelines": 0.0, "digitalization": 0.0, "required_documents": 0.0, "regional_eligibility": 0.0, "cofinancing_rates": 0.0, "oprr_priorities": 0.0, "application_submission": 0.0}, "difficulty_performance": {"easy": 0.0, "medium": 0.0, "hard": 0.0}, "best_category": ["funding_sme", 0.0], "worst_category": ["funding_sme", 0.0], "best_difficulty": ["easy", 0.0], "worst_difficulty": ["easy", 0.0]}, "recommendations": ["❌ Ниска производителност, нужни значителни подобрения", "Много неуспешни тестове - проверете качеството и количеството на данните", "Ниска успеваемост - разгледайте увеличаване на данните или подобряване на chunking"]}